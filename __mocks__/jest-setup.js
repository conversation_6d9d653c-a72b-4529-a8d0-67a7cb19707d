import '@testing-library/jest-dom';
import { TextEncoder, TextDecoder } from 'util'

beforeAll(() => {
  global.matchMedia = jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  }));
});

global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;