FROM nginx:latest
WORKDIR /app
USER root
RUN apt-get update && apt-get install -y libcap2-bin && rm -rf /var/lib/apt/lists/*
RUN useradd -m -d /app -s /bin/bash -g nginx spog-sim-management
COPY nginx.conf /etc/nginx/conf.d/default.conf
RUN setcap 'cap_net_bind_service=+ep' /usr/sbin/nginx
COPY manage.sh .
RUN chmod +x manage.sh
COPY build/ .
COPY .env .
RUN chown -R spog-sim-management:nginx /var/cache/nginx /var/run /var/log/nginx /usr/share/nginx/html /etc/nginx/conf.d /app
RUN touch /var/run/nginx.pid && chown spog-sim-management:nginx /var/run/nginx.pid
USER spog-sim-management
ENTRYPOINT ["./manage.sh"]
CMD ["start_service"]
