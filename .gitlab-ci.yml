stages:
  - build
  - test
  - trivy
  - release
  - deploy

variables:
  NODE_IMAGE:
    value: uk-london-1.ocir.io/lrpfi3ly7ayq/tools/node:22-alpine
    description: Set the NodeJS image version to use during build
  CLUSTER_ENVIRONMENT:
    value: spogdev
    description: Please enter spogdev or spogtest
  # set to 'true' if you want continous delivery to DEV
  AUTO_DEPLOY_MASTER: 'false'
  # set how long to wait for deployment operations to complete
  # DEPLOYMENT_TIMEOUT: 180s
  # set to 'true' if you want back deploy to OPS button
  # DEPLOY_OPS: 'false'
  # exclude lint job from pipeline when set to 'true'
  # TEST_SKIP_LINT: 'false'
  # exclude stylelint job from pipeline when set to 'true'
  # TEST_SKIP_STYLELINT: 'false'
  # exclude test job from pipeline when set to 'true'
  # TEST_SKIP_TEST: 'false'

include:
  - project: devops/build
    file: /pipelines/build-javascript-image-node-js-22.yaml
  # For information on shared pipeline with tests please read the document:
  # https://nextgenclearing.atlassian.net/wiki/spaces/DEVOPS/pages/454262803/How+to+customize+included+CI+test+stage
  - project: devops/test
    file: /pipelines/javascript-tests-node22.yaml
  - project: devops/test
    file: /pipelines/trivy-scan.yaml 
  - project: devops/tools
    file: /pipelines/tag-release-docker-image.yaml
  - project: devops/tools
    file: /pipelines/create-gitlab-release.yaml
  - project: devops/deploy
    file: /pipelines/trigger-deployment.yaml
