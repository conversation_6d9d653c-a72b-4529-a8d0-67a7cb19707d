import React from 'react';
/* create height witdth props for control size Icon */
interface ISimIcon {
  height?: number | string,
  width?: number | string,
  color?: string
}

const SimIcon = ({ height, width, color = '#5514B4' }: ISimIcon) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 36 36" fill="none">
    <rect width="40" height="40" rx="4" />
    <path
      d="M16.9001 17.6C15.0776 17.6 13.6001 19.0774 13.6001 20.9V25.1C13.6001 26.9226 15.0776 28.4 16.9001 28.4H22.3001C24.1227 28.4 25.6001 26.9226 25.6001 25.1V20.9C25.6001 19.0774 24.1227 17.6 22.3001 17.6H16.9001ZM15.4001 20.9C15.4001 20.0715 16.0717 19.4 16.9001 19.4H19.6001V22.4H15.4001V20.9ZM15.4001 24.2H19.6001V26.6H16.9001C16.0717 26.6 15.4001 25.9285 15.4001 25.1V24.2ZM21.4001 26.6V19.4H22.3001C23.1286 19.4 23.8001 20.0715 23.8001 20.9V25.1C23.8001 25.9285 23.1286 26.6 22.3001 26.6H21.4001Z"
      fill={color}
    />
    <path
      d="M13.9 8C11.7461 8 10 9.74608 10 11.9V28.1C10 30.2539 11.7461 32 13.9 32H25.3C27.4539 32 29.2 30.2539 29.2 28.1V16.7426C29.2 15.7083 28.7891 14.7163 28.0577 13.9849L23.2151 9.14228C22.4837 8.41089 21.4917 8 20.4574 8H13.9ZM11.8 11.9C11.8 10.7402 12.7402 9.8 13.9 9.8H20.4574C21.0143 9.8 21.5484 10.0212 21.9423 10.4151L26.7849 15.2577C27.1787 15.6515 27.4 16.1857 27.4 16.7426V28.1C27.4 29.2598 26.4598 30.2 25.3 30.2H13.9C12.7402 30.2 11.8 29.2598 11.8 28.1V11.9Z"
      fill={color}
    />
  </svg>
);

SimIcon.defaultProps = {
  height: 24,
  width: 24,
  color: '#5514B4',
};

export default SimIcon;
