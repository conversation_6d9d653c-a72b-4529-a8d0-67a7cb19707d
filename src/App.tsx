import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCookies } from 'react-cookie';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { ThemeProvider } from '@mui/material/styles';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';
import { AiOutlineCheckCircle, AiOutlineClose } from 'react-icons/ai';
import AppRoutes from 'AppRoutes';
import { ConfigSettingsService, CookiesService, HTTPService } from 'core/services';
import { coreAxios } from 'core/services/HTTPService';
import { TOAST_CONTAINER_ID } from 'core/utilities/toastHelper';
import AppContextProvider from 'AppContextProvider';
import { IUser, organizationTypes } from 'user.model';
import { getAccesss } from 'features/SimManagement/api.service';
import { initializeSocket } from 'features/SimManagement/SimManagementClient/Socket/socket';
import { GlobalStyles } from '@mui/material';
import './App.scss';

interface IAppProps {
  user: IUser | undefined,
  isLoading: boolean
}

const defaultThemeName = 'bt';

const App = ({ user, isLoading }: IAppProps) => {
  const navigate = useNavigate();
  const [cookies] = useCookies();
  const [state, setState] = useState({
    themeName: defaultThemeName,
    loadAxiosUrl: false,
  });
  const { themeName } = state;
  const currentTheme = themeConfig[state.themeName];
  const [access, setAccess] = useState([]);
  const isDevelopmentMode = process.env.NODE_ENV === 'development';
  const getProdLogOutUrl = (keycloakLogOutUrl: string) => {
    const keycloakLogoutUrlFull = `${keycloakLogOutUrl}?redirect_uri=${window.location.origin}`;

    return `/oauth2/sign_out?rd=${encodeURIComponent(keycloakLogoutUrlFull)}`;
  };

  const logOut = async (url: string) => {
    window.location.replace(url);
  };

  const getLogoutUrl = (url: string) => (isDevelopmentMode
    ? process.env.REACT_APP_LOGOUT_URL
    : getProdLogOutUrl(url));

  const redirectToLogin = () => {
    const currentPathname = window.location.href;
    const entryPointUrl = `${process.env.REACT_APP_LOGIN_URL}?entryPath=${currentPathname}`;

    navigate(entryPointUrl);
  };

  const authenticateForDevelopmentMode = () => {
    const cookieDoesntHaveAccessToken = !CookiesService.getAccessToken(cookies).value;

    if (cookieDoesntHaveAccessToken) {
      redirectToLogin();
    }

    HTTPService.setAccessToken(coreAxios, cookies);
  };

  const setupInitialData = async () => {
    const { data } = await ConfigSettingsService.getAppVariables();
    sessionStorage.setItem('AppData', JSON.stringify(data));
    const logoutUrl = getLogoutUrl(data?.themeName === 'bt' ? data?.coreUiUrl : data.keycloakLogoutUrl);

    if (isDevelopmentMode) {
      authenticateForDevelopmentMode();
    }

    HTTPService.setDefaultGlobalConfig(coreAxios, data.apiUrl);

    HTTPService.setCorsError(coreAxios, logOut, logoutUrl);

    const userAcess = await getAccesss();
    if (userAcess && userAcess?.data) {
      setAccess(userAcess?.data?.result);
    }
    setState({
      ...state,
      themeName: data.themeName,
      loadAxiosUrl: true,
    });
  };

  useEffect(() => {
    setupInitialData();
    document.body.style.fontFamily = theme?.typography?.fontFamily?.[0] || ' ';
  }, []);

  useEffect(() => {
    // Initialize socket only for CLIENT users
    if (user && user.organization.type === organizationTypes.CLIENT) {
      initializeSocket();
    }
  }, [user]);

  return (
    <ThemeProvider theme={theme(currentTheme)}>
      {/* This GlobalStyles is added to remove browser autofill box shadow */}
      <GlobalStyles
        styles={{
          "input:-webkit-autofill": {
            boxShadow: "0 0 0 1000px #ffffff00 inset !important",
            WebkitTextFillColor: "#000 !important",
            borderRadius: "4px !important",
            transition: "background-color 5000s ease-in-out 0s !important",
          },
        }}
      />
      <AppContextProvider value={{ themeName, currentTheme, access }}>
        <ToastContainer
          enableMultiContainer
          containerId={TOAST_CONTAINER_ID}
          position="bottom-right"
          autoClose={5000}
          hideProgressBar
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          closeButton={() => (
            <div className="toastify-close-button">
              <AiOutlineClose />
            </div>
          )}
          icon={<AiOutlineCheckCircle />}
        />
        <div data-testid="app" className="sim-management-app">
          {state.loadAxiosUrl && (
            <AppRoutes user={user} isLoading={isLoading} />
          )}
        </div>
      </AppContextProvider>
    </ThemeProvider>
  );
};

export default App;
