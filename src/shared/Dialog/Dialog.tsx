import React, { FormE<PERSON>Handler } from 'react';
import {
  Dialog as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTitle, DialogContent,
  Box, IconButton, DialogActions, Typography, DialogProps,
} from '@mui/material';
import CloseIcon from 'assets/images/CloseIcon';

interface IDialog extends DialogProps {

    open:boolean,
    title:string,
    subTitle?:string,
    onClose:()=>void,
     
    onSubmitEvent?: FormEventHandler | undefined;
    children?:React.ReactNode,
    footerchildren?:React.ReactNode,
    formClass?:string,
}
const Dialog = ({
  open, title, subTitle,
  onClose, children, footerchildren, onSubmitEvent, formClass, ...props
}:IDialog,
) => {
  const oncloseModal = (event, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) { return; }
    onClose();
  };
  return (
    <MuiDialog
      open={open}
      onClose={oncloseModal}
      aria-labelledby="edit-apartment"
      {...props}
    >
      <form
        onSubmit={(e) => {
          if (onSubmitEvent) {
            onSubmitEvent(e);
          }
        }}
        className={formClass}
        style={{ display: 'contents' }}
      >
        {title && (
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          sx={{
            borderTop: '6px solid #5514b4',
            '& .MuiDialogTitle-root': {
              padding: '26px 32px !important',
            },
          }}
        >
          <DialogTitle
            id="display-dialog"
            sx={{ flex: 'unset !important' }}
          >
            <Typography variant="h3">{title}</Typography>
            <Typography component="div" variant="subtitle1">{subTitle}</Typography>
          </DialogTitle>
          <IconButton
            sx={{
              marginRight: '32px',
            }}
            onClick={() => onClose()}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        )}
        <DialogContent sx={{ padding: '0px 32px' }} style={{ overflow: 'hidden' }}>
          {children}
        </DialogContent>
        <DialogActions sx={{ padding: '0px 32px 32px', justifyContent: 'flex-start' }}>
          {footerchildren}
        </DialogActions>
      </form>
    </MuiDialog>
  );
};
export default Dialog;

Dialog.defaultProps = {
  footerchildren: '',
  children: '',
  onSubmitEvent: () => null,
  formClass: '',
  subTitle: '',
};
