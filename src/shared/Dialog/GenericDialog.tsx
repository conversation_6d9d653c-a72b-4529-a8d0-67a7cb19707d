import {
  Box, Button,
  Typography,
} from '@mui/material';
import React from 'react';
import Dialog from 'shared/Dialog/Dialog';
import Loader from 'shared/Loader';

interface IDialogConfirm {
  loading?: boolean,
  open: boolean,
  onSuccess: () => void,
  onClose: () => void,
  displayFirstButton?: boolean,
  displaySecondButton?: boolean,
  firstButtonText?: string,
  SecondButtonText?: string,
  children: React.ReactNode,
  title: string
}

const GenericDialog = ({
  open, onSuccess, onClose, displayFirstButton, displaySecondButton, firstButtonText, SecondButtonText, children, title, loading
}: IDialogConfirm) => (

  <Dialog
    title={title}
    onClose={() => onClose()}
    open={open}
    sx={{
      '& .MuiDialogTitle-root': {
        p: 8,
      },

    }}
    footerchildren={(
      <Box
        display="flex"
        alignItems="flex-start"
        gap="15px"
        justifyContent="space-between"
        sx={{ opacity: loading ? 0.5 : 1 }}
      >
        {displayFirstButton && (
          <Button
            sx={{ p: '0px 25px', minWidth: '110px' }}
            variant="contained"
            color="primary"
            onClick={() => onSuccess()}
          >
            {loading ?
              <Box sx={{
                '.MuiBox-root': {
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                },
              }}>
                <Loader size={22} />
              </Box> :
              firstButtonText
            }
          </Button>
        )}

        {displaySecondButton && (
          <Button
            sx={{ p: '0px 25px', backgroundColor: '#ebe3f6', border: '0px' }}
            variant="outlined"
            onClick={() => onClose()}
          >
            {SecondButtonText}
          </Button>
        )}

      </Box>
    )}
  >
    <Box>
      <Typography
        variant="body1"
        component="p"
        sx={{ paddingBottom: '32px' }}
      >
        {children}
      </Typography>
    </Box>

  </Dialog>

);

GenericDialog.defaultProps = {
  displayFirstButton: true,
  displaySecondButton: true,
  loading: false,
  firstButtonText: 'Confirm',
  SecondButtonText: 'Cancel',
};

export default GenericDialog;
