import {
  Checkbox,
  TableHead, TableSortLabel,
} from '@mui/material';
import React from 'react';
import TableRows from 'shared/Container/TableRows';
import TableCells from './TableCells';
import { ITableHeader } from './Header.module';

const TableHeader = ({
  headerData, checkBoxSelection, onRequestSort, onSelectAllClick, order, orderBy,
}:ITableHeader) => {
  const createSortHandler = (property) => (event) => {
    if (onRequestSort) { onRequestSort(event, property); }
  };

  const onSelectAllHandler = (event) => {
    if (onSelectAllClick) { onSelectAllClick(event); }
  };

  return (

    <TableHead sx={{

      '& .MuiTableCell-root': {
        positon: 'sticky !important',
      },
    }}
    >
      <TableRows key="table-header-row">
        {checkBoxSelection && (
        <TableCells width={50}>
          <Checkbox
            color="primary"
            onChange={onSelectAllHandler}
            name="all"
            inputProps={{
              'aria-label': 'select all desserts',
            }}
          />
        </TableCells>
        )}

        {headerData.map((headerItem) => {
          const {
            name, width, align, sticky, sort,
          } = headerItem;
          let orderDirection;
          if (orderBy === name) {
            orderDirection = order;
          } else {
            orderDirection = 'asc';
          }
          return (
            <TableCells
              key={name}
              width={width}
              align={align}
              sticky={!!sticky}
              bold
              th
            >
              {align === 'right' ? (
                <span>
                  {sort && (
                  <TableSortLabel
                    active={orderBy === name}
                    direction={orderDirection}
                    onClick={createSortHandler(name)}
                  />
                  )}
                  {name}

                </span>
              ) : (
                <span>
                  {name}
                  {sort && (
                  <TableSortLabel
                    active={orderBy === name}
                    direction={orderDirection}
                    onClick={createSortHandler(name)}
                  />
                  )}
                </span>
              ) }

            </TableCells>
          );
        })}
      </TableRows>
    </TableHead>
  );
};

TableHeader.defaultProps = {
  checkBoxSelection: false,
  onRequestSort: (() => null),
  onSelectAllClick: (() => null),
  orderBy: 'id',
  order: 'asc',

};
export default TableHeader;
