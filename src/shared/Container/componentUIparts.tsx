import {
  TableRow, styled, Table, TableCell,
} from '@mui/material';

import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import getCurrentThemeColors from 'core/utilities/getCurrentThemeColors';

const StyleTable = styled(Table)({
  tableLayout: 'auto',
  '& .MuiTableRow-head': {
    borderBottom: '1px solid #E7E7EE',
  },
  border: '1px solid #E7E7EE',

});

const StyleTableRow = styled(TableRow)(({ theme }) => ({
  '.MuiTableCell-root': {
    background: theme.palette.background.default,
  },
  '&:nth-child(even) .MuiTableCell-root': {
    background: styles.lightColor100,
  },
  '.MuiTableCell-head': {
    backgroundColor: `${styles.lightColor200}!important`,
  },

  '&:hover .MuiTableCell-root': {
    background: getCurrentThemeColors(theme.palette.primary.main)[100],
  },
  '&.MuiTableCell-paddingCheckbox': {
    paddingLeft: '15px !important',
  },
}));
interface IStyledTableCell {
  bold?: boolean;
  align?: 'left' | 'right' | 'center';
  sticky?: boolean;
  th?: boolean;
}
const StyledTableCell = styled(TableCell)<IStyledTableCell>(({
  align, sticky, bold, th,
}) => ({
  fontFamily: 'BT Curve, sans-serif',
  fontSize: 14,
  fontWeight: bold ? 700 : 400,
  textAlign: align,
  color: styles.darkColor500,
  padding: '7px 16px',
  height: 54,
  position: sticky ? 'sticky' : 'static',
  zIndex: 1,
  left: 0,
  overflow: 'hidden',
  border: th ? `5px solid ${styles.lightColor400}` : 'none',
  borderWidth: th ? '1px 0 !important' : 0,
  whiteSpace: 'nowrap',
  filter: sticky ? 'drop-shadow(8px 0px 8px rgba(140, 140, 140, 0.1))' : 'none',
}));

export {
  StyleTable, StyleTableRow, StyledTableCell,
};
