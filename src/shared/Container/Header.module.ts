import React from 'react';

export interface IHeader {
    name: string;
    width: number;
    align?: 'left' | 'right' | 'center';
    sticky?: boolean;
    valuePath?: string;
    sort?:boolean
}
export interface ITableHeader{
    headerData: IHeader[],
    checkBoxSelection?: boolean,
    onRequestSort?: (event: React.MouseEvent<unknown>, property: string) => void;
    onSelectAllClick?:
    ((event: React.ChangeEvent<HTMLInputElement>, name?: string) => void) | undefined,
    order?: 'asc' | 'desc'
    orderBy?:string,
  }
