import React, { ReactElement, ReactNode } from 'react';
import { TableCellProps } from '@mui/material';
import { StyledTableCell } from './componentUIparts';

interface ITableCells extends TableCellProps{
  children?: ReactElement| ReactNode | ReactElement[];
  width?: number
  align?: 'left' | 'center' | 'right';
  sticky?: boolean
  bold?: boolean
  th?: boolean
  onClick?: () => void;
}

const TableCells = ({
  onClick, children, width = 100, align = 'left', sticky = false, bold = false, th = false,
}:ITableCells) => (
  <StyledTableCell
    style={{ minWidth: width }}
    align={align}
    sticky={sticky}
    bold={bold}
    th={th}
    onClick={onClick}
  >
    {children}
  </StyledTableCell>
);

TableCells.defaultProps = {
  align: 'left',
  width: 100,
  sticky: false,
  bold: false,
  th: false,
  children: null,
  onClick: null,
};

export default TableCells;
