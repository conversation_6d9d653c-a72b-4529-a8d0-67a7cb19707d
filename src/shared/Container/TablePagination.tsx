import {
  Box, MenuItem, Pagination, Select, Typography, styled,
} from '@mui/material';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import React from 'react';

const StyledBox = styled(Box)(({ theme }) => ({

  '& .MuiBox-root,& .MuiTypography-body1, & .MuiPaginationItem-root ': {
    color: styles.darkColor500,
    fontFamily: 'BT Curve, sans-serif',
    fontStyle: 'normal',
    fontWeight: 400,
    fontSize: '14px',
    lineHeight: '17px',
  },
  '& .Mui-selected': {
    backgroundColor: '#F5F1FA !important',
    color: `${theme.palette.primary.main}!important`,
    fontWeight: 700,
  },

}));
interface ITablePagination{
    display:boolean,
    lastPage?:number,
    totalCount?:number
}

const TablePagination = ({
  display, lastPage, totalCount,
}:ITablePagination) => (
  <Box>
    {display && (
      <StyledBox display="flex" alignItems="center" justifyContent="space-between" padding={5}>

        <Box display="flex" gap={2} alignItems="center" sx={{ m: 1, minWidth: 30 }}>
          <Box>Rows Per Page</Box>
          <Select
            value={50}
   //  onChange={handleChange}
     // displayEmpty
            inputProps={{ 'aria-label': 'Without label' }}
          >

            <MenuItem value={50}>50</MenuItem>
            <MenuItem value={75}>75</MenuItem>
            <MenuItem value={100}>100</MenuItem>

          </Select>
        </Box>
        <Typography variant="body1">
          1-50 of
          {totalCount}
        </Typography>
        <Pagination
          size="large"
          count={lastPage || 0}
          showFirstButton
          showLastButton
          shape="rounded"
        />
      </StyledBox>
    )}
  </Box>
);

TablePagination.defaultProps = {
  lastPage: 0,
  totalCount: 0,
};
export default TablePagination;
