import React from 'react';
import { Tooltip, Typography } from '@mui/material';
import './TruncatedList.scss';

const TruncatedList = ({
  list,
  maxVisible = 2,
  delimiter = ', ',
  tooltipStyle = {},
}) => {
  if (!list || list.length === 0) return null;

  const visibleItems = list.slice(0, maxVisible);
  const remainingCount = list.length - maxVisible;
  const visibleList = visibleItems.join(delimiter);

  return (
    <div className="truncated-list-container">
      <Typography fontSize="13px">{visibleList}</Typography>
      {remainingCount > 0 && (
        <Tooltip
          title={list.map((x) => (
            <div>{x}</div>
          ))}
          arrow
          placement="top"
          componentsProps={{
            tooltip: {
              sx: {
                maxWidth: '220px',
                wordBreak: 'break-word',
                whiteSpace: 'normal',
                fontSize: '12px',
                padding: '8px 12px',
                lineHeight: 1.4,
                ...tooltipStyle,
              },
            },
          }}
        >
          <span className="truncated-list-more">
            +
            {remainingCount}
          </span>
        </Tooltip>
      )}
    </div>
  );
};

export default TruncatedList;
