import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import TruncatedList from './TruncatedList';

const theme = createTheme();

const renderWithTheme = (ui) => render(
  <ThemeProvider theme={theme}>
    {ui}
  </ThemeProvider>,
);

describe('TruncatedList Component', () => {
  test('renders nothing when list is empty', () => {
    renderWithTheme(<TruncatedList list={[]} />);
    const container = document.querySelector('.truncated-list-container');
    expect(container).not.toBeInTheDocument();
  });

  test('renders nothing when list is null', () => {
    renderWithTheme(<TruncatedList list={null} />);
    const container = document.querySelector('.truncated-list-container');
    expect(container).not.toBeInTheDocument();
  });

  test('renders a single item without truncation', () => {
    const singleItem = ['Item 1'];
    renderWithTheme(<TruncatedList list={singleItem} />);
    const container = document.querySelector('.truncated-list-container');
    expect(container).toBeInTheDocument();
    expect(screen.getByText('Item 1')).toBeInTheDocument();

    const moreIndicator = document.querySelector('.truncated-list-more');
    expect(moreIndicator).not.toBeInTheDocument();
  });

  test('renders two items without truncation when maxVisible is 2', () => {
    const twoItems = ['Item 1', 'Item 2'];
    renderWithTheme(<TruncatedList list={twoItems} maxVisible={2} />);
    expect(screen.getByText('Item 1, Item 2')).toBeInTheDocument();

    const moreIndicator = document.querySelector('.truncated-list-more');
    expect(moreIndicator).not.toBeInTheDocument();
  });

  test('truncates list and shows more indicator when items exceed maxVisible', () => {
    const manyItems = ['Item 1', 'Item 2', 'Item 3', 'Item 4', 'Item 5'];
    renderWithTheme(<TruncatedList list={manyItems} maxVisible={2} />);
    expect(screen.getByText('Item 1, Item 2')).toBeInTheDocument();

    const moreIndicator = document.querySelector('.truncated-list-more');
    expect(moreIndicator).toBeInTheDocument();
    expect(moreIndicator).toHaveTextContent('+3');
  });

  test('uses custom delimiter when provided', () => {
    const twoItems = ['Item 1', 'Item 2'];
    renderWithTheme(<TruncatedList list={twoItems} delimiter=" | " />);
    expect(screen.getByText('Item 1 | Item 2')).toBeInTheDocument();
  });

  test('respects maxVisible prop', () => {
    const manyItems = ['Item 1', 'Item 2', 'Item 3', 'Item 4', 'Item 5'];
    renderWithTheme(<TruncatedList list={manyItems} maxVisible={3} />);
    expect(screen.getByText('Item 1, Item 2, Item 3')).toBeInTheDocument();

    const moreIndicator = document.querySelector('.truncated-list-more');
    expect(moreIndicator).toBeInTheDocument();
    expect(moreIndicator).toHaveTextContent('+2');
  });
});
