import React, { FC, useEffect, useState } from 'react';

import './FadeIn.scss';

 
const FadeIn: FC<any> = ({ children }) => {
  const [visible, setVisible] = useState<boolean>(false);

  useEffect(() => {
    setVisible(true);

    return () => {
      setVisible(false);
    };
  }, []);

  return (
    <div className={`fadein-wrapper ${visible ? 'visible' : ''}`}>
      {children}
    </div>
  );
};

export default FadeIn;
