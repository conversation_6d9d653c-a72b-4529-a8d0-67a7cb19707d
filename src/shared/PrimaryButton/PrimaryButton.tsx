import { Button, styled } from '@mui/material';
import React, { FC, ReactNode } from 'react';
import getButtonColors from 'core/utilities/getButtonColors';

interface IPrimaryButtonProps {
  onClick?: () => void;
  children?: ReactNode;
  disabled?: boolean
  btnSize?: 'large' | 'medium';
  btnStyle?: 'primary' | 'secondary' | 'light' | 'superLight';
  className?: string;
  onMouseDown?: (e: any) => any;
  sx?: any
}

const PrimaryButton: FC<IPrimaryButtonProps> = ({ children, ...props }) => {
  const StyledButton = styled(Button)<IPrimaryButtonProps>(({ theme, btnSize, btnStyle }) => {
    const { bg, color } = getButtonColors(theme, btnStyle || 'primary');

    return {
      textTransform: 'none',
      fontSize: btnSize === 'medium' ? 12 : 14,
      padding: btnSize === 'medium' ? '5px 16px' : '8px 24px',
      minHeight: btnSize === 'medium' ? 31 : 40,
      background: bg.default,
      color: `${color.default}!important`,
      width: 'auto !important',
      '.MuiBox-root': {
        display: 'flex',
        alignItems: 'center',
      },

      '.MuiBox-root, .primary-btn__icon': {
        marginRight: 7,
      },

      '.primary-btn__icon': {
        '& *': {
          fill: 'none!important',
          stroke: `${color.default}!important`,
        },
      },

      '&:hover': {
        background: bg.hover,
        color: `${color.hover}!important`,
        '& .primary-btn__icon *': {
          stroke: `${color.hover}!important`,
        },
      },

      '&:disabled': {
        background: bg.disabled,
        color: `${color.disabled}!important`,
        '& .primary-btn__icon *': {
          stroke: `${color.disabled}!important`,
        },
      },
    };
  });

  return <StyledButton {...props}>{children}</StyledButton>;
};

PrimaryButton.defaultProps = {
  onClick: () => undefined,
  onMouseDown: () => undefined,
  children: '',
  btnSize: 'medium',
  disabled: false,
  btnStyle: 'primary',
  className: '',
  sx: {},
};

export default PrimaryButton;
