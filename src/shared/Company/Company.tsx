import React, { FC, useState } from 'react';
import { styled } from '@mui/material';
import getCurrentThemeColors from 'core/utilities/getCurrentThemeColors';

import './Company.scss';

interface ICompanyProps {
    logo: string;
    name: string;
}

const Placeholder = styled('div')(({ theme }) => ({
  background: getCurrentThemeColors(theme.palette.primary.main)[100],
  borderRadius: theme.shape.borderRadius,
}));

const Company: FC<ICompanyProps> = ({ logo, name }) => {
  const [loadFailed, setLoadFailed] = useState<boolean | undefined>(undefined);

  const onErrorHandler = (): void => setLoadFailed(true);

  return (
    <div className="billing-table__company" data-testid="company-img-wrapper">
      {loadFailed !== undefined && (logo && !loadFailed)
        ? <img className="billing-table__company-logo" src={logo} alt={name} onError={onErrorHandler} data-testid="company-img" />
        : <Placeholder className="billing-table__company-logo placeholder" data-testid="company-placeholder" />}
      <span data-testid="company-name">{name}</span>
    </div>
  );
};

export default Company;
