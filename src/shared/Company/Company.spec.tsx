import React from 'react';
import { screen } from '@testing-library/react';
import testRender from 'core/utilities/testUtils';
import Company from './Company';

describe('Company', () => {
  test('should Company render correct', () => {
    testRender(<Company name="Company with logo" logo="https://example.com" />);

    expect(screen.getByTestId('company-img-wrapper')).toBeInTheDocument();
  });

  test('should Company with no logo', () => {
    testRender(<Company name="Company with no logo" logo="" />);

    expect(screen.getByTestId('company-placeholder')).toBeInTheDocument();
  });

  test('should Company with logo ', () => {
    testRender(<Company name="Company with no logo" logo="" />);

    expect(screen.getByTestId('company-name')).toHaveTextContent('Company with no logo');
  });
});
