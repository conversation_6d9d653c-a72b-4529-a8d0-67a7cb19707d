import React, { FC, ReactElement } from 'react';
import { useNavigate } from 'react-router-dom';
import { AiOutlineLeft } from 'react-icons/ai';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import './TopBar.scss';

interface ITopBarProps {
  children: ReactElement | ReactElement[];
  className?: string;
  navigateTo?: any;
}

const TopBar: FC <ITopBarProps> = ({ children, className, navigateTo = -1 }) => {
  const navigate = useNavigate();

  const navigateBack = () => {
    const baseUrl = window.location.origin;
    if (navigateTo === baseUrl) {
      window.location.href = navigateTo;
    } else {
      navigate(navigateTo);
    }
  };

  return (
    <div className={`sim-management-top-bar ${className}`} data-testid="top-bar">
      <button
        type="button"
        onClick={navigateBack}
        className="sim-management-top-bar__back"
      >
        <AiOutlineLeft fontSize={24} color={styles.darkColor300} />
      </button>
      {children}
    </div>
  );
};

TopBar.defaultProps = {
  className: '',
  navigateTo: -1,
};

export default TopBar;
