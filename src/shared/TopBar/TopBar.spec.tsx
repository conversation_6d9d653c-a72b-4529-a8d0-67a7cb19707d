import React from 'react';
import { screen } from '@testing-library/react';
import testRender from 'core/utilities/testUtils';
import { Button } from '@mui/material';
import TopBar from './TopBar';

describe('TopBar', () => {
  test('should render with Download button', () => {
    testRender(<TopBar><Button>Download</Button></TopBar>);
    expect(screen.getByText('Download')).toBeInTheDocument();
  });

  test('should render with Heading text', () => {
    testRender(<TopBar><h1>Heading</h1></TopBar>);
    expect(screen.getByText('Heading')).toBeInTheDocument();
  });
});
