import useAuthorization from 'hooks/useAuthorization';
import React, { FC, ReactElement } from 'react';
import Forbidden from '@nv2/nv2-pkg-js-shared-components/lib/Forbidden';
import getCurrentThemeColors from 'core/utilities/getCurrentThemeColors';

interface IAuthWrapper {
  children: ReactElement | ReactElement[],
  permission: string[],
  repository: string[],
  isComponent?: boolean,
}

const AuthWrapper: FC<IAuthWrapper> = (
  {
    children, permission, repository, isComponent,
  }):any => {
  if ((permission && permission?.length !== 0) && (repository && repository?.length !== 0)) {
    const isAuthorise = useAuthorization(permission, repository);
    if (!isAuthorise && isComponent) {
      return <Forbidden getCurrentThemeColors={getCurrentThemeColors} />;
    }
    if (isAuthorise) {
      return children;
    }
    return false;
  }
  return children;
};

AuthWrapper.defaultProps = {
  isComponent: false,
};
export default AuthWrapper;
