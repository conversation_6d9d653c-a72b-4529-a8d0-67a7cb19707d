import React from 'react';
import { screen } from '@testing-library/react';
import permissions from 'hooks/permissions';
import testRender from 'core/utilities/testUtils';
import AuthWrapper from '.';

const TesComponent = () => <div>Child Component</div>;
// Mock the AppContextProvider module
jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    access: permissions.result,
  }),
}));
describe('AuthWrapper', () => {
  it('renders children when permission is not specified', () => {
    testRender(<AuthWrapper permission={[]} repository={[]}><TesComponent /></AuthWrapper>);
    expect(screen.getByText('Child Component')).toBeInTheDocument();
  });

  it('does not render children when permission is specified, and isAuthorise is false', () => {
    const permission = 'invalidPermission';
    const repository = ['AccountManagement', 'MarketShareReport'];
    testRender(
      <AuthWrapper permission={[permission]} repository={repository}>
        <TesComponent />
      </AuthWrapper>,
    );
    expect(screen.queryByText('Child Component')).toBeNull();
  });

  it('renders children when permission is empty', () => {
    testRender(
      <AuthWrapper permission={[]} repository={['AccountManagement']}><TesComponent /></AuthWrapper>,
    );
    expect(screen.getByText('Child Component')).toBeInTheDocument();
  });

  it('renders children when repository is empty', () => {
    testRender(
      <AuthWrapper permission={['Create Account']} repository={[]}><TesComponent /></AuthWrapper>,
    );
    expect(screen.getByText('Child Component')).toBeInTheDocument();
  });

  it('matches snapshot', () => {
    const { asFragment } = testRender(
      <AuthWrapper permission={['Create Account']} repository={['Accounts', 'Reporting']}>
        <TesComponent />
      </AuthWrapper>,
    );
    expect(asFragment()).toMatchSnapshot();
  });
});
