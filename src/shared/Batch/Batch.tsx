import React from 'react';
import { Box } from '@mui/material';

interface BatchProps {
  label: string;
  sx?: any;
}

const Batch: React.FC<BatchProps> = ({ label, sx }) => {
  let backgroundColor: string;

  switch (label) {
    case 'Usage Monitoring':
      backgroundColor = '#A56DD7';
      break;
    case 'Default':
      backgroundColor = '#00616D';
      break;
    case 'SIM Provisioning':
      backgroundColor = '#4EB6E1';
      break;
    case 'Network and System':
      backgroundColor = '#F18F93';
      break;
    case 'Security':
      backgroundColor = '#6EC9A7';
      break;
    case 'Subscription Management':
      backgroundColor = '#6F37BF';
      break;
    case 'Frequently Used':
      backgroundColor = '#707';
      break;
    case 'N/A':
      backgroundColor = '#BCBDC';
      break;
    case 'initial':
    case undefined:
      backgroundColor = 'transparent';
      break;
    default:
      backgroundColor = 'gray';
  }

  return (
    label ? (
      <Box
        className="customBatch"
        sx={{
          backgroundColor,
          width: 'fit-content',
          padding: '5px 10px',
          borderRadius: '5px',
          color: 'white',
          marginBottom: '10px',
          fontSize: '12px',
          ...sx,
        }}
      >
        {label}
      </Box>
    ) : <span />
  );
};

Batch.defaultProps = {
  sx: {},
};

export default Batch;
