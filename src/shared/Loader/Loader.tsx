import React from 'react';
import { Box, CircularProgress } from '@mui/material';
import getCurrentThemeColors from 'core/utilities/getCurrentThemeColors';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

interface ILoaderProps {
  size?: number;
  staticColor?: string;
}

const Loader = ({ size = 25, staticColor, ...props }: ILoaderProps) => (
  <Box sx={{ position: 'relative', margin: 'auto' }} className="common-loader">
    <CircularProgress
      data-testid="loader"
      variant="determinate"
      sx={{
        color: staticColor,
      }}
      size={size}
      thickness={4}
      {...props}
      value={100}
    />
    <CircularProgress
      variant="indeterminate"
      disableShrink
      sx={{
        color: (theme) => getCurrentThemeColors(theme.palette.primary.main)[500],
        animationDuration: '550ms',
        position: 'absolute',
        left: 0,
      }}
      size={size}
      thickness={4}
      {...props}
    />
  </Box>
);

Loader.defaultProps = {
  size: 25,
  staticColor: styles.lightColor50,
};

export default Loader;
