import { TimelineDot } from '@mui/lab';
import {
  Box, Typography, useTheme,
} from '@mui/material';
import { statusColorMap } from 'core/utilities/constants';

import React from 'react';

interface IStatusText {
  message: string | number
  status: 'error' | 'warning' | 'active' | 'unknown' | 'deactivated' | 'pending' | 'ready for activation' | 'totalothers' | 'totalactive' | 'Updated' | 'Partially Completed' | string;
  isBold?: boolean;
  colorFun?: Function;
}

export default function StatusText({ status = 'unknown', message, isBold = false, colorFun = statusColorMap }: IStatusText) {
  const bold = {
    color: 'rgba(0, 0, 0, 0.87)',
    fontFamily: '"Open Sans",sans-serif',
    fontWeight: 700,
    fontSize: '14px',
    lineHeight: 1.43,
    whiteSpace: 'nowrap',
  };
  const statusLabel = status.toLocaleLowerCase();
  const theme = useTheme();
  const statusColor = colorFun(theme);
  return (
    <Box
      sx={{
        '& .MuiTimelineDot-filled': {
          margin: '8px 0px',
          boxShadow: 'unset',
          padding: '2px',
        },
        marginLeft: '8px',
      }}
      display="flex"
      flexDirection="row"
      alignItems="center"
      gap={2}
    >
      <TimelineDot
        sx={{
          backgroundColor: statusColor[statusLabel],
        }}
      />
      {(statusLabel === 'totalothers' || statusLabel === 'totalactive' || isBold) && (
        <Typography variant="h3" sx={bold}>{message}</Typography>)}

      {!(statusLabel === 'totalothers' || statusLabel === 'totalactive' || isBold) && (
        <Typography variant="body2" sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>{message}</Typography>)}
    </Box>
  );
}
