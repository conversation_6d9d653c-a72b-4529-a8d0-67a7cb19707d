import React, { ChangeEvent, FC, useMemo } from 'react';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { PickersDay, PickersDayProps } from '@mui/x-date-pickers/PickersDay';
import { styled, TextField } from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { GrCalendar } from 'react-icons/gr';

const SHORT_DATE_FORMAT = 'MM-YYYY';

interface ICustomDatePickerProps {
   
  date: any;
   
  setDate?: (date: any) => void;
   
  onChange?: (date: any) => void;
  disabled?: boolean;
  label?: string;
  disabledInput?: boolean;
  disableFuture?: boolean;
  format?: string;
  readOnly?: boolean;
  width?: number | string;
  name?: string;
  maxDate?: string | Date;
}

const OpenPickerIcon = () => <GrCalendar />;

const StyledPickersDay = styled(PickersDay)({ fontSize: 13 });

const renderPickerDay = (
  dayDate: unknown,
  selectedDates: Array<unknown | null>,
  pickersDayProps: PickersDayProps<unknown>,
   
): JSX.Element => <StyledPickersDay {...pickersDayProps} />;

const CustomDatePicker: FC<ICustomDatePickerProps> = ({
  date,
  setDate,
  disabled,
  label,
  disabledInput,
  disableFuture,
  format,
  width,
  onChange,
  maxDate,
  readOnly,
  ...rest
}) => {
  const StyledDatePicker = styled(DatePicker)({
    '.MuiInputBase-root': {
      width: width || '100%',
      padding: 0,
    },

    '.MuiInputAdornment-root': {
      left: -7,
    },

    '.MuiSvgIcon-root': {
      width: 26,
      height: 26,
    },

    '.MuiInputBase-input': {
      paddingLeft: 5,
    },
  });

  const inputChange = (e: ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    return false;
  };

  const disabledInputParams = disabledInput
    ? {
      onChange: inputChange,
      onKeyDown: inputChange,
      onPaste: inputChange,
    }
    : {};

  const memorizedDatePicker = useMemo(
    () => (
      <StyledDatePicker
        disabled={!!disabled}
        views={
          format !== SHORT_DATE_FORMAT
            ? ['year', 'month', 'day']
            : ['year', 'month']
        }
        label={label}
        value={date}
        onChange={onChange || (() => true)}
        inputFormat={format || SHORT_DATE_FORMAT}
        openTo={format !== SHORT_DATE_FORMAT ? 'day' : 'month'}
        maxDate={maxDate}
         
        renderInput={(param: any) => (
          <TextField
            {...param}
            ico
            helperText={null}
            {...disabledInputParams}
          />
        )}
        closeOnSelect={false}
        onMonthChange={(newDate: unknown) => setDate?.(newDate)}
        components={{ OpenPickerIcon }}
        readOnly={readOnly || false}
        disableFuture={!!disableFuture}
        renderDay={renderPickerDay}
        {...rest}
      />
    ),
    [date, disabled],
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      {memorizedDatePicker}
    </LocalizationProvider>
  );
};

CustomDatePicker.defaultProps = {
  disabled: false,
  label: '',
  disabledInput: false,
  disableFuture: false,
  format: SHORT_DATE_FORMAT,
  name: '',
  onChange: () => true,
  readOnly: false,
  setDate: () => true,
  width: '100%',
  maxDate: '',
};

export default CustomDatePicker;
