import { Box, Typography } from '@mui/material';
import React, { ReactNode } from 'react';

interface NoDataFoundProps {
  icon: ReactNode;
  text: string;
  description: string;
}

const NoDataFound: React.FC<NoDataFoundProps> = ({ icon, text, description }) => (
  <Box sx={{
      display: 'flex',
      justifyContent: 'center',
      fontFamily: '"Open Sans",sans-serif',
      marginTop: '20px',
      minHeight: '210px',
      alignItems: 'center',
    }}
  >
    <div style={{
        display: 'flex',
        alignItems: 'center',
        flexDirection: 'column',
        gap: '10px',
      }}
    >
      <div style={{ background: '#F5F1FA', borderRadius: '20%' }}>
        {icon}
      </div>
      <Typography sx={{
          fontSize: '18px', color: '#333333', fontWeight: 700,
        }}
      >
        {text}
      </Typography>
      <Typography>{description}</Typography>
    </div>
  </Box>
  );

export default NoDataFound;
