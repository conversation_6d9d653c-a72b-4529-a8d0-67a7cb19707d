import { render, screen } from '@testing-library/react';
import React from 'react';
import NoDataFound from './NoDataFound';

const TestIcon = () => <div>Test Icon</div>;

describe('NoDataFound Component', () => {
  const testText = 'No Data Available';
  const testDescription = 'We could not find any data matching your request.';

  test('applies correct styles', () => {
    render(<NoDataFound icon={<TestIcon />} text={testText} description={testDescription} />);

    const textElement = screen.getByText(testText);
    expect(textElement).toHaveStyle('font-size: 18px');
    expect(textElement).toHaveStyle('font-weight: 700');
    expect(textElement).toHaveStyle('color: #333333');
  });
});
