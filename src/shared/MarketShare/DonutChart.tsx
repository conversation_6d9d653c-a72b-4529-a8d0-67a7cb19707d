import React from 'react';
import {
  <PERSON><PERSON><PERSON>, <PERSON>, Cell,
} from 'recharts';

const piR = 50;
const poR = 100;
const pvalue = 50;
const chartCx = 150;
const chartCy = 150;
const innerRadius = 80;
const outerRadius = 130;
const needle = (value, pdata, cx, cy, iR, oR, totalUsage) => [
  <text x={cx} y={cy - 10} dy={8} textAnchor="middle" className="dataLable" fill="#4B535E">Total Data Usage </text>,
  <text x={cx} y={cy + 10} dy={8} textAnchor="middle" className="dataLable" fill="#4B535E">(MB)</text>,
  <text x={cx} y={cy + 40} dy={8} textAnchor="middle" className="dataUsage" fill="#2C3542">{totalUsage}</text>,

];
export default function DonutChart({ chartData, totalUsage }:any) {
  return (
    <PieChart width={300} height={300}>
      <Pie
        data={chartData}
        cx={chartCx}
        cy={chartCy}
        innerRadius={innerRadius}
        outerRadius={outerRadius}
        fill="#8884d8"
        dataKey="value"
        stroke="none"
        // onMouseEnter={onPieEnter}
      >
        {chartData.map((entry) => (
          <Cell key={`cell-${entry.id}`} fill={entry.color} />
        ))}
      </Pie>
      {needle(pvalue, chartData, chartCx, chartCy, piR, poR, totalUsage)}
    </PieChart>
  );
}

DonutChart.defaultProps = {
  chartData: [],
};
