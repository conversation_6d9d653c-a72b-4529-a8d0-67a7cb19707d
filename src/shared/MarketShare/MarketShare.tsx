import React, { useState } from 'react';
import { Box, Tooltip, Typography } from '@mui/material';
import CustomDatePicker from 'shared/CustomDatePicker';
import dayjs from 'dayjs';
import {
  currentYearMonthDay, lastDayOfMonth, startWithAugust,
} from 'core/utilities/formatDate';
import { useAppContext } from 'AppContextProvider';
import { TimelineDot } from '@mui/lab';
import MuiTable from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';
import { MuiTableProvider } from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable/MuiTableContext';
import { IChartData, IMarketShare } from 'features/SimManagementDetail/SimManagement.module';
import SimManagementPlaceholder from 'features/SimManagement/SimManagementPlaceholder';
import { TGetCurrentThemeColors } from 'features/models';
import { accountNameSlice } from 'core/utilities/parsing';
import DonutChart from './DonutChart';

export const totalRow = 'Total';

const getRowClassName = ({ id }) => {
  if (id !== totalRow) return '';
  return 'MuiDataGrid-row-bold total-sticky-row';
};

interface IColumns{
  field: string
  sortable: boolean
  align?: string
  headerClassName?: string
  headerName?: string
  width?: number
  cellClassName?: string,
  renderCell?: (args: { row: any }) => string;
  renderHeader?: (params: any) => React.ReactNode;
}
interface ImarkestShare{
  columns:IColumns[],
  loading:boolean,
  marketShareData:IMarketShare | undefined,
  chartData:IChartData[],
  errorMessage:string,
  onChangeDate:any,
}
export default function MarketShare({
  columns, loading, marketShareData, onChangeDate, errorMessage,
  chartData,
}:ImarkestShare) {
  const [date, setDate] = useState<string | Date>(currentYearMonthDay);
  const { primaryColor, getBrandColors } = useAppContext();
  const accountWarning = marketShareData?.warningThresholdAccountValue;
  const message = `more than ${accountWarning}% of Data Usage is outside EE Network`;

  const maxDate = new Date(lastDayOfMonth);
  const minDate = new Date(startWithAugust);
  const DateInputProps = {
    inputFormat: 'MMM-YY',
    openTo: 'month',
    minDate,
  };

  return (
    <Box
      mt={1}
      minWidth={880}
      minHeight={300}
    >
      <CustomDatePicker
        label="Period"
        date={date}
        setDate={(dateParam) => {
          const formateDate = dayjs(dateParam).format('YYYY-MM-DD');
          setDate(formateDate);
          onChangeDate(formateDate);
        }}
        width={111}
        maxDate={maxDate}
        disabledInput
        {
        ...DateInputProps
        }
      />

      {(!loading && !!marketShareData && marketShareData?.summary?.length) ? (
        <Box
          display="flex"
          alignItems="flex-start"
          sx={{
            '& .MuiDataGrid-columnHeader.MuiDataGrid-withBorderColor.data-usage-mb': {
              marginLeft: 2,
            },
            '& .data-usage-cell': {
              fontSize: '14px !important',
            },
            '&.MuiDataGrid-columnHeaderTitleContainer': {
              justifyContent: 'center !important',
              width: '100%  !important',
            },
            '.recharts-pie-label-line': {
              display: 'none',
            },
            '.dataLable': {
              fontFamily: 'BT Curve, sans-serif',
              fontSize: '14px',
              fontWeight: 400,
              lineHeight: '20px',
              letterSpacing: '0px',
              textAlign: 'center',
              color: '#4B535E',
            },
            '.dataUsage': {
              fontFamily: 'BT Curve, sans-serif',
              fontSize: '18px',
              fontWeight: 500,
              lineHeight: '22px',
              letterSpacing: '0em',
              textAlign: 'left',
              color: '#2C3542',
            },
            '& .total-sticky-row': {
              backgroundColor: '#ebe3f6',
              position: 'sticky',
              bottom: '0px',
              zIndex: '1111111 !important',
            },
          }}
        >
          <Box display="flex" flexDirection="column" alignItems="center">
            <MuiTableProvider
              onChange={null}
              onChangePagination={null}
              onChangeSearch={null}
              onChangeSort={null}
              defaultSort={undefined}
            >
              <MuiTable
                sx={{
                  '& .MuiDataGrid-main': {
                    '& .MuiDataGrid-row:last-child .MuiDataGrid-cell': {
                      background: '#ebe3f6',
                    },
                  },
                }}
                rows={marketShareData.summary}
                maxTableHeight="270px"
                columns={columns}
                loading={loading}
                primaryColor={primaryColor as string}
                getCurrentThemeColors={getBrandColors as TGetCurrentThemeColors}
                getRowClassName={getRowClassName}
                hideFooter
              />
            </MuiTableProvider>
            {marketShareData.warningThreshold ? (
              <Box
                display="flex"
                sx={{
                  color: '#000000',
                  padding: '10px',
                  borderRadius: ' 4px',
                  border: '1px solid #F18F93',
                  backgroundColor: '#FDECEC',
                  my: '10px',
                }}
              >
                <Typography fontFamily="BT Curve, sans-serif" fontSize="14px" fontWeight={700} lineHeight="20px">
                  Pay attention:&nbsp;
                </Typography>
                <Typography fontFamily="BT Curve, sans-serif" fontSize="14px" fontWeight={400} lineHeight="20px">
                  {message}
                </Typography>
              </Box>
            ) : (<Box sx={{ minHeight: '63px' }} />)}

          </Box>
          <DonutChart chartData={chartData} totalUsage={marketShareData?.totalUsage} />
          <Box
            display="flex"
            flexDirection="column"
            mt={4}
            gap={1}
            sx={{
              '& .MuiTimelineDot-filled': {
                margin: '6px 9px',
                boxShadow: 'unset',
                padding: '2px',
              },
            }}
          >
            {marketShareData?.summary.map((item: any) => {
              const carrier: string = accountNameSlice(item.carrier, 9);
              const toolTip = (item.carrier && item.carrier.length > 9) ? item.carrier : '';
              if (item.id !== 'Total') {
                return (
                  <Box display="flex" alignItems="center">
                    <TimelineDot sx={{ backgroundColor: item?.color }} />
                    <Box
                      display="flex"
                      alignItems="center"
                      width={130}
                      justifyContent="space-between"
                      sx={{
                        gap: '15px',
                      }}
                    >
                      <Tooltip title={toolTip} arrow placement="top">
                        <Typography
                          variant="body1"
                          fontFamily="BT Curve, sans-serif"
                          fontStyle="normal"
                          fontWeight={400}
                          fontSize="13px"
                          color="#525252"
                        >
                          {carrier}
                        </Typography>
                      </Tooltip>
                      <Typography
                        variant="body1"
                        fontFamily="BT Curve, sans-serif"
                        fontStyle="normal"
                        fontWeight={400}
                        fontSize="13px"
                        color="#525252"
                        textAlign="right"
                      >
                        {item.percentage}

                      </Typography>
                    </Box>
                  </Box>
                );
              }
              return null;
            })}
          </Box>
        </Box>
      ) : (
        <Box
          display="flex"
          alignItems="flex-center"
          justifyContent="center"
        >
          <Box mt={10}>
            <SimManagementPlaceholder loading={loading} title={errorMessage} />
          </Box>
        </Box>
      )}

    </Box>
  );
}
