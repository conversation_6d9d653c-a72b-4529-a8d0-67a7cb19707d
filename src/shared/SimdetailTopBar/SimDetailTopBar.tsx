import React, { useContext } from 'react';
import { Box, Typography } from '@mui/material';
import CustomDatePicker from 'shared/CustomDatePicker';
import { SimDetailContex } from 'features/SimManagementDetail/Context/SimDetailContex';
import dayjs from 'dayjs';

import SummaryParse from 'core/utilities/parsing';
import useGetSummary from 'hooks/useGetSummary';
import { IAccountTopBar } from 'features/SimManagementDetail/SimManagement.module';
// import SimIcon from 'assets/images/SimIcon';
import SimIconHeader from 'assets/images/SimIconHeader';
import { lastDayOfMonth, startWithAugust } from 'core/utilities/formatDate';

const SimDetailTopBar = () => {
  const { onChangeDate, date } = useContext(SimDetailContex);
  const { summary } = useGetSummary();
  const topBarAccount = SummaryParse(summary);

  const maxDate = new Date(lastDayOfMonth);
  const minDate = new Date(startWithAugust);
  const DateInputProps = {
    inputFormat: 'MMM-YY',
    openTo: 'month',
    minDate,
  };

  return (
    <Box
      alignItems="center"
      display="flex"
    >

      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        gap={2}
      >

        <Box>
          <SimIconHeader />
        </Box>

      </Box>
      <Box marginLeft={5}>
        <CustomDatePicker
          label="Period"
          date={date}
          setDate={(newDate) => {
            const formateDate = dayjs(newDate).format('YYYY-MM');
            onChangeDate(formateDate);
          }}
          width={111}
          maxDate={maxDate}
          disabledInput
          {
          ...DateInputProps
          }
        />
      </Box>
      <Box display="flex" alignItems="center" gap={9} marginLeft={5}>
        {topBarAccount.map(({ name, value }: IAccountTopBar) => (
          <Box key={name} display="flex" flexDirection="column" rowGap={1.2}>

            {name !== 'Month' && (
              <Box>
                <Typography
                  variant="body1"
                  fontFamily="BT Curve, sans-serif"
                  fontStyle="normal"
                  fontWeight={400}
                  fontSize="14px"
                  lineHeight="140%"
                  color="#525252"
                >
                  {name}
                </Typography>
                <Typography
                  variant="body1"
                  fontFamily="BT Curve, sans-serif"
                  fontStyle="normal"
                  fontWeight={700}
                  fontSize="14px"
                  lineHeight="17px"
                  color="#333333"
                >
                  {value}
                </Typography>
              </Box>
            )}

          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default React.memo(SimDetailTopBar);
