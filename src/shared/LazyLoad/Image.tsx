import React, { SyntheticEvent } from 'react';

import ImagePlaceholder from 'assets/images/image-placeholder.svg';

interface IImage {
  src:string,
  alt:string
  height? :number | string,
  width?:number | string,
  className?:string | undefined,
  style?:any
}

/*
  This is basic Image component with static fallbackUrl.
  Later it will be  using LazyLoad
*/
const Image = ({
  src, alt, height, width, className, style,
}:IImage) => {
  const onImageError = (e: SyntheticEvent) => {
    (e.target as HTMLImageElement).onerror = null;
    (e.target as HTMLImageElement).src = String(ImagePlaceholder);
  };

  return (
    <img
      src={src}
      alt={alt}
      className={className}
      onError={onImageError}
      height={height}
      width={width}
      style={style}
    />
  );
};

Image.defaultProps = {
  height: '100px',
  width: '100px',
  className: '',
  style: '',
};
export default Image;
