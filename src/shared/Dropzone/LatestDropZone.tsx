 
import {
  Box,
  Button, IconButton, Typography,
} from '@mui/material';
import getCurrentThemeColors from 'core/utilities/getCurrentThemeColors';
import { toastError } from 'core/utilities/toastHelper';
import { isArray } from 'lodash';
import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { AiOutlineDelete, AiOutlineFile } from 'react-icons/ai';

import { MB_CSV_FILE_SIZE } from 'core/utilities/constants';
import './LatestDropZone.scss';

interface DropzoneProps {
  primaryColor: string,
  description?: string,
  uploadImage: any,
  setImage: any,
  dropzoneImg: any,
  dropzoneText: string,
  name?: string,
  description2?: string,
  images?: any,
  dropzoneClass?: string,
  image?: any,
  appImageIndex?: number,
  isSetImageByIndex?: boolean,
  removeAppImage?: any,
  formik: any,
  customErrorMessage?: string | null,
}

const Dropzone = ({
  uploadImage,
  images,
  setImage,
  description,
  description2,
  dropzoneClass,
  dropzoneImg,
  dropzoneText,
  image,
  removeAppImage,
  primaryColor,
  name,
  formik,
  customErrorMessage,
}: DropzoneProps) => {
  const [error, setError] = useState(false);
  const onDrop = useCallback(async (acceptedFiles) => {
    if (isArray(acceptedFiles) && acceptedFiles.length > 0) {
      setError(false);
      uploadImage(acceptedFiles[0]);
      setImage(acceptedFiles[0]);
    } else {
      setError(true);
    }
  }, [images]);
  const {
     
    getRootProps, getInputProps, open, acceptedFiles, fileRejections,
  } = useDropzone({
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    },
    noClick: true,
    noKeyboard: true,
    maxFiles: 1,
    onDrop,
    maxSize: MB_CSV_FILE_SIZE,
  });

  const isFileUploadError = fileRejections.length > 0 && fileRejections[0].errors.length > 0;
  const isFileTooLarge = fileRejections.length > 0 && fileRejections[0]?.file?.size > MB_CSV_FILE_SIZE;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let mutableFileRejections = [...fileRejections]; 
  if (isFileUploadError) {
    toastError('File type not supported');
    (fileRejections as any).length = 0;
  }

  if (error && description && fileRejections.length > 0) {
    toastError(description);
  }
  if (isFileTooLarge) {
    toastError('File size exceed 5MB');
    (fileRejections as any).length = 0;
  }
  return (
    <div className={`latest-dropzone__wrap ${dropzoneClass}`}>
      {!image
        ? (
          <>
            <div
              {...getRootProps({ className: 'latest-dropzone' })}
              style={{ borderColor: formik?.errors?.file || customErrorMessage || error && 'red' }}
            >
              <input name={name} data-testid="latest-dropzone__input" {...getInputProps()} />
              <div className="latest-dropzone__content">
                {dropzoneImg}
                <Typography variant="body1" component="p" className="latest-dropzone__text">
                  {dropzoneText}
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  className="latest-dropzone__btn"
                  onClick={open}
                >
                  Upload
                </Button>
              </div>
            </div>
            <Typography
              variant="body1"
              component="p"
              sx={{ color: (formik?.errors?.file || customErrorMessage) ? 'red' : 'unset' }}
              className={` ${!error && formik?.errors?.file
                && formik.touched.file && 'latest-dropZone_error'}`}
            >
              {!error && formik.touched.file && (customErrorMessage || formik?.errors?.file)}
            </Typography>
            <Typography variant="body1" component="p" sx={{ color: error ? 'red' : 'unset' }} className="dropZone_error">
              {error && description}
            </Typography>

            <Typography variant="body1" component="p" className="latest-dropzone__description">
              {description2}
            </Typography>
          </>
        )
        : (
          <div {...getRootProps({ className: 'latest-dropzone' })} data-testid="latest-dropzone__preview">
            <Box
              className="file-information"
              display="flex"
              alignItems="center"
              justifyContent="space-between"
              mx={6}
              height={70}
              sx={{
                backgroundColor: '#F5F1FA',
              }}
            >
              <Box display="flex" alignItems="center" ml={6}>
                <AiOutlineFile size={40} color="primary" />
                <Box component="div">
                  <Typography component="div" variant="body2" fontWeight={700} color="#333333">
                    {acceptedFiles[0]?.name}
                  </Typography>
                  <Typography component="div" variant="body2" fontWeight={400} color="#707070">
                    {`${acceptedFiles[0]?.size ? (acceptedFiles[0].size) : 0 * 0.0010} bytes`}
                  </Typography>

                </Box>
              </Box>
              <IconButton
                sx={{ marginRight: '24px' }}
                data-testid="latest-dropzone__preview-btn"
                className="latest-dropzone__preview-btn"
                style={{ backgroundColor: getCurrentThemeColors(primaryColor)[50] }}
                onClick={() => removeAppImage()}
              >
                <AiOutlineDelete size={24} />
              </IconButton>
            </Box>
          </div>
        )}
    </div>
  );
};

Dropzone.defaultProps = {
  dropzoneClass: '',
  name: '',
  image: '',
  appImageIndex: 0,
  isSetImageByIndex: false,
  removeAppImage: () => true,
  images: [],
  description: '',
  description2: '',
};

export default React.memo(Dropzone);
