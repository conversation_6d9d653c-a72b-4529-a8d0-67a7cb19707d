import React, {
  createContext, useMemo, ReactElement,
} from 'react';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import IResource from 'model/IResource';

interface IAppContext {
  primaryColor: string,
  secondaryColor: string,
  themeName: string,
  access: Array<IResource>,
  getBrandColors: (color: string) => ({ [index: number]: string }),
}

const AppContext = createContext<Partial<IAppContext>>({});

interface IAppContextProvider {
  children: ReactElement | ReactElement[] | null;
  value: {
     
    [key: string] : any
  }
}

const AppContextProvider = ({ children, value }: IAppContextProvider) => {
  const { themeName, currentTheme, access } = value;

  const getCurrentThemeColors = (color) => getBrandColors(color, themeName);

  const themeVariables = {
    primaryColor: currentTheme?.primaryColor,
    secondaryColor: currentTheme?.secondaryColor,
    getBrandColors: getCurrentThemeColors,
    themeName,
    access,
  };

  const appContextValue = useMemo(() => ({
    ...value,
    ...themeVariables,
  }), [themeName, access]);

  return (
    <AppContext.Provider value={appContextValue}>
      {children}
    </AppContext.Provider>
  );
};

const useAppContext = () => React.useContext(AppContext);

export { AppContext, AppContextProvider, useAppContext };

export default AppContextProvider;
