
import React, { useEffect } from 'react';
import { Box } from '@mui/material';
import {
  Route, Routes,
} from 'react-router-dom';
import PageNotFound from '@nv2/nv2-pkg-js-shared-components/lib/PageNotFound';
import { paths } from 'core/configs/paths';
import SimManagement from 'features/SimManagement';
import { IUser, organizationTypes } from 'user.model';
import Loader from 'shared/Loader';
import { useAppContext } from 'AppContextProvider';
import SimDetail from 'features/SimManagementDetail/SimDetail';
import { getAccountByOrganizationId } from 'features/SimManagement/api.service';
import PrivateRotes from 'PrivateRotes';
import { REPOSITORY, ROUTE_PERMISSION } from 'core/utilities/constants';
import OrderNewSim from 'features/SimManagement/SimOrdering/OrderNewSim';
import SimManagementClientTab from 'features/SimManagement/SimManagementClientTab';

interface IAppRoutesProps {
  user: IUser | undefined,
  isLoading: boolean
}

const SimManagementComponents = {
  [organizationTypes.DISTRIBUTOR]: SimManagement,
  [organizationTypes.CLIENT]: SimManagementClientTab,
};

const RouterLoader = () => (
  <Box sx={{ display: 'flex', width: '100vw', height: '100vh' }}>
    <Loader size={60} staticColor="#f5f1fa" />
  </Box>
);

const PageNotFoundWrapper = () => {
  const { primaryColor, getBrandColors } = useAppContext();

  return (
    <PageNotFound
      data-testid="not-found"
      primaryColor={primaryColor}
      getCurrentThemeColors={getBrandColors}
    />
  );
};

const AppRoutes = ({ user, isLoading }: IAppRoutesProps) => {
  const SimManagementElement = (user && !isLoading)
    ? SimManagementComponents[user.organization.type]
    : RouterLoader;
  const getAccountDetail = async (userinfo: IUser) => {
    let userProfile = JSON.parse(localStorage.getItem('userDetails') || 'null');
    if (userinfo.organization.type !== organizationTypes.DISTRIBUTOR) {
      if (!userProfile ||
          userProfile.organization.id !== userinfo?.organization.id ||
          !userProfile.accountId ||
          !userProfile.accountName) {
        const account = await getAccountByOrganizationId(userinfo?.organization.id);

        if (account && account.data) {
          userProfile = { ...userinfo, accountId: account.data.id, accountName: account.data.name };
          localStorage.setItem('userDetails', JSON.stringify(userProfile));
        }
      }
    }
  };

  useEffect(() => {
    if (user &&
        user.organization.type !== organizationTypes.DISTRIBUTOR &&
        (!user.accountId || !user.accountName)) {
      getAccountDetail(user);
    }
  }, [user?.organization?.id, user?.organization?.type, user?.accountId, user?.accountName]);

  return (
    <Routes>
      <Route
        path={paths.base}
        element={
          <PrivateRotes
            permission={[]}
            repoName={[]}
          >
            <SimManagementElement user={user} />
          </PrivateRotes>
        }
      />
      <Route
        path={paths.simDetail}
        element={(
          <PrivateRotes
            permission={user?.organization?.type === organizationTypes.DISTRIBUTOR
              ? [] : [ROUTE_PERMISSION.GET_SIM_MANAGEMENT, ROUTE_PERMISSION.GET_RATE_PLANS]}
            repoName={
            user?.organization?.type === organizationTypes.DISTRIBUTOR
              ? [
                REPOSITORY.SIM_MANAGEMENT,
                REPOSITORY.RATE_PLAN,
              ] : [
                REPOSITORY.SIM_MANAGEMENT,
                REPOSITORY.RATE_PLAN,
              ]
          }
          >
            <SimDetail />
          </PrivateRotes>
        )}
      />

      <Route
        path={paths.simOrderCreate}
        element={(
          <PrivateRotes
            permission={[
              ROUTE_PERMISSION.CREATE_ORDER,
            ]}
            repoName={[
              REPOSITORY.SIM_ORDERS,
            ]}
          >
            <OrderNewSim user={user} />
          </PrivateRotes>
        )}
      />

      <Route path={paths.notFound} element={<PageNotFoundWrapper />} />
      <Route path="*" element={<PageNotFoundWrapper />} />
    </Routes>
  );
};

export default AppRoutes;
