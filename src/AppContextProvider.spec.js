import React from 'react';
import testRender from 'core/utilities/testUtils';
import { AppContextProvider, useAppContext } from 'AppContextProvider';

const mockPrimaryColor = 'mockPrimaryColor';
const mockSecondaryColor = 'mockSecondaryColor';

const mockThemeName = 'mockThemeName';

describe('AppContextProvider', () => {
  const TestEl = () => {
    const { primaryColor, secondaryColor, themeName } = useAppContext();

    return (
      <div>
        <span>{primaryColor}</span>
        <span>{secondaryColor}</span>
        <span>{themeName}</span>
      </div>
    );
  };

  test('should be theme variables in the style of test element', () => {
    const { getByText } = testRender(
      <AppContextProvider
        value={{
          themeName: mockThemeName,
          currentTheme: {
            primaryColor: mockPrimaryColor,
            secondaryColor: mockSecondaryColor,
          },
        }}
      >
        <TestEl />
      </AppContextProvider>,
    );

    const primaryColorEl = getByText(mockPrimaryColor);
    const secondaryColorEl = getByText(mockSecondaryColor);
    const themeNameEl = getByText(mockThemeName);

    expect(primaryColorEl).toBeInTheDocument();
    expect(secondaryColorEl).toBeInTheDocument();
    expect(themeNameEl).toBeInTheDocument();
  });
});
