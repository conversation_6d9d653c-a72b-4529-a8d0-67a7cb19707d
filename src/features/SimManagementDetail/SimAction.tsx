import { Box } from '@mui/material';
import MuiTable from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';
import { MuiTableProvider } from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable/MuiTableContext';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import { useAppContext } from 'AppContextProvider';
import FileIcon from 'assets/images/FileIcon';
import useAbortController from 'core/hooks/useAbortController';
import { RESPONSE } from 'core/utilities/constants';
import { toastError, toastSuccess } from 'core/utilities/toastHelper';
import useMuiTableSearchParams from 'hooks/useMuiTableSearchParams';
import React, { useContext, useEffect, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { SimDetailContex } from './Context/SimDetailContex';
import { TGetCurrentThemeColors } from 'features/models';
import { DataDisconnectSessionMessage, flushSimStateMessage, SIM_ACTIONS, sortSimActionNames, TOASTS } from 'features/constants';
import { getSimActionByImsi, sendFlushSim, sendPOD } from 'features/SimManagement/api.service';
import useSimActionColumns from 'hooks/useSimActionColumns';
import GenericDialog from 'shared/Dialog/GenericDialog';
import IMSIAllocationsContextProvider from 'features/SimManagement/IMSIAllocations/IMSIAllocationsContext';
import { IMSI_DETAILS_TABS_TABS } from './SimManagement.models';
import { descending } from 'features/SimManagement/SimManagementClient/constants';
import { ISimAction } from 'features/SimManagement/SimManagement.models';

interface ISimActionProps {
  flushValidation: boolean,
  setFlushValidation: React.Dispatch<React.SetStateAction<boolean>>,
  SessionValidation: boolean,
  setSessionValidation: React.Dispatch<React.SetStateAction<boolean>>
}

const SimAction = ({ flushValidation, setFlushValidation, SessionValidation, setSessionValidation }: ISimActionProps) => {
  const { primaryColor, getBrandColors } = useAppContext();
  const [simAction, setSimAction] = React.useState<ISimAction[]>([]);
  const [noData, setNoData] = useState({});
  const [rowCount, setRowCount] = useState(0);
  const [counter, setCounter] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const { cancelPreviousRequest, setNewController } = useAbortController();
  const { date } = useContext(SimDetailContex);
  const { imsi } = useParams();
  const {
    generateParamsForUrl,
    getParamsFromUrl,
    defaultPagination,
    defaultSort,
    initialSearchValue,
  } = useMuiTableSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  const columns = useSimActionColumns();
  const user = localStorage.getItem('userDetails');
  const userProfile = user ? JSON.parse(user) : '';
  const { page, pageSize, field, sort, search } = getParamsFromUrl();


  const loadSimAction = async (page, pageSize, field, sort, search, signal) => {
    let ordering = sortSimActionNames[field];

    if (sort === descending) {
      ordering = `-${ordering}`;
    }
    const payload = {
      simAction: [SIM_ACTIONS.FLUSH, SIM_ACTIONS.POD],
      imsi: [imsi],
    };
    const { data: { results, totalCount } } = await getSimActionByImsi(
      payload, date ?? '', page, pageSize, ordering, search, signal
    );
    const responseArray = results.map((x, i) => {
      const data = { ...x };
      data.id = i.toString();
      return data;
    });
    if(responseArray.length === 0) {
      setNoData({
        icon: <FileIcon />,
        title: 'No available logs for chosen IMSI',
        description: RESPONSE.GET_SIMACTION_EMPTY,
      });
    }
    setSimAction(responseArray);
    setRowCount(totalCount);
  };

  const getData = async (page, pageSize, field, sort, search) => {
    cancelPreviousRequest();
    const { signal } = setNewController();
    const newSearchParams = generateParamsForUrl(page, pageSize, field, sort, search, 'sim_');

    newSearchParams.set('tab', IMSI_DETAILS_TABS_TABS.DIAGNOSTICS);

    const newUrl = `${location.pathname}?${newSearchParams.toString()}`;
    navigate(newUrl, { replace: true });
    try {
      setIsLoading(true);
      await loadSimAction(page, pageSize, field, sort, search, signal);
    } catch (errorObj: any) {
      setRowCount(0);
      setSimAction([]);
      let message = '';
      if (errorObj?.response?.status === 404) {
        message = RESPONSE.GET_SIMACTION_EMPTY;
      } else if (errorObj?.response?.status === 500) {
        message = RESPONSE.HTTP_STATUS_500;
      } else {
        message = errorObj?.response?.data?.detail;
      }
      setNoData({
        icon: <FileIcon />,
        title: 'No available logs for chosen IMSI',
        description: message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshData = (initialPage?: number, initialPageSize?: number) => {
    getData(initialPage ?? page, initialPageSize ?? pageSize, field, sort, search);
    setCounter((counter) => counter + 1);
  };

  const handleDisconnectDataSession = async () => {
    setConfirmLoading(true);

    try {
      const payload = { imsi, createdBy: userProfile?.email };
      await sendPOD(payload);
      setSessionValidation(false);
      toastSuccess(TOASTS?.SUCCESS_DATA_SESSION_DISCONNECT);
      refreshData(1, 10);
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.detail ||
        TOASTS?.FAILURE_DATA_SESSION_DISCONNECT;
      setSessionValidation(false);
      toastError(errorMessage);
      refreshData(1, 10);
    } finally {
      setConfirmLoading(false);
    }
  };

  const handleFlushSimState = async () => {
    setConfirmLoading(true);

    try {
      const payload = { imsi, createdBy: userProfile?.email };
      await sendFlushSim(payload);
      setFlushValidation(false);
      toastSuccess(TOASTS?.SUCCESS_FLUSH_SIM_STATE);
      refreshData(1, 10);
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.detail ||
        TOASTS?.FAILURE_FLUSH_SIM_STATE;
      setFlushValidation(false);
      toastError(errorMessage);
      refreshData(1, 10);
    } finally {
      setConfirmLoading(false);
    }
  };

  const onChange = ({ page, pageSize }, { field, sort }, search) => {
    getData(page, pageSize, field, sort, search);
  };

  const getRowClassName = ({ id }) => {
    if (id === 'TOTAL') return 'MuiDataGrid-row-bold';
    return '';
  };

  const dynamicHeight = `calc(${simAction?.length > 0 ? '-470px' : '-418px'} + 100vh)`;

  useEffect(() => {
    if (!date) return;
    refreshData();
  }, [date]);

  return (
    <Box
      data-testid="audit-trail"
      className="audit-trail"
      sx={{
        background: styles.lightColor50,
        '& .MuiDataGrid-row-bold': {
          '& .MuiCheckbox-root': {
            display: 'none !important',
          },
        },
        '& .MuiDataGrid-columnHeaders': {
          backgroundColor: `${styles.lightColor100} !important`,
        },
        '&  .MuiDataGrid-columnHeaders': {
          backgroundColor: '#F5F5F9 !important',
        },
        '& .MuiDataGrid-virtualScroller': {
          height: `${dynamicHeight} !important`,
          overflow: rowCount < 0 ? 'none' : "auto !important",
        }
      }}
    >
      <IMSIAllocationsContextProvider value={{ getData, getParamsFromUrl }} key={counter}>
        <MuiTableProvider
          defaultPagination={defaultPagination}
          onChange={onChange}
          onChangePagination={onChange}
          onChangeSearch={onChange}
          initialSearchValue={initialSearchValue}
          onChangeSort={onChange}
          defaultSort={defaultSort}
        >
          <MuiTable
            sx={{
              '& .MuiCheckbox-colorPrimary': {
                color: '#C6C7D5',
              },
              '& .Mui-disabled ': {
                opacity: '0.3',
              },
            }}
            rows={simAction}
            columns={columns}
            loading={isLoading}
            rowCount={rowCount}
            primaryColor={primaryColor as string}
            getCurrentThemeColors={getBrandColors as TGetCurrentThemeColors}
            showFirstLastPageButtons
            isVisibleSearchInput
            getRowClassName={getRowClassName}
            getRowId={(row) => `${row?.id}`}
            hideFooter={!(simAction.length > 0)}
            noDataConfig={noData}
          />
        </MuiTableProvider>
      </IMSIAllocationsContextProvider>
      <GenericDialog
        loading={confirmLoading}
        open={flushValidation}
        title={"Are you sure?"}
        onClose={() => {
          setFlushValidation(false);
        }}
        onSuccess={() => {
          handleFlushSimState()
        }}
      >
        {flushSimStateMessage}
      </GenericDialog>
      <GenericDialog
        loading={confirmLoading}
        open={SessionValidation}
        title={"Are you sure?"}
        onClose={() => {
          setSessionValidation(false);
        }}
        onSuccess={() => {
          handleDisconnectDataSession()
        }}
      >
        {DataDisconnectSessionMessage}
      </GenericDialog>
    </Box>
  )
}

export default SimAction
