import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import testRender from 'core/utilities/testUtils';
import { coreAxios } from 'core/services/HTTPService';
import SmsConnectionHistory from './SmsConnectionHistory';

jest.mock('core/services/HTTPService');
describe('Sms Connection History', () => {
  test('renders SmsConnectionHistory with correct headers', async () => {
    testRender(<SmsConnectionHistory selectedTab={1} />);
    await waitFor(() => {
      expect(screen.getByText('ICCID')).toBeInTheDocument();
      expect(screen.getByText('IMSI')).toBeInTheDocument();
      expect(screen.getByText('Country')).toBeInTheDocument();
    });
  });
  test('should call get Api once after call', async () => {
    const getList = jest.spyOn(coreAxios, 'get');

    testRender(<SmsConnectionHistory selectedTab={1} />);

    expect(getList).toHaveBeenCalledTimes(2);
  });
  test('renders 10 rows per page', async () => {
    const { container } = testRender(<SmsConnectionHistory selectedTab={1} />);
    await waitFor(() => {
      const rows = container.querySelectorAll('.MuiDataGrid-row');
      expect(rows.length).toBe(rows.length);
    });
  });
});
