import React, { useContext, useEffect, useState } from 'react';
import {
  Box,
} from '@mui/material';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import MuiTable from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';
import { MuiTableProvider } from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable/MuiTableContext';
import { useAppContext } from 'AppContextProvider';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { RESPONSE } from 'core/utilities/constants';
import useMuiTableSearchParams from 'core/hooks/useMuiTableSearchParams';
import useAbortController from 'core/hooks/useAbortController';
import { TGetCurrentThemeColors } from 'features/models';
import IMSIAllocationsContextProvider from 'features/SimManagement/IMSIAllocations/IMSIAllocationsContext';
import { descending, sortFieldNames } from 'features/SimManagement/SimManagementClient/constants';
import { getAuiditTrail } from 'features/SimManagement/api.service';
import useAuditTrailColumns from 'hooks/useAuditTrailColumns';
import FileIcon from 'assets/images/FileIcon';
import { SimDetailContex } from './Context/SimDetailContex';
import { IMSI_DETAILS_TABS_INDEXES, IMSI_DETAILS_TABS_TABS } from './SimManagement.models';
import { IParseAuditTrailData } from './SimManagement.module';

interface IAuditTrailProps {
  selectedTab: undefined | number
}

const AuditTrail = ({ selectedTab }:IAuditTrailProps) => {
  const { primaryColor, getBrandColors } = useAppContext();
  const { cancelPreviousRequest, setNewController } = useAbortController();
  const { date } = useContext(SimDetailContex);
  const { imsi } = useParams();
  const {
    generateParamsForUrl,
    getParamsFromUrl,
    defaultPagination,
    defaultSort,
    initialSearchValue,
  } = useMuiTableSearchParams();

  const columns = useAuditTrailColumns();
  const [auiditTrail, setAuiditTrail] = useState<IParseAuditTrailData[] >([]);
  const [rowCount, setRowCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [noData, setNoData] = useState({});
  const navigate = useNavigate();
  const location = useLocation();
  const loadAuiditTrail = async (page, pageSize, field, sort, search, signal) => {
    let ordering = sortFieldNames[field];

    if (sort === descending) {
      ordering = `-${ordering}`;
    }
    const { data: { results, totalCount } } = await getAuiditTrail(
      imsi, date, page, pageSize, ordering, search, signal,
    );
    const responseArray = results.map((x, i) => {
      const data = { ...x };
      data.id = i.toString();
      return data;
    });
    setAuiditTrail(responseArray);
    setRowCount(totalCount);
  };

  const getData = async (page, pageSize, field, sort, search) => {
    cancelPreviousRequest();
    const { signal } = setNewController();
    const newSearchParams = generateParamsForUrl(page, pageSize, field, sort, search);

    newSearchParams.set('tab', IMSI_DETAILS_TABS_TABS.AUDIT_TRAIL);

    const newUrl = `${location.pathname}?${newSearchParams.toString()}`;

    navigate(newUrl, { replace: true });
    try {
      setIsLoading(true);
      await loadAuiditTrail(page, pageSize, field, sort, search, signal);
    } catch (errorObj:any) {
      let message = '';
      if (errorObj?.response?.status === 404) {
        message = RESPONSE.GET_AUIDITTRAIL_EMPTY;
      } else if (errorObj?.response?.status === 500) {
        message = RESPONSE.HTTP_STATUS_500;
      } else {
        message = errorObj?.response?.data?.detail;
      }
      setNoData({
        icon: <FileIcon />,
        title: 'No available logs for chosen IMSI',
        description: message,
      });
      setRowCount(0);
      setAuiditTrail([]);
    } finally {
      setIsLoading(false);
    }
  };

  const onChange = ({ page, pageSize }, { field, sort }, search) => {
    getData(page, pageSize, field, sort, search);
  };

  useEffect(() => {
    if (selectedTab !== undefined && selectedTab !== IMSI_DETAILS_TABS_INDEXES.AUDIT_TRAIL) return;
    if (!date) return;
    const {
      page, pageSize, field, sort, search,
    } = getParamsFromUrl();

    getData(page, pageSize, field, sort, search);
  }, [date]);

  const getRowClassName = ({ id }) => {
    if (id === 'TOTAL') return 'MuiDataGrid-row-bold';
    return '';
  };
  return (
    <Box
      data-testid="audit-trail"
      className="audit-trail"
      sx={{
        background: styles.lightColor50,
        '& .MuiDataGrid-row-bold': {
          '& .MuiCheckbox-root': {
            display: 'none !important',
          },
        },
        '& .MuiDataGrid-columnHeaders': {
          backgroundColor: `${styles.lightColor100} !important`,
        },
        '&  .MuiDataGrid-columnHeaders': {
          backgroundColor: '#F5F5F9 !important',
        },
        '& .MuiDataGrid-virtualScrollerContent': {
          minHeight: '160px !important',
        },

      }}
    >
      <IMSIAllocationsContextProvider value={{ getData, getParamsFromUrl }}>
        <MuiTableProvider
          defaultPagination={defaultPagination}
          onChange={onChange}
          onChangePagination={onChange}
          onChangeSearch={onChange}
          initialSearchValue={initialSearchValue}
          onChangeSort={onChange}
          defaultSort={defaultSort}
        >
          <MuiTable
            sx={{
              '& .MuiCheckbox-colorPrimary': {
                color: '#C6C7D5',
              },
              '& .Mui-disabled ': {
                opacity: '0.3',
              },
            }}
            rows={auiditTrail}
            columns={columns}
            loading={isLoading}
            rowCount={rowCount}
            primaryColor={primaryColor as string}
            getCurrentThemeColors={getBrandColors as TGetCurrentThemeColors}
            showFirstLastPageButtons
            isVisibleSearchInput
            getRowClassName={getRowClassName}
            getRowId={(row) => `${row?.id}`}
            hideFooter={!(auiditTrail.length > 0)}
            noDataConfig={noData}
          />
        </MuiTableProvider>
      </IMSIAllocationsContextProvider>
    </Box>
  );
};
export default AuditTrail;
