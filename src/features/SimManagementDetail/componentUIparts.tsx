import {
  Box, styled,
} from '@mui/material';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

const StyleBoxTabContainer = styled(Box)({
  width: '100%',
  borderBottom: `1px solid ${styles.inputBorderColor}`,
  marginLeft: 5,
  '& .MuiButtonBase-root, &.MuiTab-textColorPrimary': {
    fontFamily: 'BT Curve, sans-serif',
    fontStyle: 'normal',
    fontWeight: 700,
    fontSize: '14px',
    lineHeight: '17px',
    display: 'flex',
    textTransform: 'none',
    color: '#333333',
  },
});

export default StyleBoxTabContainer;
