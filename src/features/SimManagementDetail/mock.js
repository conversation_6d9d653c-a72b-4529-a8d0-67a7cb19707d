const connectionHistoryData = [{
  icid: 'a123as4',
  deviceid: '53554000000126',
  country: 'United Kingdom',
  carrier: 'idea',
  bearer: '5G',
  sst: '2022-08-07 13:32:15',
  set: '2022-08-07 13:42:15',
  duration: '10 min',
  dv: '1,000,000',
  rp: 'PAYG',

},
{
  icid: 'b1fd234',
  deviceid: '43554000000126',
  country: 'india',
  carrier: 'vodafone',
  bearer: '5G',
  sst: '2022-08-07 13:32:15',
  set: '2022-08-07 13:42:15',
  duration: '10 min',
  dv: '1,000,000',
  rp: 'PAYG',

},
{
  icid: 'c12gf34',
  deviceid: '43554000000126',
  country: 'srilanka',
  carrier: 'hutch',
  bearer: '5G',
  sst: '2022-08-07 13:32:15',
  set: '2022-08-07 13:42:15',
  duration: '10 min',
  dv: '1,000,000',
  rp: 'PAYG',

},
{
  icid: 'v1f23g4',
  deviceid: '63554000000126',
  country: 'ukrain',
  carrier: 'jio',
  bearer: '5G',
  sst: '2022-08-07 13:32:15',
  set: '2022-08-07 13:42:15',
  duration: '10 min',
  dv: '1,000,000',
  rp: 'PAYG',

},
{
  icid: 'n12fd34',
  deviceid: '83554000000126',
  country: 'indon asia',
  carrier: 'airtel',
  bearer: '5G',
  sst: '2022-08-07 13:32:15',
  set: '2022-08-07 13:42:15',
  duration: '10 min',
  dv: '1,000,000',
  rp: 'PAYG',

}, {
  icid: 'm1f2d34',
  deviceid: '93554000000126',
  country: 'United Kingdom',
  carrier: 'bsnl',
  bearer: '5G',
  sst: '2022-08-07 13:32:15',
  set: '2022-08-07 13:42:15',
  duration: '10 min',
  dv: '1,000,000',
  rp: 'PAYG',

},
{
  icid: 'qf2s34',
  deviceid: '**************',
  country: 'United Kingdom',
  carrier: 'uninor',
  bearer: '5G',
  sst: '2022-08-07 13:32:15',
  set: '2022-08-07 13:42:15',
  duration: '10 min',
  dv: '1,000,000',
  rp: 'PAYG',

},

];

const audittrailData = [{
  id: 'rateplan1',
  field: 'Rate Plan',
  pValue: 'PAYG 123',
  nValue: 'PAYG 2',
  eDate: '2022-10-07 13:32:15',
  uname: 'DaleConnorBT',
},
{
  id: 'rateplan2',
  field: 'SIM Status',
  pValue: 'Inactive',
  nValue: 'Active',
  eDate: '2022-10-07 13:32:15',
  uname: 'DaleConnorBT',
},
{
  id: 'rateplan3',
  field: 'Rate Plan',
  pValue: 'PAYG 123',
  nValue: 'PAYG 2',
  eDate: '2022-10-07 13:32:15',
  uname: 'DaleConnorBT',
},
{
  id: 'rateplan4',
  field: 'SIM Status',
  pValue: 'Inactive',
  nValue: 'Active',
  eDate: '2022-10-07 13:32:15',
  uname: 'DaleConnorBT',
},
];
const topBarAccount = [
  {
    name: 'Operator Account ID',
    value: 2261,
  }, {
    name: 'Month',
    value: '2022-08',
  }, {
    name: 'ICCID',
    value: 4567,
  }, {
    name: 'MSISDN',
    value: ***********,
  }, {
    name: 'Primary IMSI',
    value: **************,
  }, {
    name: 'IMEI',
    value: ***************,
  }, {
    name: 'SIM Status',
    value: 'Active',
  },
  {
    name: 'First Activated',
    value: '2022-10-01',
  },
  {
    name: 'Last Session',
    value: '2022-11-09',
  },

];
export { connectionHistoryData, audittrailData, topBarAccount };
