 
import React, {
  useContext, useEffect, useState,
} from 'react';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import MuiTable from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';
import { MuiTableProvider } from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable/MuiTableContext';
import {
  Box, IconButton, Tooltip,
} from '@mui/material';
import { useAppContext } from 'AppContextProvider';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { REPOSITORY, RESPONSE, ROUTE_PERMISSION } from 'core/utilities/constants';

import { getConnectionHistory, getConnectionHistoryExport } from 'features/SimManagement/api.service';
import useMuiTableSearchParams from 'core/hooks/useMuiTableSearchParams';
import useAbortController from 'core/hooks/useAbortController';
import { descending, sortFieldNames } from 'features/SimManagement/SimManagementClient/constants';
import { getSearchSortModel, onExportCSVFileHandle } from 'core/utilities/exportCSVMAP';
import exportCSVFile from 'core/utilities/exportCSVFile';
import { TGetCurrentThemeColors } from 'features/models';
import IMSIAllocationsActionExport from 'features/SimManagement/IMSIAllocations/IMSIAllocationsActionExport';
import IMSIAllocationsContextProvider from 'features/SimManagement/IMSIAllocations/IMSIAllocationsContext';
import useConnectionHistoryColumns from 'hooks/useConnectionHistoryColumns';
import FileIcon from 'assets/images/FileIcon';
import MarketShareDialog from 'features/SimManagement/SimManagementClient/MarketShare/MarketShareDialog';
import PieChart from 'assets/images/PieChart';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import { SimDetailContex } from './Context/SimDetailContex';
import { IConnectionHistory } from './SimManagement.module';
import { IMSI_DETAILS_TABS_INDEXES, IMSI_DETAILS_TABS_TABS } from './SimManagement.models';

interface IConnetionHistoryProps {
  selectedTab: undefined | number
}

const ConnectionHistory = ({ selectedTab }: IConnetionHistoryProps) => {
  const { cancelPreviousRequest, setNewController } = useAbortController();
  const { primaryColor, getBrandColors } = useAppContext();
  const { date } = useContext(SimDetailContex);

  const [toggle, setToggle] = useState(false);
  const { imsi } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const {
    generateParamsForUrl,
    getParamsFromUrl,
    defaultPagination,
    defaultSort,
    initialSearchValue,
  } = useMuiTableSearchParams();

  const columns = useConnectionHistoryColumns();
  const [connectionHistoryData, setConnectionHistory] = useState<IConnectionHistory[]>([]);
  const [rowCount, setRowCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [noData, setNoData] = useState({});

  const getData = async (page, pageSize, field, sort, search) => {
    cancelPreviousRequest();
    const { signal } = setNewController();
    const newSearchParams = generateParamsForUrl(page, pageSize, field, sort, search);
    newSearchParams.set('tab', IMSI_DETAILS_TABS_TABS?.CONNECTION_HISTORY);
    // for search url of paginatoion and sorting
    const newUrl = `${location?.pathname}?${newSearchParams?.toString()}`;

    navigate(newUrl, { replace: true });
    try {
      setLoading(true);
      let ordering = sortFieldNames[field];

      if (sort === descending) {
        ordering = `-${ordering}`;
      }
      if (sort === 'asc') {
        ordering = `${ordering}`;
      }
      const { data: { results, totalCount } } = await getConnectionHistory(
        imsi, date, page, pageSize, ordering, search, signal);
      const responseArray = results.map((x, i) => {
        const data = { ...x };
        data.id = i.toString();
        return data;
      });
      setConnectionHistory(responseArray);
      setRowCount(totalCount);
    } catch (errorObj: any) {
      let message = '';
      if (errorObj?.response?.status === 404) {
        message = RESPONSE?.GET_CONNECTIONHISTORY_EMPTY;
      } else if (errorObj?.response?.status === 500) {
        message = RESPONSE.HTTP_STATUS_500;
      } else {
        message = errorObj?.response?.data?.detail;
      }
      setNoData({
        icon: <FileIcon />,
        title: 'Connection history is not found for chosen IMSI',
        description: message,
      });
      setConnectionHistory([]);
      setRowCount(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (selectedTab !== undefined && selectedTab !== IMSI_DETAILS_TABS_INDEXES.CONNECTION_HISTORY) {
      return;
    }

    const {
      page, pageSize, field, sort, search,
    } = getParamsFromUrl();

    getData(page, pageSize, field, sort, search);
  }, [date]);

  const onChange = ({ page, pageSize }, { field, sort }, search) => {
    getData(page, pageSize, field, sort, search);
  };

  const onExportCSVFile = async () => {
    const { ordering, search, formattedDate } = getSearchSortModel(getParamsFromUrl, setIsLoading);
    const fileName = `ConnectionHistory_${formattedDate}.csv`;
    const { data: csvData } = await getConnectionHistoryExport(imsi ?? '', date ?? '', ordering, search ?? '');
    onExportCSVFileHandle(csvData, fileName, exportCSVFile, setIsLoading);
  };
  return (
    <Box
      data-testid="connection-history"
      className="connection-history"
      sx={{
        background: styles.lightColor50,
        '& .MuiDataGrid-row-bold': {
          '& .MuiCheckbox-root': {
            display: 'none !important',
          },
        },
        '& .MuiDataGrid-columnHeaders': {
          backgroundColor: `${styles.lightColor100} !important`,
        },
        '&  .MuiDataGrid-columnHeaders': {
          backgroundColor: '#F5F5F9 !important',
        },
        '& .MuiDataGrid-virtualScrollerContent': {
          minHeight: '160px !important',
        },
      }}
    >
      <IMSIAllocationsContextProvider value={{ getData, getParamsFromUrl }}>
        <MuiTableProvider
          defaultPagination={defaultPagination}
          onChange={onChange}
          onChangePagination={onChange}
          onChangeSearch={onChange}
          initialSearchValue={initialSearchValue}
          onChangeSort={onChange}
          defaultSort={defaultSort}
        >
          <MuiTable
            sx={{
              '& .MuiCheckbox-colorPrimary': {
                color: '#C6C7D5',
              },
              '& .Mui-disabled ': {
                opacity: '0.3',
              },
            }}
            Actions={() => (
              <Box
                display="flex"
                justifyContent="space-between"
                columnGap={2}
                marginLeft={3}
                sx={{ width: '100% !important' }}
              >
                <CommonAuthwrapper
                  permission={[ROUTE_PERMISSION.EXPORT_CONNECTION_HISTORY]}
                  repository={[REPOSITORY.MARKET_SHARE_REPORT]}
                >
                  <IMSIAllocationsActionExport
                    rowCount={rowCount}
                    onExportCSVFile={onExportCSVFile}
                    isLoading={isLoading}
                  />
                </CommonAuthwrapper>
                <CommonAuthwrapper
                  permission={[ROUTE_PERMISSION.MARKET_SHARE_FOR_ACCOUNT]}
                  repository={[REPOSITORY.MARKET_SHARE_REPORT]}
                >
                  <Tooltip title="Market Share Report" arrow placement="top">
                    <IconButton
                      sx={{
                        '&:hover path': {
                          stroke: 'unset',
                        },

                      }}
                      data-testid="marketshare-buttons"
                      onClick={() => setToggle(!toggle)}
                    >
                      <PieChart />
                    </IconButton>
                  </Tooltip>
                </CommonAuthwrapper>
              </Box>

            )}
            rows={connectionHistoryData}
            columns={columns}
            loading={loading}
            rowCount={rowCount}
            primaryColor={primaryColor as string}
            getCurrentThemeColors={getBrandColors as TGetCurrentThemeColors}
            showFirstLastPageButtons
            isVisibleSearchInput
            getRowId={(row) => row.id}
            hideFooter={!(connectionHistoryData.length > 0)}
            noDataConfig={noData}
          />
        </MuiTableProvider>
      </IMSIAllocationsContextProvider>
      {toggle && (
        <MarketShareDialog open={toggle} onClose={() => setToggle(false)} marketShareFor="imsi" />
      )}
    </Box>
  );
};
export default ConnectionHistory;
