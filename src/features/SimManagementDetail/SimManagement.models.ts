export interface IHeader {
  name: string;
  width?: number;
  align?: 'left' | 'right' | 'center';
  sticky?: boolean;
  valuePath?: string;
}

export interface IStyledTableCellProps {
  bold?: boolean;
  align?: 'left' | 'right' | 'center';
  sticky?: boolean;
  th?: boolean;
}

export interface IAllocation {
  id: number;
  rangeId: number;
  allocationId: number;
  ratePlanId: number;
  iccid: string;
  imsi: string;
  msisdn: string;
  simStatus: string;
}

export interface IAllocationResponse {
  results: IAllocation[]
}

export interface IIMSIAllocation {
  id: number;
  title: string;
  quantity: number;
  imsiFirst: string;
  imsiLast: string;
  createdAt: string;
  accountId: number;
  rangeId: number;
}
export interface ISIMUsage {
  iccid: string;
  msisdn: string;
  imsi: string;
  type: string;
  allocationReference: string;
  allocationDate: Date | string;
  simStatus: string;
  usage: number;
  ratePlan: string | null;
}
export interface ISystemAuditTrail {
  id: string;
  date: Date | string;
  ipAddress: string;
  field: string;
  priorValue: string;
  newValue: string;
  userName: string;
  iccid: string;
  msisdn: string;
  imsi: string;
}

export interface IPagination<Result> {
  results: Result
  lastPage: number
  page: number
  pageSize: number
  totalCount: number
}

export interface IIMSIRange {
  id: number;
  title: string;
  provider: string;
  formFactor: string;
  quantity: number;
  imsiFirst: string;
  imsiLast: string;
  remaining: number;
  createdAt: string;
  createdBy: string;
}

export interface IRatePlan {
  id: number;
  name: string;
  accessFee: number;
  currency: string;
  isDefault: boolean;
}

export interface IRatePlanCompany {
  accountId: number;
  accountName: string;
  accountLogoUrl: string;
  ratePlans: IRatePlan[];
}

export interface ITab {
  [key: string]: number;
}

export enum ACCOUNT_DETAILS_TABS {
  IMSI_ALLOCATIONS = 'sim-management',
  BILLING_SETTINGS = 'billing-settings',
  GENERAL_AUDIT_TRAIL = 'general-audit-trail'
}

export enum ACCOUNT_DETAILS_TABS_INDEXES {
  IMSI_ALLOCATIONS = 0,
  BILLING_SETTINGS = 1,
  GENERAL_AUDIT_TRAIL = 2
}

export enum IMSI_DETAILS_TABS_INDEXES {
  CONNECTION_HISTORY = 0,
  SMS_HISTORY = 1,
  VOICE_HISTORY = 2,
  DIAGNOSTICS = 3,
  SIM_LOCATION= 4,
  AUDIT_TRAIL = 5,
}

export enum IMSI_DETAILS_TABS_TABS {
  CONNECTION_HISTORY = 'connection-history',
  SMS_HISTORY = 'sms',
  VOICE_HISTORY = 'voice',
  DIAGNOSTICS = 'diagnostics',
  SIM_LOCATION = 'sim-location',
  AUDIT_TRAIL = 'audit-trail',
}

export interface ISimResponse {
  uuid: string;
  message: string;
  status: string;
}

export interface IsimRequest {
  imsi: string;
  createdBy: string;
}
export interface IBillingSettings {
  paymentTerms: number | null
  simCharge: number | string
}
