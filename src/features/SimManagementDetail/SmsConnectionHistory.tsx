 
import React, { useContext, useEffect, useState } from 'react';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import MuiTable from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';
import { MuiTableProvider } from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable/MuiTableContext';
import { Box } from '@mui/material';
import { ISMSConnectionHistory } from 'features/SimManagementDetail/SimManagement.module';
import { TGetCurrentThemeColors } from 'features/models';
import { useAppContext } from 'AppContextProvider';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import {
  descending,
  sortSmsFieldNames,
} from 'features/SimManagement/SimManagementClient/constants';
import { getSmsConnectionHistory, getSimConnectionHistoryExport } from 'features/SimManagement/api.service';
import { REPOSITORY, RESPONSE, ROUTE_PERMISSION } from 'core/utilities/constants';
import IMSIAllocationsActionExport from 'features/SimManagement/IMSIAllocations/IMSIAllocationsActionExport';
import { getSearchSortModel, onExportCSVFileHandle } from 'core/utilities/exportCSVMAP';
import exportCSVFile from 'core/utilities/exportCSVFile';
import useMuiTableSearchParams from 'core/hooks/useMuiTableSearchParams';
import IMSIAllocationsContextProvider from 'features/SimManagement/IMSIAllocations/IMSIAllocationsContext';
import useAbortController from 'core/hooks/useAbortController';
import FileIcon from 'assets/images/FileIcon';
import useSmsConnectionHistoryColumns from 'hooks/useSmsConnectionHistoryColumns';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import {
  IMSI_DETAILS_TABS_INDEXES,
  IMSI_DETAILS_TABS_TABS,
} from './SimManagement.models';
import { SimDetailContex } from './Context/SimDetailContex';

interface IConnetionHistoryProps {
  selectedTab: undefined | number;
}

const SmsConnectionHistory = ({ selectedTab }: IConnetionHistoryProps) => {
  const { cancelPreviousRequest, setNewController } = useAbortController();
  const { primaryColor, getBrandColors } = useAppContext();
  const { date } = useContext(SimDetailContex);
  const { imsi } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const {
    generateParamsForUrl,
    getParamsFromUrl,
    defaultPagination,
    defaultSort,
    initialSearchValue,
  } = useMuiTableSearchParams();

  const columns = useSmsConnectionHistoryColumns();
  const [smsHistoryData, setSmsHistoryData] = useState<
    ISMSConnectionHistory[]
  >([]);
  const [rowCount, setRowCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [noData, setNoData] = useState({});

  const getData = async (page, pageSize, field, sort, search) => {
    cancelPreviousRequest();
    const { signal } = setNewController();
    const newSearchParams = generateParamsForUrl(
      page,
      pageSize,
      field,
      sort,
      search,
    );
    newSearchParams.set('tab', IMSI_DETAILS_TABS_TABS.SMS_HISTORY);
    const newUrl = `${location.pathname}?${newSearchParams.toString()}`;

    navigate(newUrl, { replace: true });
    try {
      setLoading(true);
      let ordering = sortSmsFieldNames[field] || field;

      if (sort === descending) {
        ordering = `-${ordering}`;
      }
      if (sort === 'asc') {
        ordering = `${ordering}`;
      }
      const {
        data: { results, totalCount },
      } = await getSmsConnectionHistory(
        imsi,
        date,
        page,
        pageSize,
        ordering,
        search,
        signal,
      );
      const responseArray = results.map((x, i) => {
        const data = { ...x };
        data.id = i.toString();
        return data;
      });
      setSmsHistoryData(responseArray);
      setRowCount(totalCount);
    } catch (errorObj: any) {
      let message = '';
      if (errorObj?.response?.status === 404) {
        message = RESPONSE.GET_SMS_CONNECTIONHISTORY_EMPTY;
      } else if (errorObj?.response?.status === 500) {
        message = RESPONSE.HTTP_STATUS_500;
      } else {
        message = errorObj?.response?.data?.detail;
      }
      setNoData({
        icon: <FileIcon />,
        title: 'SMS history is not found for chosen IMSI',
        description: message,
      });
      setSmsHistoryData([]);
      setRowCount(0);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (
      selectedTab !== undefined
      && selectedTab !== IMSI_DETAILS_TABS_INDEXES.SMS_HISTORY
    ) { return; }

    const {
      page, pageSize, field, sort, search,
    } = getParamsFromUrl();

    getData(page, pageSize, field, sort, search);
  }, [date]);

  const onChange = ({ page, pageSize }, { field, sort }, search) => {
    getData(page, pageSize, field, sort, search);
  };

  const getRowClassName = ({ id }) => {
    if (id === 'TOTAL') return 'MuiDataGrid-row-bold';
    return '';
  };

  const onExportCSVFile = async () => {
    const { ordering, search, formattedDate } = getSearchSortModel(
      getParamsFromUrl,
      setIsLoading,
    );
    const fileName = `SMS_ConnectionHistory_${formattedDate}.csv`;
    const { data: csvData } = (await getSimConnectionHistoryExport(
      imsi ?? '',
      date ?? '',
      ordering,
      search ?? '',
    )) || [];
    onExportCSVFileHandle(csvData, fileName, exportCSVFile, setIsLoading);
  };

  return (
    <Box
      data-testid="connection-history"
      className="connection-history"
      sx={{
        background: styles.lightColor50,
        '& .MuiDataGrid-row-bold': {
          '& .MuiCheckbox-root': {
            display: 'none !important',
          },
        },
        '& .MuiDataGrid-columnHeaders': {
          backgroundColor: `${styles.lightColor100} !important`,
        },
        '&  .MuiDataGrid-columnHeaders': {
          backgroundColor: '#F5F5F9 !important',
        },
        '& .MuiDataGrid-virtualScrollerContent': {
          minHeight: '160px !important',
        },
      }}
    >
      <IMSIAllocationsContextProvider value={{ getData, getParamsFromUrl }}>
        <MuiTableProvider
          defaultPagination={defaultPagination}
          onChange={onChange}
          onChangePagination={onChange}
          onChangeSearch={onChange}
          initialSearchValue={initialSearchValue}
          onChangeSort={onChange}
          defaultSort={defaultSort}
        >
          <MuiTable
            sx={{
              '& .MuiCheckbox-colorPrimary': {
                color: '#C6C7D5',
              },
              '& .Mui-disabled ': {
                opacity: '0.3',
              },
            }}
            Actions={() => (
              <Box
                display="flex"
                justifyContent="space-between"
                columnGap={2}
                marginLeft={3}
                sx={{ width: '100% !important' }}
              >
                <CommonAuthwrapper
                  permission={[ROUTE_PERMISSION.GET_SMS_CONNECTION_HISTORY_EXPORT]}
                  repository={[REPOSITORY.MARKET_SHARE_REPORT]}
                >
                  <IMSIAllocationsActionExport
                    rowCount={rowCount}
                    onExportCSVFile={onExportCSVFile}
                    isLoading={isLoading}
                  />
                </CommonAuthwrapper>
              </Box>
            )}
            rows={smsHistoryData}
            columns={columns}
            loading={loading}
            rowCount={rowCount}
            primaryColor={primaryColor as string}
            getCurrentThemeColors={getBrandColors as TGetCurrentThemeColors}
            getRowClassName={getRowClassName}
            showFirstLastPageButtons
            isVisibleSearchInput
            getRowId={(row) => row.id}
            hideFooter={!(smsHistoryData.length > 0)}
            noDataConfig={noData}
          />
        </MuiTableProvider>
      </IMSIAllocationsContextProvider>

    </Box>
  );
};
export default SmsConnectionHistory;
