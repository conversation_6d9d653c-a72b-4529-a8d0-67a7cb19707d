import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import testRender from 'core/utilities/testUtils';
import { coreAxios } from 'core/services/HTTPService';
import VoiceConnectionHistory from './VoiceConnectionHistory';

jest.mock('core/services/HTTPService');
describe('Voice Connection History', () => {
  test('renders VoiceConnectionHistory with correct headers', async () => {
    testRender(<VoiceConnectionHistory selectedTab={2} />);
    await waitFor(() => {
      expect(screen.getByText('ICCID')).toBeInTheDocument();
      expect(screen.getByText('IMSI')).toBeInTheDocument();
      expect(screen.getByText('Country')).toBeInTheDocument();
    });
  });
  test('should call get Api once after call', async () => {
    const getList = jest.spyOn(coreAxios, 'get');

    testRender(<VoiceConnectionHistory selectedTab={2} />);

    expect(getList).toHaveBeenCalledTimes(2);
  });
  test('renders 10 rows per page', async () => {
    const { container } = testRender(<VoiceConnectionHistory selectedTab={2} />);
    await waitFor(() => {
      const rows = container.querySelectorAll('.MuiDataGrid-row');
      expect(rows.length).toBe(rows.length);
    });
  });
});
