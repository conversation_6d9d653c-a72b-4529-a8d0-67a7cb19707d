import { ReactNode } from 'react';

export interface ISummary {
  msisdn: string;
  imsi: string;
  iccid: string;
  firstActivated: string;
  lastSession: string;
  simStatus: string;
}
export interface IAuditTrailData {
  id: string;
  field: string;
  date: string;
  prior_value: string;
  new_value: string;
  user_name: string;
}
export interface IParseAuditTrailData {
  id: string;
  field: string;
  date: string;
  priorValue: string;
  newValue: string;
  userName: string;
}

export interface IConnectionHistory {
  id: string;
  iccid: string;
  deviceid?: string;
  country: string;
  carrier: string;
  bearer?: string,
  sessionStarttime: string;
  sessionEndtime: string;
  duration: number;
  dataVolume: number;
  rp?: string;
  createdAt: string;
  countryName?: string;
  carrierName?: string;
}

export interface ISMSConnectionHistory {
  id: string;
  iccid: string;
  imsi: string;
  country: string;
  carrier: string;
  dateSent: string,
  sentFrom?: string,
  sentTo?: string,
  countryName?: string,
  carrierName?: string,
}

export interface IVoiceConnectionHistory {
  id: string;
  iccid: string;
  imsi: string;
  country: string;
  carrier: string;
  callDate: string,
  callNumber?: string,
  callMinutes?: string,
  countryName?: string,
  carrierName?: string,
}
export interface ISummaryMarketShare {
  id?: string
  carrier: string;
  usage: number;
}
export interface IChartData{
name:string, value: number, pchartValue: string, color: string,
}
export interface IMarketShare {
  warningThreshold?:boolean,
  warningThresholdAccountValue?:any
  totalUsage ?: number | string;
  summary: ISummaryMarketShare[];
  chartData?:IChartData[]
}
export interface IAccountTopBar {
  name: string, value?: string | ReactNode, valueClassName?: string
}
