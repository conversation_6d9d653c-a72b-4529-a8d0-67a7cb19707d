import { Box, Grid } from '@mui/material'
import { <PERSON><PERSON>, <PERSON>ack, Tooltip, Typography } from '@mui/material'
import { useContext, useEffect, useState } from 'react'
import { GrBeacon, GrDisabledOutline } from 'react-icons/gr'
import { SimDetailContex } from './Context/SimDetailContex'
import SendSMSToDevice from './SendSMSToDevice'
import SimAction from './SimAction'
import { IMSI_DETAILS_TABS_INDEXES } from './SimManagement.models'
import CommonAuthwrapper from 'core/CommonAuthWrapper'
import React from 'react'
import { REPOSITORY, ROUTE_PERMISSION } from 'core/utilities/constants'
import { GetAuthorization } from 'PrivateRotes'


interface IDiagnosticsProps {
  selectedTab: undefined | number,
}

const Diagnostics = ({ selectedTab }: IDiagnosticsProps) => {
  const [flushValidation, setFlushValidation] = useState(false);
  const [SessionValidation, setSessionValidation] = useState(false);
  const { date } = useContext(SimDetailContex);

  const gridActionAuth = GetAuthorization([ROUTE_PERMISSION.FLUSH_SESSION], [REPOSITORY.SIM_MANAGEMENT]) || GetAuthorization([ROUTE_PERMISSION.TERMINATE_SESSION], [REPOSITORY.SIM_MANAGEMENT]);
  const gridSMSAuth = GetAuthorization([ROUTE_PERMISSION.SEND_SMS], [REPOSITORY.SIM_MANAGEMENT]);

  useEffect(() => {
    if (
      selectedTab !== undefined &&
      selectedTab !== IMSI_DETAILS_TABS_INDEXES.DIAGNOSTICS
    )
      return;
    if (!date) return;
  }, [date]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        marginTop="15px"
      >
        {gridActionAuth && (<Typography variant="h3">Diagnostics</Typography>)}
        <Box display="flex" justifyContent="flex-end">
          <Stack direction="row" spacing={2}>
            <CommonAuthwrapper
              permission={[ROUTE_PERMISSION.TERMINATE_SESSION]}
              repository={[REPOSITORY.SIM_MANAGEMENT]}
            >
              <Tooltip title="Disconnect Data Session POD" arrow placement="top">
                <Button
                  sx={{
                    textTransform: 'none'
                  }}
                  variant="contained"
                  size="large"
                  color="primary"
                  onClick={() => setSessionValidation(true)}
                  className="account-management-top-bar__button"
                  startIcon={<GrBeacon size={20} />}
                >
                  Disconnect Data Session POD
                </Button>
              </Tooltip>
            </CommonAuthwrapper>
            <CommonAuthwrapper
              permission={[ROUTE_PERMISSION.FLUSH_SESSION]}
              repository={[REPOSITORY.SIM_MANAGEMENT]}
            >
              <Tooltip title="Send Cancel Location (Flush SIM)" arrow placement="top">
                <Button
                  sx={{
                    textTransform: 'none'
                  }}
                  variant="contained"
                  size="large"
                  color="primary"
                  onClick={() => setFlushValidation(true)}
                  className="account-management-top-bar__button"
                  startIcon={<GrDisabledOutline size={20} />}
                >
                  Send Cancel Location
                </Button>
              </Tooltip>
            </CommonAuthwrapper>
          </Stack>
        </Box>
      </Box>
      <Grid container spacing={6}>
        {gridActionAuth && (
          <Grid item xs={12} sm={gridActionAuth && !gridSMSAuth ? 12 : 7}>
            <CommonAuthwrapper
              permission={[]}
              repository={[REPOSITORY.SIM_MANAGEMENT]}
            >
              <SimAction
                flushValidation={flushValidation}
                setFlushValidation={setFlushValidation}
                SessionValidation={SessionValidation}
                setSessionValidation={setSessionValidation}
              />
            </CommonAuthwrapper>
          </Grid>
        )}
        {gridSMSAuth && (
          <Grid item xs={12} sm={!gridActionAuth && gridSMSAuth ? 12 : 5}>
            <CommonAuthwrapper
              permission={[ROUTE_PERMISSION.SEND_SMS]}
              repository={[REPOSITORY.SIM_MANAGEMENT]}
            >
              <SendSMSToDevice />
            </CommonAuthwrapper>
          </Grid>
        )}
      </Grid>
    </Box>
  );
}
export default Diagnostics
