import React, { useEffect, useState } from 'react';

import SimDetailTopBar from 'shared/SimdetailTopBar/SimDetailTopBar';

import { Box, useTheme } from '@mui/material';
import Tabs from '@nv2/nv2-pkg-js-shared-components/lib/Tabs';
import TabPanel from '@nv2/nv2-pkg-js-shared-components/lib/TabPanel';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import FadeIn from 'shared/FadeIn';
import { startMonthAugest } from 'core/utilities/formatDate';
import { useSearchParams } from 'react-router-dom';
import TopBar from 'shared/TopBar';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import { REPOSITORY, ROUTE_PERMISSION } from 'core/utilities/constants';
import { GetAuthorization } from 'PrivateRotes';
import { IMSI_DETAILS_TABS_TABS, ITab } from './SimManagement.models';
import AuditTrail from './AuditTrail';
import ConnectionHistory from './ConnectionHistory';
import { SimDetailContex } from './Context/SimDetailContex';
import VoiceConnectionHistory from './VoiceConnectionHistory';
import SmsConnectionHistory from './SmsConnectionHistory';
import './SimDetails.scss';
import Diagnostics from './Diagnostics';
import SimLiveLocation from './SIMLiveLocation/MapViewer';

export const tabs: ITab = {
  [IMSI_DETAILS_TABS_TABS.CONNECTION_HISTORY]: 0,
  [IMSI_DETAILS_TABS_TABS.SMS_HISTORY]: 1,
  [IMSI_DETAILS_TABS_TABS.VOICE_HISTORY]: 2,
  [IMSI_DETAILS_TABS_TABS.DIAGNOSTICS]: 3,
  [IMSI_DETAILS_TABS_TABS.SIM_LOCATION]: 4,
  [IMSI_DETAILS_TABS_TABS.AUDIT_TRAIL]: 5,
};

export default function SimDetail() {
  const theme = useTheme();
  const [date, setDate] = useState(startMonthAugest);
  const [tabIndex, setTabIndex] = useState<number | undefined>(undefined);
  const [searchParams, setSearchParams] = useSearchParams();

  const selectedNewTab = async (event, newTabValue) => {
    setTabIndex(newTabValue);
    setSearchParams({ tab: Object.keys(tabs)[newTabValue] }, {
      replace: true,
    });
  };

  const tabsConfig = {
    tabItemsConfig: [
      {
        name: 'Connection History',
        disabled: !GetAuthorization(
          [ROUTE_PERMISSION.GET_CONNECTION_HISTORY],
          [REPOSITORY.SIM_MANAGEMENT]),
      },
      {
        name: 'SMS',
        disabled: !GetAuthorization(
          [ROUTE_PERMISSION.GET_SMS_CONNECTION_HISTORY],
          [REPOSITORY.SIM_MANAGEMENT]),
      },
      {
        name: 'Voice',
        disabled: !GetAuthorization(
          [ROUTE_PERMISSION.GET_VOICE_CONNECTION_HISTORY],
          [REPOSITORY.SIM_MANAGEMENT]),
      },
      {
        name: 'Diagnostics',
        disabled: !(GetAuthorization([ROUTE_PERMISSION.FLUSH_SESSION], [REPOSITORY.SIM_MANAGEMENT]) || GetAuthorization([ROUTE_PERMISSION.SEND_SMS], [REPOSITORY.SIM_MANAGEMENT]) || GetAuthorization([ROUTE_PERMISSION.TERMINATE_SESSION], [REPOSITORY.SIM_MANAGEMENT])),
      },
       {
        name: 'Location',
        disabled: !(GetAuthorization([ROUTE_PERMISSION.VIEW_CELL_LOCATION], [REPOSITORY.SIM_MANAGEMENT]) || GetAuthorization([ROUTE_PERMISSION.VIEW_LATEST_LOCATION_DATA], [REPOSITORY.SIM_MANAGEMENT]) || GetAuthorization([ROUTE_PERMISSION.VIEW_LOCATION_DATA], [REPOSITORY.SIM_MANAGEMENT])),
      },
      {
        name: 'Audit Trail',
        disabled: !GetAuthorization(
          [ROUTE_PERMISSION.SIM_AUDIT_LOGS],
          [REPOSITORY.AUDIT]),
      },
    ],
    primaryColor: theme.palette.secondary.main,
  };

  useEffect(() => {
    const tabFromQuery = searchParams.get('tab');
    if (tabFromQuery && tabs[tabFromQuery] !== tabIndex) {
      setTabIndex(tabs[tabFromQuery]);
      setSearchParams({ tab: tabFromQuery }, {
        replace: true,
      });
    } else if (!tabFromQuery) {
      const defaultTabIndex = tabsConfig.tabItemsConfig.findIndex((tab) => !tab.disabled);
      setSearchParams({ tab: Object.keys(tabs)[defaultTabIndex] }, {
        replace: true,
      });
    }
  }, [searchParams]);

  const onChangeDate = (dateParam) => {
    setDate(dateParam);
  };
  const navigateBack = '/';

  const TabComponent = [
    {
      components: <ConnectionHistory selectedTab={tabIndex} />,
      permission: [ROUTE_PERMISSION.GET_CONNECTION_HISTORY],
      repository: [REPOSITORY.SIM_MANAGEMENT],
    },
    {
      components: <SmsConnectionHistory selectedTab={tabIndex} />,
      permission: [ROUTE_PERMISSION.GET_SMS_CONNECTION_HISTORY],
      repository: [REPOSITORY.SIM_MANAGEMENT],
    },
    {
      components: <VoiceConnectionHistory selectedTab={tabIndex} />,
      permission: [ROUTE_PERMISSION.GET_VOICE_CONNECTION_HISTORY],
      repository: [REPOSITORY.SIM_MANAGEMENT],
    },
    {
      components: <Diagnostics selectedTab={tabIndex} />,
      permission: [],
      repository: [REPOSITORY.SIM_MANAGEMENT],
    },
    {
      components: <SimLiveLocation selectedTab={tabIndex} />,
      permission: [],
      repository: [REPOSITORY.SIM_MANAGEMENT],
    },
    {
      components: <AuditTrail selectedTab={tabIndex} />,
      permission: [ROUTE_PERMISSION.SIM_AUDIT_LOGS],
      repository: [REPOSITORY.AUDIT],
    }
  ];

  return (
    <SimDetailContex.Provider value={{ date, onChangeDate }}>
      <div className="sim-details" data-testid="sim-details">
        <Box
          minHeight="87px"
          sx={{
            background: styles.lightColor50,
            marginBottom: '20px',
          }}
        >
          <TopBar navigateTo={`${process.env.NODE_ENV === 'development' ? navigateBack : '/sim-management'}`}>

            <SimDetailTopBar />
          </TopBar>
        </Box>
        <div className="sim-details__main">
          <Box
            sx={{
              '.MuiTabs-flexContainer': {
                '.Mui-disabled': {
                  display: 'none',
                },
              },
            }}
          >
            <Tabs
              tabIndex={tabIndex}
              setTabIndex={setTabIndex}
              selectedNewTab={selectedNewTab}
              selectedTab={tabIndex}
              {...tabsConfig}
            />
            {
              TabComponent?.map((item, index) => (
                <TabPanel index={index} value={tabIndex}>
                  <FadeIn>
                    <CommonAuthwrapper
                      permission={item?.permission}
                      repository={item?.repository}
                      isComponent
                    >
                      {item?.components}
                    </CommonAuthwrapper>
                  </FadeIn>
                </TabPanel>
              ))
            }
          </Box>
        </div>
      </div>
    </SimDetailContex.Provider>
  );
}
