import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { GetAuthorization } from 'PrivateRotes';
import MapViewer from './MapViewer';


jest.mock('./api.service');
jest.mock('core/utilities/toastHelper');
jest.mock('PrivateRotes');
jest.mock('core/utilities/constants', () => ({
  simOrderStatusColorMap: () => ({
    shipped: '#4caf50',
    rejected: '#f44336',
    approved: '#2196f3',
    pending: '#ff9800'
  }),
  ROUTE_PERMISSION: {
    VIEW_LOCATION_DATA: 'View Location History'
  },
  REPOSITORY: {
    SIM_MANAGEMENT: 'SIM Details'
  }
}));
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({ imsi: '123456789012345' }),
  useNavigate: () => jest.fn(),
  useLocation: () => ({ pathname: '/test' }),
}));
jest.mock('core/hooks/useAbortController', () => () => ({
  cancelPreviousRequest: jest.fn(),
  setNewController: () => ({ signal: {} })
}));
jest.mock('core/hooks/useMuiTableSearchParams', () => () => ({
  generateParamsForUrl: jest.fn(() => new URLSearchParams()),
  getParamsFromUrl: jest.fn(),
  defaultPagination: { page: 1, pageSize: 10 },
  defaultSort: { field: 'id', sort: 'asc' },
  initialSearchValue: '',
}));
jest.mock('./LocationHistory', () => () => <div>LocationHistoryMock</div>);
jest.mock('./ViewLatestDataSession', () => () => <div>ViewLatestDataSessionMock</div>);
const mockedGetAuthorization = GetAuthorization as jest.MockedFunction<typeof GetAuthorization>;

describe('MapViewer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockedGetAuthorization.mockClear();
    mockedGetAuthorization.mockReturnValue(true);
  });
  it('renders without crashing and shows mocked children', () => {
    jest.mock('PrivateRotes', () => ({
      GetAuthorization: jest.fn().mockReturnValue(true)
    }));
    render(<MapViewer selectedTab={0} />);
    expect(mockedGetAuthorization).toHaveBeenCalledWith(
        ['View Location History'],
        ['SIM Details']
      );
    expect(screen.getByText('LocationHistoryMock')).toBeInTheDocument();
    expect(screen.getByText('ViewLatestDataSessionMock')).toBeInTheDocument();
  });
});
