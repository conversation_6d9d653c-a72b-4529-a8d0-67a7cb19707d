import { ILocationHistory, ILocationResponse } from './LocationHistory.models';

describe('SIMLiveLocation LocationHistory.models', () => {
  it('should allow creation of a valid ILocationHistory object', () => {
    const obj: ILocationHistory = {
      id: "18151927",
      imsi: "123456789012345",
      firstLocationUpdate: "2025-07-03T19:44:27Z",
      lastLocationUpdate: "2025-07-03T19:45:25Z",
      mscGlobalTitle: "447785014052",
      vlrGlobalTitle: "447785014052",
      sgsnGlobalTitle: "447785012624",
      draMobileCountryCode: null,
      draMobileNetworkCode: null,
      networkName: "Vodafone Ltd",
      countryName: "United Kingdom",
      continentName: "Europe",
      simProvider: "T-Mobile USA Inc",
      countryFlag: "gb.svg"
    };
    expect(obj).toBeDefined();
    expect(obj.imsi).toBe('123456789012345');
  });

  it('should allow creation of a valid ILocationResponse object', () => {
    const response: ILocationResponse = {
      page: 1,
      pageSize: 10,
      lastPage: 2,
      totalCount: 20,
      results: []
    };
    expect(response.page).toBe(1);
    expect(response.results).toEqual([]);
  });
});
