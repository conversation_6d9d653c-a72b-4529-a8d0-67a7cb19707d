import { coreAxios } from "core/services/HTTPService";
import { IPagination } from "features/models";
import { ILocationHistory } from "./LocationHistory.models";


export const locationHistoryList = (
  imsi: string,
  signal: AbortSignal,
  page: number,
  pageSize: number,
  field?: string,
  ordering?: string,
  search?: string,
) => {
  let url = `/glass/sim/location/${imsi}/history?page=${page}&page_size=${pageSize}`;
  if (ordering === 'asc') url += `&ordering=${field}`;
  if (ordering === 'desc') url += `&ordering=-${field}`;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  if (search) url += `&search=${search}`;
  return coreAxios.get<IPagination<ILocationHistory[]>>(url, signal ? { signal } : {}); 
  
};


export const getLatestLocation = (
  imsi: string,
  signal: AbortSignal,
) => {
  const url = `/glass/sim/location/${imsi}/latest`;
  const response = coreAxios.get<ILocationHistory>(url, signal ? { signal } : {});
  return response;
}

export const getSIMCurrentLocation = (
  imsi: string,
  signal: AbortSignal,
) => {
  const url = `/glass/sim/location/${imsi}/cell`;
  const response = coreAxios.get<ILocationHistory>(url, signal ? { signal } : {});
  return response;
}