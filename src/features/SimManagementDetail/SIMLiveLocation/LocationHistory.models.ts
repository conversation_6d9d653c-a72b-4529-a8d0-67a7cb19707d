
export interface ILocationHistory {
  id: string;
  imsi: string;
  firstLocationUpdate: string; // ISO 8601 timestamp
  lastLocationUpdate: string;  // ISO 8601 timestamp
  mscGlobalTitle: string;
  vlrGlobalTitle: string;
  sgsnGlobalTitle: string;
  draMobileCountryCode: string | null;
  draMobileNetworkCode: string | null;
  networkName: string;
  countryName: string;
  continentName: string;
  simProvider: string | null;
  countryFlag: string | null;
}


export interface ILocationResponse {
  page: number;
  pageSize: number;
  lastPage: number;
  totalCount: number;
  results?: ILocationHistory[] | null;
}
