import LocationHistoryColumns from './LocationHistoryColumns';
import { render } from '@testing-library/react';

describe('LocationHistoryColumns', () => {
  it('should return an array of column definitions', () => {
    const columns = LocationHistoryColumns();
    expect(Array.isArray(columns)).toBe(true);
    expect(columns.length).toBeGreaterThan(0);
    columns.forEach(col => {
      expect(col).toHaveProperty('headerName');
      expect(col).toHaveProperty('field');
      expect(col).toHaveProperty('width');
      expect(typeof col?.renderCell).toBe('function');
    });
  });

  it('should render cell for a column', () => {
    const columns = LocationHistoryColumns();
    const idCol = columns.find(col => col?.field === 'id');
    expect(idCol).toBeDefined();
    if (idCol) {
      const { container } = render(idCol.renderCell({ row: { id: 'test-id' } }));
      expect(container.textContent).toBe('test-id');
    }
  });
});
