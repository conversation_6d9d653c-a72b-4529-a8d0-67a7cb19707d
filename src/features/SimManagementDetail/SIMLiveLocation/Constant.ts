export const famousStreetViews = [
  { name: "Times Square, New York", latitude: 40.7580, longitude: -73.9855 },
  { name: "Hollywood Boulevard, Los Angeles", latitude: 34.1016, longitude: -118.3269 },
  { name: "The Strip, Las Vegas", latitude: 36.1147, longitude: -115.1728 },
  { name: "Michigan Avenue, Chicago", latitude: 41.8916, longitude: -87.6244 },
  { name: "Bourbon Street, New Orleans", latitude: 29.9584, longitude: -90.0644 },
  { name: "Yonge Street, Toronto", latitude: 43.6487, longitude: -79.3854 },
  { name: "Robson Street, Vancouver", latitude: 49.2856, longitude: -123.1210 },
  { name: "Avenida Paulista, São Paulo", latitude: -23.5617, longitude: -46.6559 },
  { name: "Calle 7, Bogotá", latitude: 4.5981, longitude: -74.0760 },
  { name: "Caminito, Buenos Aires", latitude: -34.6346, longitude: -58.3633 },
  { name: "<PERSON><PERSON><PERSON> <PERSON>, Buenos Aires", latitude: -34.5560, longitude: -58.4324 },
  { name: "Av. <PERSON>, Quito", latitude: -0.2039, longitude: -78.4921 },
  { name: "Av. Larco, Lima", latitude: -12.1321, longitude: -77.0290 },
  { name: "La Rambla, Barcelona", latitude: 41.3809, longitude: 2.1732 },
  { name: "Champs-Élysées, Paris", latitude: 48.8698, longitude: 2.3078 },
  { name: "Oxford Street, London", latitude: 51.5154, longitude: -0.1410 },
  { name: "Strøget, Copenhagen", latitude: 55.6796, longitude: 12.5771 },
  { name: "Nevsky Prospect, St. Petersburg", latitude: 59.9343, longitude: 30.3351 },
  { name: "Via del Corso, Rome", latitude: 41.9010, longitude: 12.4808 },
  { name: "Kärntner Straße, Vienna", latitude: 48.2044, longitude: 16.3703 },
  { name: "Königsallee, Düsseldorf", latitude: 51.2257, longitude: 6.7834 },
  { name: "Gran Vía, Madrid", latitude: 40.4203, longitude: -3.7058 },
  { name: "Istiklal Avenue, Istanbul", latitude: 41.0366, longitude: 28.9851 },
  { name: "Andrassy Avenue, Budapest", latitude: 47.5034, longitude: 19.0696 },
  { name: "Nanjing Road, Shanghai", latitude: 31.2336, longitude: 121.4750 },
  { name: "Orchard Road, Singapore", latitude: 1.3048, longitude: 103.8318 },
  { name: "Myeongdong, Seoul", latitude: 37.5639, longitude: 126.9827 },
  { name: "Connaught Place, Delhi", latitude: 28.6315, longitude: 77.2167 },
  { name: "Marine Drive, Mumbai", latitude: 18.9430, longitude: 72.8235 },
  { name: "Shibuya Crossing, Tokyo", latitude: 35.6595, longitude: 139.7005 },
  { name: "Nathan Road, Hong Kong", latitude: 22.3045, longitude: 114.1722 },
  { name: "Thamel, Kathmandu", latitude: 27.7166, longitude: 85.3123 },
  { name: "Jalan Malioboro, Yogyakarta", latitude: -7.7923, longitude: 110.3658 },
  { name: "Chungking Mansions, Hong Kong", latitude: 22.2965, longitude: 114.1724 },
  { name: "Vilakazi Street, Soweto", latitude: -26.2348, longitude: 27.9092 },
  { name: "Avenue Mohamed V, Casablanca", latitude: 33.5932, longitude: -7.6171 },
  { name: "Moi Avenue, Nairobi", latitude: -1.2833, longitude: 36.8227 },
  { name: "Long Street, Cape Town", latitude: -33.9249, longitude: 18.4227 },
  { name: "Adly Street, Cairo", latitude: 30.0506, longitude: 31.2396 },
  { name: "George Street, Sydney", latitude: -33.8675, longitude: 151.2070 },
  { name: "Queen Street, Auckland", latitude: -36.8485, longitude: 174.7633 },
  { name: "Bourke Street, Melbourne", latitude: -37.8141, longitude: 144.9633 },
  { name: "Cuba Street, Wellington", latitude: -41.2910, longitude: 174.7758 },
  { name: "King Fahd Road, Riyadh", latitude: 24.7117, longitude: 46.6753 },
  { name: "Sheikh Zayed Road, Dubai", latitude: 25.2001, longitude: 55.2719 },
  { name: "Al-Mutanabbi Street, Baghdad", latitude: 33.3306, longitude: 44.4009 },
  { name: "Azadi Street, Tehran", latitude: 35.6997, longitude: 51.3370 },
  { name: "Hamra Street, Beirut", latitude: 33.8966, longitude: 35.4787 },
  { name: "5th Avenue, Playa del Carmen", latitude: 20.6296, longitude: -87.0739 },
  { name: "Avenida Central, San José", latitude: 9.9347, longitude: -84.0875 },
  { name: "Calle El Conde, Santo Domingo", latitude: 18.4736, longitude: -69.8837 },
  { name: "The Mall, Washington D.C.", latitude: 38.8895, longitude: -77.0353 },
  { name: "The Royal Mile, Edinburgh", latitude: 55.9509, longitude: -3.1902 },
  { name: "Broadway, New York", latitude: 40.7590, longitude: -73.9845 },
  { name: "Fremont Street, Las Vegas", latitude: 36.1700, longitude: -115.1446 },
  { name: "Rodeo Drive, Beverly Hills", latitude: 34.0692, longitude: -118.4037 },
  { name: "Fifth Avenue, New York", latitude: 40.7753, longitude: -73.9654 },
  { name: "Unter den Linden, Berlin", latitude: 52.5163, longitude: 13.3807 },
  { name: "Passeig de Gracia, Barcelona", latitude: 41.3917, longitude: 2.1649 },
  { name: "Tverskaya Street, Moscow", latitude: 55.7633, longitude: 37.6040 },
  { name: "Ginza, Tokyo", latitude: 35.6717, longitude: 139.7650 },
  { name: "Akihabara, Tokyo", latitude: 35.6984, longitude: 139.7730 },
  { name: "Tsim Sha Tsui Promenade, Hong Kong", latitude: 22.2936, longitude: 114.1719 },
  { name: "Karl Johans gate, Oslo", latitude: 59.9133, longitude: 10.7400 },
  { name: "Ban Jelacic Square, Zagreb", latitude: 45.8120, longitude: 15.9785 },
  { name: "Paseo de la Reforma, Mexico City", latitude: 19.4326, longitude: -99.1332 },
  { name: "Khreshchatyk Street, Kyiv", latitude: 50.4477, longitude: 30.5205 },
  { name: "Al-Khazneh Trail, Petra", latitude: 30.3220, longitude: 35.4519 }
];

export const locationHistoryData = [
  {
    "id": "123456",
    "imsi": "234580000000001",
    "firstLocationUpdate": "2025-06-01T12:12:15Z",
    "lastLocationUpdate": "2025-05-01T08:00:00Z",
    "mscGlobalTitle": "447624000001",
    "vlrGlobalTitle": "447624000002",
    "sgsnGlobalTitle": "447624000003",
    "networkName": "T-Mobile USA Inc",
    "countryName": "United States",
    "continentName": "North America",
    'simProvider': "T-Mobile USA Inc",
    "countryFlag": null
  },
  {
    "id": "123457",
    "imsi": "234580000000001",
    "firstLocationUpdate": "2025-06-01T12:12:18Z",
    "lastLocationUpdate": "2025-05-01T08:00:00Z",
    "mscGlobalTitle": "447624000001",
    "vlrGlobalTitle": "447624000002",
    "sgsnGlobalTitle": "447624000003",
    "networkName": "T-Mobile USA Inc",
    "countryName": "United States",
    'simProvider': "T-Mobile USA Inc",
    "continentName": "North America",
    "countryFlag": null
  },
  {
    "id": "14099673",
    "imsi": "234588570050716",
    "firstLocationUpdate": "2025-06-07T11:41:58Z",
    "lastLocationUpdate": "2025-06-07T11:41:58Z",
    "mscGlobalTitle": "447953710127",
    "vlrGlobalTitle": "447953710127",
    "sgsnGlobalTitle": null,
    "networkName": "Everything Everywhere Limited",
    "countryName": "United Kingdom",
    'simProvider': "T-Mobile USA Inc",
    "continentName": "Europe",
    "countryFlag": "gb.svg"
  }
];

export const latestDataSession = {
  "id": "123",
  "imsi": "234580000000001",
  "firstLocationUpdate": "2025-06-07T11:41:58Z",
  "lastLocationUpdate": "2026-06-07T11:41:58Z",
  "mscGlobalTitle": "447624000001",
  "vlrGlobalTitle": "447624000001",
  "sgsnGlobalTitle": "447624000003",
  "draMobileCountryCode": "234",
  "draMobileNetworkCode": "58",
  "networkName": "T-Mobile USA Inc",
  "countryName": "United States",
  "continentName": "North America",
  "countryFlag": "mp.svg"
};

export const cellLocation = {
  "mcc": "234",
  "mnc": "30",
  "lac": "21821",
  "cell": "3958785",
  "lat": 54.67041833333333,
  "lon": -1.2676033333333334,
  "range": 3867,
  "source": "https://opencellid.org/"
}