import { getSIMCurrentLocation, getLatestLocation, locationHistoryList } from './api.service';
import { coreAxios } from 'core/services/HTTPService';

describe('SIMLiveLocation API Service', () => {
  const imsi = '123456789012345';
  const signal = {} as AbortSignal;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call locationHistoryList with correct params (asc ordering)', () => {
    const getSpy = jest.spyOn(coreAxios, 'get').mockResolvedValue({});
    locationHistoryList(imsi, signal, 1, 10, 'timestamp', 'asc', 'searchTerm');
    expect(getSpy).toHaveBeenCalledWith(
      expect.stringContaining(`/glass/sim/location/${imsi}/history?page=1&page_size=10&ordering=timestamp&search=searchTerm`),
      { signal }
    );
  });

  it('should call locationHistoryList with correct params (desc ordering)', () => {
    const getSpy = jest.spyOn(coreAxios, 'get').mockResolvedValue({});
    locationHistoryList(imsi, signal, 2, 20, 'timestamp', 'desc');
    expect(getSpy).toHaveBeenCalledWith(
      expect.stringContaining(`/glass/sim/location/${imsi}/history?page=2&page_size=20&ordering=-timestamp`),
      { signal }
    );
  });

  it('should call getLatestLocation with correct url', () => {
    const getSpy = jest.spyOn(coreAxios, 'get').mockResolvedValue({});
    getLatestLocation(imsi, signal);
    expect(getSpy).toHaveBeenCalledWith(`/glass/sim/location/${imsi}/latest`, { signal });
  });

  it('should call getSIMCurrentLocation with correct url', () => {
    const postSpy = jest.spyOn(coreAxios, 'get').mockResolvedValue({});
    getSIMCurrentLocation(imsi, signal);
    expect(postSpy).toHaveBeenCalledWith(`/glass/sim/location/${imsi}/cell`, { signal });
  });
});
