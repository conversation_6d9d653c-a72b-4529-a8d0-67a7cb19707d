import { Box } from '@mui/material';
import { formateDateWithTimeZone } from 'core/utilities/formatDate';
import React from 'react';

const LocationHistoryColumns = () => {
  return [
    {
      headerName: 'SIM ID',
      field: 'id',
      width: 100,
      renderCell: ({ row }) => (
        <Box>{row.id}</Box>
      ),
    },
    {
      headerName: 'IMSI',
      field: 'imsi',
      width: 170,
      renderCell: ({ row }) => (
        <Box>{row.imsi}</Box>
      ),
    },
    {
      headerName: 'Network Name',
      field: 'networkName',
      width: 250,
      renderCell: ({ row }) => (
        <Box>{row.networkName}</Box>
      ),
    },
    {
      headerName: 'Country',
      field: 'countryName',
      width: 170,
      renderCell: ({ row }) => (
        <Box>{row.countryName}</Box>
      ),
    },
    {
      headerName: 'Continent',
      field: 'continentName',
      width: 170,
      renderCell: ({ row }) => (
        <Box>{row.continentName}</Box>
      ),
    },
    {
      headerName: 'First Location Update',
      field: 'firstLocationUpdate',
      width: 170,
      renderCell: ({ row }) => (
        <Box>{formateDateWithTimeZone(row.firstLocationUpdate)}</Box>
      ),
    },
    {
      headerName: 'Last Location Update',
      field: 'lastLocationUpdate',
      width: 170,
      renderCell: ({ row }) => (
        <Box>{formateDateWithTimeZone(row.lastLocationUpdate)}</Box>
      ),
    },
    {
      headerName: 'MSC Global Title',
      field: 'mscGlobalTitle',
      width: 170,
      renderCell: ({ row }) => (
        <Box>{row.mscGlobalTitle}</Box>
      ),
    },
    {
      headerName: 'VLR Global Title',
      field: 'vlrGlobalTitle',
      width: 150,
      renderCell: ({ row }) => (
        <Box>{row.vlrGlobalTitle}</Box>
      ),
    },
    {
      headerName: 'SGSN Global Title',
      field: 'sgsnGlobalTitle',
      width: 150,
      renderCell: ({ row }) => (
        <Box>{row.sgsnGlobalTitle}</Box>
      ),
    }
  ];
};

export default LocationHistoryColumns;
