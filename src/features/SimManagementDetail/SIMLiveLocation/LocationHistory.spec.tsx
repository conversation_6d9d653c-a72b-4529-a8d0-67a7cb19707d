import { render, screen } from '@testing-library/react';
import LocationHistory from './LocationHistory';
import React from 'react';
const defaultPagination = { page: 1, pageSize: 10 };
const defaultSort = { field: 'id', sort: 'asc' };
const locationHistory = [
  {
    id: "18151927",
    imsi: "234588570010201",
    firstLocationUpdate: "2025-07-03T19:44:27Z",
    lastLocationUpdate: "2025-07-03T19:45:25Z",
    mscGlobalTitle: "447785014052",
    vlrGlobalTitle: "447785014052",
    sgsnGlobalTitle: "447785012624",
    draMobileCountryCode: null,
    draMobileNetworkCode: null,
    networkName: "Vodafone Ltd",
    countryName: "United Kingdom",
    continentName: "Europe",
    simProvider: "T-Mobile USA Inc",
    countryFlag: "gb.svg"
  }
];

describe('LocationHistory', () => {
  it('renders MuiTableProvider and MuiTable', () => {
    render(
      <LocationHistory
        defaultPagination={defaultPagination}
        defaultSort={defaultSort}
        locationHistory={locationHistory}
        loading={false}
        rowCount={1}
        onChange={jest.fn()}
        noData={{}}
      />
    );
    expect(screen.getByTestId('account-user-log')).toBeInTheDocument();
  });
});
