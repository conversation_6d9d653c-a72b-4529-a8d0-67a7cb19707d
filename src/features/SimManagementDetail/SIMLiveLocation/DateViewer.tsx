
import React from 'react';
import { Typography} from "@mui/material";
import { getRelativeTime } from "core/utilities/formatDate";

const DateViewer = ({ label, date, sx }: { label: string, date?: string, sx?: any }) => {
    return (
        <Typography variant="body1" sx={{ ...sx }}>
            {label}
            <br />
            {date ? (
                <strong>
                    {new Date(date).toLocaleString()}
                    <br/>
                    <span style={{ color: "#5E35B1" }}>
                        ({getRelativeTime(date)})
                    </span>
                </strong>
            ) : (
                <span style={{ color: "#5E35B1" }}>-</span>
            )}
        </Typography>
    )
}


export default DateViewer;