import { Grid, Typography, Button, Skeleton, Tooltip } from "@mui/material";
import <PERSON><PERSON><PERSON><PERSON> from "./DateViewer";
import { CiFlag1 } from "react-icons/ci";
import React from "react";

const ViewLatestDataSession = ({ handleRefreshLocation, dataSession, loading }: any) => {
    const {
        networkName,
        countryFlag,
        firstLocationUpdate,
        lastLocationUpdate,
        mscGlobalTitle,
        vlrGlobalTitle,
        sgsnGlobalTitle,
    } = dataSession;



   return loading ? (
        <Grid container spacing={11.5} alignItems="center" justifyContent="space-between">
            {/* Country Flag and Network Name Skeleton */}
            <Grid item xs={12} sm={6} md={6} display="flex" alignItems="center">
                <Skeleton variant="rectangular" width={32} height={24} sx={{ mr: 1 }} />
                <Skeleton variant="text" width={120} height={32} />
            </Grid>
            <Grid item xs={12} sm={6} md={6} textAlign="right">
                <Skeleton variant="rectangular" width={80} height={36} sx={{ borderRadius: 1, display: 'inline-block' }} />
            </Grid>

            {/* Location Update(s) Skeleton */}
            <Grid item xs={12} md={6}>
                <Skeleton variant="text" width={160} height={28} />
                <Skeleton variant="text" width={220} height={24} />
            </Grid>
            <Grid item xs={12} md={6}>
                <Skeleton variant="text" width={160} height={28} />
                <Skeleton variant="text" width={220} height={24} />
            </Grid>

            {/* Global Titles Skeleton */}
            <Grid item xs={12}>
                <Skeleton variant="text" width={160} height={28} />
                <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                        <Skeleton variant="text" width={100} height={24} />
                        <Skeleton variant="text" width={80} height={20} />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                        <Skeleton variant="text" width={100} height={24} />
                        <Skeleton variant="text" width={80} height={20} />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                        <Skeleton variant="text" width={100} height={24} />
                        <Skeleton variant="text" width={80} height={20} />
                    </Grid>
                </Grid>
            </Grid>

            {/* DRAs Skeleton */}
            <Grid item xs={12}>
                <Skeleton variant="text" width={160} height={28} />
                <Grid container spacing={2}>
                    <Grid item xs={6} sm={3}>
                        <Skeleton variant="text" width={80} height={20} />
                        <Skeleton variant="text" width={60} height={20} />
                    </Grid>
                    <Grid item xs={6} sm={3}>
                        <Skeleton variant="text" width={80} height={20} />
                        <Skeleton variant="text" width={60} height={20} />
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    ) : (
        <Grid container spacing={11.5} alignItems="center" justifyContent="space-between">
            {/* Country Flag and Network Name */}
            <Grid item xs={12} sm={6} md={6} display="flex" alignItems="center">
                {countryFlag ? (
                    <img
                        src={`https://cdn.jsdelivr.net/gh/lipis/flag-icons/flags/4x3/${countryFlag}`}
                        alt="flag"
                        width={24}
                        height={16}
                        style={{ marginRight: 8 }}
                    />) :
                    (<CiFlag1 style={{ fontSize: "20px" }} />)}
                <Typography fontWeight="bold">
                    {networkName}
                </Typography>
            </Grid>

            <Grid item xs={12} sm={6} md={6} textAlign="right">
                <Tooltip title="Refresh SIM Location" arrow placement='top'>
                    <Button
                        sx={{ p: '0px 25px', backgroundColor: '#ebe3f6', border: '0px' }}
                        variant="outlined"
                        onClick={handleRefreshLocation}
                    >
                        Refresh
                    </Button>
                </Tooltip>
            </Grid>
            <Grid item xs={12}>
                <Grid container>
                    <Grid item xs={12} md={3}>
                        <Typography >
                            <strong>
                                Location Update(s)
                            </strong>
                        </Typography>
                    </Grid>
                    <Grid item xs={12} md={9}>
                        <Grid container>
                            <Grid item xs={12} md={8}>
                                <DateViewer label="First Location" date={firstLocationUpdate} />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <DateViewer label="Last Location" date={lastLocationUpdate} />
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>
            {/* Global Titles */}
            <Grid item xs={12}>
                <Grid container>
                    <Grid item xs={12} sm={3}>
                        <Typography>
                            <strong>
                                Global Title(s)
                            </strong>
                        </Typography>
                    </Grid>
                    <Grid item xs={12} sm={9}>
                        <Grid container>
                            <Grid item xs={12} sm={6} md={8} xl={8} sx={{ mb: 2 }}>
                                <Grid container>
                                    <Grid item xs={12} sm={12} md={6} xl={6} sx={{ mb: 2 }}>
                                        <Typography variant="body1">
                                            MSC
                                            <br />
                                            <strong >
                                                {mscGlobalTitle || "-"}
                                            </strong>
                                        </Typography>
                                    </Grid>

                                    <Grid item xs={12} sm={12} md={6} xl={6}>
                                        <Typography variant="body1">
                                            VLR
                                            <br />
                                            <strong>
                                                {vlrGlobalTitle || "-"}
                                            </strong>
                                        </Typography>
                                    </Grid>
                                </Grid>
                            </Grid>


                            <Grid item xs={12} sm={6} md={4} xl={4}>
                                <Typography variant="body1">
                                    SGSN
                                    <br />
                                    <strong>
                                        {sgsnGlobalTitle || "-"}
                                    </strong>
                                </Typography>
                            </Grid>

                        </Grid>
                    </Grid>
                </Grid>

            </Grid>
            {/* DRAs */}
            <Grid item xs={12}>
                <Grid container>
                    <Grid item xs={12} sm={3}>
                        <Typography>
                            <strong>
                                Provider
                            </strong>
                        </Typography>
                    </Grid>
                    <Grid item xs={12} sm={9}>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={3} md={3}>
                                <Typography variant="body1">
                                    SIM Provider
                                    <br />
                                    <strong>{"-"}</strong>
                                </Typography>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default ViewLatestDataSession;
