import React from 'react';
import { Box } from '@mui/material';
import MuiTable from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';
import { MuiTableProvider } from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable/MuiTableContext';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import { useAppContext } from 'AppContextProvider';
import LocationHistoryColumns from './LocationHistoryColumns';
import { ILocationHistory } from './LocationHistory.models';

interface LocationHistoryProps {
    defaultPagination:object,
    defaultSort: object,
    initialSearchValue?: string,
    locationHistory: ILocationHistory[],
    loading?: boolean,
    rowCount?: number,
    onChange: ({ page, pageSize }: { page: any; pageSize: any; }, { field, sort }: { field: any; sort: any; }, search: any) => void,
    noData: object,
}

const LocationHistory: React.FC<LocationHistoryProps> = ({
    defaultPagination,
    defaultSort,
    initialSearchValue,
    locationHistory,
    loading,
    rowCount,
    onChange,
    noData
}) => {
    const { primaryColor, getBrandColors } = useAppContext();


    return (
        <Box
            sx={{
                background: styles.lightColor50,
                '& .MuiDataGrid-columnHeaders': {
                    backgroundColor: `${styles.lightColor100} !important`,
                },
                '& .MuiDataGrid-virtualScrollerContent': {
                    minHeight: '160px !important',
                },
                '& .interactions__actions': {
                    justifyContent: 'flex-start',
                    width: '100%',
                },
            }}
            data-testid="account-user-log"
        >
            <MuiTableProvider
                defaultPagination={defaultPagination}
                onChange={onChange}
                onChangePagination={onChange}
                onChangeSearch={onChange}
                initialSearchValue={initialSearchValue}
                onChangeSort={onChange}
                defaultSort={defaultSort}
                isVisibleSearchInput={false}
            >
                <MuiTable
                    sx={{
                        '& .MuiCheckbox-colorPrimary': {
                            color: '#C6C7D5',
                        },
                        '& .Mui-disabled ': {
                            opacity: '0.3',
                        },
                    }}
                    rows={locationHistory}
                    columns={LocationHistoryColumns()}
                    loading={loading}
                    rowCount={rowCount}
                    primaryColor={primaryColor as string}
                    getCurrentThemeColors={getBrandColors}
                    showFirstLastPageButtons
                    isVisibleSearchInput={false}
                    getRowId={(row) => row.id}
                    noDataConfig={noData}
                    rowHeight={73}
                    hideFooter={!(locationHistory?.length > 0)}
                />
            </MuiTableProvider>
        </Box>
    );

}

export default LocationHistory;
