import { render, screen, fireEvent } from '@testing-library/react';
import ViewLatestDataSession from './ViewLatestDataSession';
import React from 'react';

describe('ViewLatestDataSession', () => {
  const mockHandleRefresh = jest.fn();
  const dataSession = {
    networkName: 'Test Network',
    countryFlag: 'us.svg',
    firstLocationUpdate: '2025-07-02T12:00:00Z',
    lastLocationUpdate: '2025-07-02T13:00:00Z',
    mscGlobalTitle: '447624000001',
    vlrGlobalTitle: '447624000002',
    sgsnGlobalTitle: '447624000003',
    draMobileCountryCode: '234',
    draMobileNetworkCode: '58',
  };

  it('renders loading state', () => {
    render(<ViewLatestDataSession handleRefreshLocation={mockHandleRefresh} dataSession={dataSession} loading={true} />);
    // Should not render the Refresh button while loading
    expect(screen.queryByText('Refresh')).not.toBeInTheDocument();
    // Should render at least one skeleton
    expect(document.querySelector('.MuiSkeleton-root')).toBeInTheDocument();
  });

  it('renders data session info and refresh button', () => {
    render(<ViewLatestDataSession handleRefreshLocation={mockHandleRefresh} dataSession={dataSession} loading={false} />);
    expect(screen.getByText('Test Network')).toBeInTheDocument();
    expect(screen.getByText('Refresh')).toBeInTheDocument();
    const img = screen.getByAltText('flag');
    expect(img).toHaveAttribute('src', expect.stringContaining('us.svg'));
    fireEvent.click(screen.getByText('Refresh'));
    expect(mockHandleRefresh).toHaveBeenCalled();
  });

  it('does not render refresh button when loading', () => {
    render(<ViewLatestDataSession handleRefreshLocation={mockHandleRefresh} dataSession={dataSession} loading={true} />);
    expect(screen.queryByText('Refresh')).not.toBeInTheDocument();
  });

  it('renders placeholders (skeletons) when loading', () => {
    render(<ViewLatestDataSession handleRefreshLocation={mockHandleRefresh} dataSession={dataSession} loading={true} />);
    // Check for at least one skeleton element
    expect(document.querySelector('.MuiSkeleton-root')).toBeInTheDocument();
  });

  it('renders fallback dashes for missing data', () => {
    const partialSession = { ...dataSession, mscGlobalTitle: undefined, vlrGlobalTitle: undefined, sgsnGlobalTitle: undefined, draMobileCountryCode: undefined, draMobileNetworkCode: undefined };
    render(<ViewLatestDataSession handleRefreshLocation={mockHandleRefresh} dataSession={partialSession} loading={false} />);
    expect(screen.getAllByText('-').length).toBeGreaterThan(0);
  });
});
