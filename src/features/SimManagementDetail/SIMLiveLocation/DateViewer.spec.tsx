import React from 'react';
import { render, screen } from '@testing-library/react';
import Date<PERSON>iewer from './DateViewer';

jest.mock('core/utilities/formatDate', () => ({
  getRelativeTime: jest.fn(() => '2 hours ago'),
}));

describe('DateViewer', () => {
  it('renders label and formatted date when date is provided', () => {
    render(<DateViewer label="Test Label" date="2025-07-01T12:00:00Z" />);
    expect(screen.getByText('Test Label')).toBeInTheDocument();
    expect(screen.getByText(/2 hours ago/)).toBeInTheDocument();
    expect(screen.getByText(/2025/)).toBeInTheDocument();
  });

  it('renders label and dash when date is not provided', () => {
    render(<DateViewer label="No Date" />);
    expect(screen.getByText('No Date')).toBeInTheDocument();
    expect(screen.getByText('-')).toBeInTheDocument();
  });

  it('applies sx prop to Typography', () => {
    render(<DateViewer label="Styled" date="2025-07-01T12:00:00Z" sx={{ color: 'red' }} />);
    const typography = screen.getByText('Styled').closest('p');
    expect(typography).toHaveStyle('color: red');
  });
});
