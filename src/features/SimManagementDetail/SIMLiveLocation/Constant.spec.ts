import { famousStreetViews, locationHistoryData, latestDataSession, cellLocation } from './Constant';

describe('SIMLiveLocation Constants', () => {
  it('should have a non-empty famousStreetViews array', () => {
    expect(Array.isArray(famousStreetViews)).toBe(true);
    expect(famousStreetViews.length).toBeGreaterThan(0);
    famousStreetViews.forEach(item => {
      expect(item).toHaveProperty('name');
      expect(item).toHaveProperty('latitude');
      expect(item).toHaveProperty('longitude');
    });
  });

  it('should have a valid locationHistoryData array', () => {
    expect(Array.isArray(locationHistoryData)).toBe(true);
    expect(locationHistoryData.length).toBeGreaterThan(0);
    locationHistoryData.forEach(item => {
      expect(item).toHaveProperty('id');
      expect(item).toHaveProperty('imsi');
      expect(item).toHaveProperty('firstLocationUpdate');
      expect(item).toHaveProperty('lastLocationUpdate');
    });
  });

  it('should have a valid latestDataSession object', () => {
    expect(latestDataSession).toHaveProperty('id');
    expect(latestDataSession).toHaveProperty('imsi');
    expect(latestDataSession).toHaveProperty('firstLocationUpdate');
    expect(latestDataSession).toHaveProperty('lastLocationUpdate');
  });

  it('should have a valid cellLocation object', () => {
    expect(cellLocation).toHaveProperty('mcc');
    expect(cellLocation).toHaveProperty('mnc');
    expect(cellLocation).toHaveProperty('lac');
    expect(cellLocation).toHaveProperty('cell');
    expect(cellLocation).toHaveProperty('lat');
    expect(cellLocation).toHaveProperty('lon');
    expect(cellLocation).toHaveProperty('range');
    expect(cellLocation).toHaveProperty('source');
  });
});
