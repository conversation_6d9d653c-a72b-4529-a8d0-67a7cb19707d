import {
  IAccount,
  IRatePlan,
} from 'features/SimManagement/SimManagement.models';

const imsiAllocationsMockData = [
  {
    id: 1,
    title: 'Tesla',
    quantity: 380,
    provider: 'NR',
    formFactor: 'MICRO',
    createdAt: '2022-12-31T11:55:13',
    accountId: 1,
    rangeId: 1,
    accountName: 'Account One',
    country: 'GB',
    logoUrl: 'https://example.com/logo1.png',
  },
  {
    id: 2,
    title: 'Reference',
    quantity: 380,
    provider: 'NR',
    formFactor: 'MICRO',
    createdAt: '2022-12-31T11:55:13',
    accountId: 1,
    rangeId: 1,
    accountName: 'Account One',
    country: 'Country A',
    logoUrl: 'https://example.com/logo2.png',
  },
  {
    id: 3,
    title: 'Reference',
    quantity: 380,
    provider: 'NR',
    formFactor: 'MICRO',
    createdAt: '2022-12-31T11:55:13',
    accountId: 1,
    rangeId: 1,
    accountName: 'Account One',
    country: 'Country A',
    logoUrl: 'https://example.com/logo3.png',
  },
];

export const accountsMockData: IAccount[] = [
  {
    id: 100,
    name: 'Kyivsta',
    logoUrl: 'https://example.com/icon',
    status: { ref: 'Active', name: 'Active' },
    agreementNumber: '25m',
    currency: 'GBP',
    country: 'GB',
    contactName: '',
    email: '',
    phone: '',
    address1: '',
    address2: '',
    stateRegion: '',
    city: '',
    postcode: '',
    industryVertical: { ref: 'Telecommunications', name: 'Telecommunications' },
    salesChannel: { ref: 'Wholesale', name: 'Wholesale' },
    productTypes: [{ ref: 'National Roaming', name: 'National Roaming' }],
    defaultRatePlan: {
      id: 1,
      name: 'PAYG',
      accessFee: 2.33,
      currency: 'GBP',
      isDefault: true,
    },
    salesPerson: 'Brooklyn Simmons',
  },
];

export const mockRatePlansListData: IRatePlan[] = [
  {
    id: 1,
    name: 'PAYG',
    accessFee: 2.33,
    currency: 'GBP',
    isDefault: true,
  },
  {
    id: 2,
    name: 'Plan B',
    accessFee: 0.0033,
    currency: 'GBP',
    isDefault: false,
  },
  {
    id: 3,
    name: 'Plan C',
    accessFee: 0.0022,
    currency: 'GBP',
    isDefault: false,
  },
];

export const simCardsUserMockData = {
  page: 1,
  pageSize: 5,
  lastPage: 1,
  totalCount: 5,
  results: [
    {
      id: 10207,
      iccid: '8944538532046555961',
      msisdn: '883200000106906',
      imsi: '234588570006596',
      type: 'STANDARD',
      allocationReference: '12345',
      allocationDate: '2023-05-02T07:37:38.979344',
      simStatus: 'Ready for Activation',
      usage: 0,
    },
    {
      id: 102508,
      iccid: '8944538532046597104',
      msisdn: '883200000111020',
      imsi: '234588570010710',
      type: 'NANO',
      allocationReference: '8944538531005850000',
      allocationDate: '2023-05-03T09:20:05.700522',
      simStatus: 'Pending',
      usage: 0,
    },
    {
      id: 104206,
      iccid: '8944538532046597096',
      msisdn: '883200000111019',
      imsi: '234588570010709',
      type: 'NANO',
      allocationReference: '8944538531005850000',
      allocationDate: '2023-05-03T09:20:05.700522',
      simStatus: 'Pending',
      usage: 0,
    },
    {
      id: 103204,
      iccid: '8944538532046596635',
      msisdn: '883200000110973',
      imsi: '234588570010663',
      type: 'NANO',
      allocationReference: '8944538531005850000',
      allocationDate: '2023-05-03T09:20:05.700522',
      simStatus: 'Ready for Activation',
      usage: 0,
    },
    {
      id: 102204,
      iccid: '8944538532046596643',
      msisdn: '883200000110974',
      imsi: '234588570010664',
      type: 'NANO',
      allocationReference: '8944538531005850000',
      allocationDate: '2023-05-03T09:20:05.700522',
      simStatus: 'Ready for Activation',
      usage: 0,
    },
  ],
};

export default imsiAllocationsMockData;
