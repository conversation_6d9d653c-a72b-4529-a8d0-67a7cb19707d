import React from 'react';
import { screen } from '@testing-library/react';
import testRender from 'core/utilities/testUtils';
import IMSIAllocationsTableHead from './IMSIAllocationsTableHead';

describe('IMSIAllocationsTableHead', () => {
  const mockProps = {

  };

  test('should render table head', () => {
    testRender(<IMSIAllocationsTableHead {...mockProps} />);

    expect(screen.getByTestId('imsi-allocations__table-head')).toBeInTheDocument();
  });
});
