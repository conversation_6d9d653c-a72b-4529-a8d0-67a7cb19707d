import { TableHead, TableRow } from '@mui/material';
import React from 'react';

import { StyledTableCell } from 'features/SimManagement/componentUIparts';
import { IHeader } from 'features/SimManagement/SimManagement.models';
import headers, { Alignment } from 'features/SimManagement/IMSIAllocations/tableConfig';

const IMSIAllocationsTableHead = () => {
  const headersList = headers.map(
    ({
      name, width, align = Alignment.Left, sticky,
    }: IHeader) => (
      <StyledTableCell
        key={name}
        style={{ minWidth: width }}
        align={align}
        sticky={!!sticky}
        bold
        th
      >
        {name}
      </StyledTableCell>
    ),
  );

  return (
    <TableHead data-testid="imsi-allocations__table-head">
      <TableRow>

        {headersList}
      </TableRow>
    </TableHead>
  );
};

export default IMSIAllocationsTableHead;
