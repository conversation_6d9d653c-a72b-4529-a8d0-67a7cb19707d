import { Box, Modal } from '@mui/material';
import ActionButtonWithTooltip from '@nv2/nv2-pkg-js-shared-components/lib/ActionButtonWithTooltip';
import { GetAuthorization } from 'PrivateRotes';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import { PERMISSION, REPOSITORY, RESPONSE } from 'core/utilities/constants';
import { toastInfo } from 'core/utilities/toastHelper';
import {
  CloseModalReason,
  IAccount,
  IResponse,
} from 'features/SimManagement/SimManagement.models';
import {
  getAccountsByName,
  totalMsisdn,
} from 'features/SimManagement/api.service';
import React, { FC, useEffect, useState } from 'react';
import { GrDirections } from 'react-icons/gr';
import TopBar from 'shared/TopBar';
import CreateAllocationModal from './CreateAllocationModal';
import './IMSIAllocationsTopBar.scss';
import IMSIRangeValidateErrorModal from './IMSIRangeValidateErrorModal';
import IMSIRangeValidationSucessModal from './IMSIRangeValidationSucessModal';
import { IFreeMsisdn } from './CreateAllocationModal/models';

interface IIMSIAllocationsTopBarProps {
  fetchAccounts: () => void;
}

const IMSIAllocationsTopBar: FC<IIMSIAllocationsTopBarProps> = ({ fetchAccounts }) => {
  const baseUrl = window.location.origin;
  const [open, setOpen] = React.useState(false);
  const [image, setImage] = useState<File>();
  const [accounts, setAccounts] = useState<IAccount[]>();
  const [responseData, setResponseData] = useState<IResponse | null>(null);
  const [openError, setOpenError] = useState(false);
  const [openSuccess, setOpenSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const [totalRemainingCount, setTotalRemainingCount] = useState<IFreeMsisdn>({
    totalCount: 0,
    national: 0,
    international: 0,
  });

  const handleOpen = () => {
    setImage(undefined);
    setOpen(true);
  };
  const handleClose = (event: any, reason: any) => {
    if (reason !== CloseModalReason.BackdropClick) {
      setOpen(false);
    }
  };

  const getAccountList = async () => {
    try {
      const accountsResponse = await getAccountsByName();

      if (accountsResponse.data?.length > 0) {
        const response = accountsResponse.data;
        setAccounts(response);
      }
    } catch (err) {
      toastInfo(RESPONSE.GET_ACCOUNTS_ERROR);
    }
  };

  useEffect(() => {
    getAccountList();
  }, []);

  const checkMsisdnCount = GetAuthorization(
    [PERMISSION?.FREE_MSISDN_COUNT], [REPOSITORY?.SIM_MANAGEMENT]);

  const fetchTotalRemainingCount = async () => {
    if (!checkMsisdnCount) return;
    try {
      setLoading(true);
      const { data } = await totalMsisdn();
      setTotalRemainingCount(data);
      setLoading(false);
    } catch (error: any) {
      setLoading(false);
      console.log('error: ', error);
    }
  };

  useEffect(() => {
    fetchTotalRemainingCount();
  }, []);

  return (
    <TopBar navigateTo={baseUrl} className="imsi-allocations__top-bar">
      <Box
        display="flex"
        justifyContent="flex-end"
        columnGap={2}
        marginLeft={3}
        sx={{ width: '100% !important' }}
      >
        {/* <SearchInput placeholder="Search" /> TODO: Return after search implementation */}
        <CommonAuthwrapper
          permission={[PERMISSION.CREATE_ALLOCATION]}
          repository={[REPOSITORY.SIM_ORDERS]}
        >
          <ActionButtonWithTooltip
            title="Create Allocation"
            data-testid="imsi-allocations__top-bar_create-button"
            className="imsi-allocations__top-bar_create-button"
            action={handleOpen}
            icon={<GrDirections size={21} />}
          />
        </CommonAuthwrapper>
      </Box>

      <Modal open={open} onClose={handleClose}>
        <CreateAllocationModal
          accounts={accounts || []}
          ratePlanLoading={loading}
          setParentLoading={setLoading}
          fetchAccounts={fetchAccounts}
          handleClose={handleClose}
          setImage={setImage}
          setOpenSuccess={setOpenSuccess}
          image={image}
          setOpenError={setOpenError}
          setResponseData={setResponseData}
          totalRemainingCount={totalRemainingCount}
        />
      </Modal>
      <Modal open={openError} onClose={setOpenError}>
        <IMSIRangeValidateErrorModal
          responseData={responseData}
          setResponseData={setResponseData}
          handleClose={setOpenError}
          fileName={image?.name}
          setImage={setImage}
          fetchAccounts={fetchAccounts}
        />
      </Modal>
      <Modal open={openSuccess} onClose={setOpenSuccess}>
        <IMSIRangeValidationSucessModal
          responseData={responseData}
          setResponseData={setResponseData}
          handleClose={setOpenSuccess}
          setImage={setImage}
        />
      </Modal>
    </TopBar>
  );
};

export default IMSIAllocationsTopBar;
