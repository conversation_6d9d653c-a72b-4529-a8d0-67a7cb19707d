import React from 'react';
import {
  render, screen, fireEvent,
} from '@testing-library/react';
import '@testing-library/jest-dom';
import { IResponse } from 'features/SimManagement/SimManagement.models';
import mockGenerateCSVFile from 'core/utilities/generateCSVFile';
import IMSIRangeValidateErrorModal from '../IMSIRangeValidateErrorModal';

// Mock functions
jest.mock('core/utilities/generateCSVFile', () => jest.fn());

const mockSetResponseData = jest.fn();
const mockHandleClose = jest.fn();
const mockSetImage = jest.fn();
const mockFetchAccounts = jest.fn();

describe('IMSIRangeValidateErrorModal', () => {
  const mockResponseData: IResponse = {
    totalSIM: 10,
    errorSIM: 3,
    results: [
      { sim: '***************', issue: 'Invalid IMSI' },
      { sim: '***************', issue: 'Already Allocated' },
    ],
  };

  const defaultProps = {
    responseData: mockResponseData,
    setResponseData: mockSetResponseData,
    handleClose: mockHandleClose,
    fileName: 'testfile.csv',
    setImage: mockSetImage,
    fetchAccounts: mockFetchAccounts,
  };

  it('should close modal and reset data on close button click', () => {
    render(<IMSIRangeValidateErrorModal {...defaultProps} />);

    const closeButton = screen.getByTestId('create-allocation-modal_close-button');
    fireEvent.click(closeButton);

    expect(mockSetResponseData).toHaveBeenCalledWith({});
    expect(mockSetImage).toHaveBeenCalledWith(undefined);
    expect(mockHandleClose).toHaveBeenCalled();
    expect(mockFetchAccounts).toHaveBeenCalled();
  });

  it('should call generateCSVFile when download button is clicked', () => {
    render(<IMSIRangeValidateErrorModal {...defaultProps} />);

    const downloadButton = screen.getByTestId('create-allocation-modal__body_buttons_confirm');
    fireEvent.click(downloadButton);

    expect(mockGenerateCSVFile).toHaveBeenCalledWith(
      [
        { IMSI: '***************', ErrorType: 'Invalid IMSI' },
        { IMSI: '***************', ErrorType: 'Already Allocated' },
      ],
      'testfile_Invalid_IMSI',
      expect.any(Function),
      ';',
    );
    expect(mockSetResponseData).toHaveBeenCalledWith({});
    expect(mockHandleClose).toHaveBeenCalled();
    expect(mockFetchAccounts).toHaveBeenCalled();
  });

  it('should close modal when cancel button is clicked', () => {
    render(<IMSIRangeValidateErrorModal {...defaultProps} />);

    const cancelButton = screen.getByTestId('create-allocation-modal__body_buttons_cancel');
    fireEvent.click(cancelButton);

    expect(mockSetResponseData).toHaveBeenCalledWith({});
    expect(mockSetImage).toHaveBeenCalledWith(undefined);
    expect(mockHandleClose).toHaveBeenCalled();
    expect(mockFetchAccounts).toHaveBeenCalled();
  });

  it('should render table rows correctly', () => {
    render(<IMSIRangeValidateErrorModal {...defaultProps} />);

    const tableRows = screen.getAllByRole('row');
    expect(tableRows).toHaveLength(3);

    expect(screen.getByText('***************')).toBeInTheDocument();
    expect(screen.getByText('Invalid IMSI')).toBeInTheDocument();
    expect(screen.getByText('***************')).toBeInTheDocument();
    expect(screen.getByText('Already Allocated')).toBeInTheDocument();
  });
});
