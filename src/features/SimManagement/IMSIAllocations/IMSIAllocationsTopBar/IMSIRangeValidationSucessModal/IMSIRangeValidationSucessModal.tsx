import {
  Box,
  Button,
  Typography,
} from '@mui/material';
import { IResponse } from 'features/SimManagement/SimManagement.models';
import React, { FC } from 'react';
import { GrClose } from 'react-icons/gr';
import { StyledCreateAllocationModal } from '../CreateAllocationModal/componentUIparts';

interface IMSIRangeValidationSucessModalProps {
  responseData: IResponse | null;
  setResponseData:(event?: any, reason?: any) => void;
  handleClose: (event?: any, reason?: any) => void;
  setImage:(event?: any, reason?: any) => void;

}

const IMSIRangeValidationSucessModal: FC<IMSIRangeValidationSucessModalProps> = ({
  responseData,
  setResponseData,
  handleClose,
  setImage,
}) => {
  const onClose = () => {
    setResponseData();
    setImage(undefined);
    handleClose();
  };
  return (
    <StyledCreateAllocationModal
      data-testid="create-allocation-modal"
      className="create-allocation-modal"
    >

      <div className="create-allocation-modal__header">
        <Typography variant="h2" component="h2" sx={{ display: 'flex', paddingRight: '10px' }}>
          Allocate IMSI Range
        </Typography>
        <button
          data-testid="create-allocation-modal_close-button"
          type="button"
          onClick={onClose}
        >
          <GrClose size={21} />
        </button>
      </div>
      <div className="create-allocation-modal__body">
        <Box sx={{
          backgroundColor: '#f5f1fa',
          display: 'flex',
          width: '100%',
          padding: '10px',
          alignItems: 'center',
        }}
        >
          <Typography
            variant="body1"
          >
            <strong>
              {responseData?.totalSIM}
              {' '}
              IMSIs
            </strong>
            {' '}
            out of
            {' '}
            <strong>{responseData?.totalSIM}</strong>
            {' '}
            were successfully allocated.
          </Typography>
        </Box>
        <div className="create-allocation-modal__body_buttons">
          <Button
            className="create-allocation-modal__body_cancel-button"
            onClick={onClose}
            variant="outlined"
            onMouseDown={(e) => e.preventDefault()}
            data-testid="create-allocation-modal__body_buttons_cancel"
            sx={{
              p: '0px 25px', backgroundColor: '#ebe3f6', border: '0px', textTransform: 'uppercase', fontSize: '12px !important',
            }}
          >
            OK
          </Button>
        </div>
      </div>
    </StyledCreateAllocationModal>
  );
};

export default IMSIRangeValidationSucessModal;
