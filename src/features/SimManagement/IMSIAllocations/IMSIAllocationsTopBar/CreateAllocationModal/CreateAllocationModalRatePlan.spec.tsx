import React from 'react';
import testRender from 'core/utilities/testUtils';
import { mockRatePlansListData } from 'features/SimManagement/IMSIAllocations/IMSIAllocationsMockData';
import CreateAllocationModalRatePlan, { ICreateAllocationModalRatePlanProps } from './CreateAllocationModalRatePlan';

const defaultMockProps: ICreateAllocationModalRatePlanProps = {
  ratePlans: mockRatePlansListData,
  selectedAccountId: undefined,
  onChange: () => undefined,
  value: undefined,
  onSelectRatePlan: () => undefined,
  formik: { },
};

describe('CreateAllocationModalRatePlan', () => {
  const mockProps = {
    ...defaultMockProps,
  };

  test('should render select if selectedAccountId is undefined', () => {
    const { getByTestId } = testRender(
      <CreateAllocationModalRatePlan {...mockProps} selectedAccountId={undefined} />,
    );
    const select = getByTestId('select_rate_plans');
    expect(select).toBeInTheDocument();
  });

  test('should render render create new rate plan block if selected account has no rate plans available', () => {
    const { getByTestId, queryByTestId } = testRender(
      <CreateAllocationModalRatePlan {...mockProps} selectedAccountId="someId" ratePlans={[]} />,
    );
    const select = queryByTestId('select_rate_plans');
    const createRatePlanButton = getByTestId('create_new_rate_plan');
    const reloadRatePlansButton = getByTestId('reload_rate_plans');

    expect(select).not.toBeInTheDocument();
    expect(createRatePlanButton).toBeInTheDocument();
    expect(reloadRatePlansButton).toBeInTheDocument();
  });

  test('should render select if selected account has plans available', () => {
    const { getByTestId, queryByTestId } = testRender(
      <CreateAllocationModalRatePlan
        {...mockProps}
        selectedAccountId={`${mockRatePlansListData[0].id}`}
        ratePlans={mockRatePlansListData}
      />,
    );
    const select = getByTestId('select_rate_plans');
    const createRatePlanButton = queryByTestId('create_new_rate_plan');
    const reloadRatePlansButton = queryByTestId('reload_rate_plans');

    expect(select).toBeInTheDocument();
    expect(createRatePlanButton).not.toBeInTheDocument();
    expect(reloadRatePlansButton).not.toBeInTheDocument();
  });

  test('should select first rate plan as a default one', () => {
    const onSelectRatePlan = jest.fn();

    testRender(
      <CreateAllocationModalRatePlan
        {...mockProps}
        selectedAccountId={`${mockRatePlansListData[0].id}`}
        ratePlans={mockRatePlansListData}
        onSelectRatePlan={onSelectRatePlan}
      />,
    );

    expect(onSelectRatePlan).toBeCalledWith(mockRatePlansListData[0].id);
  });
});
