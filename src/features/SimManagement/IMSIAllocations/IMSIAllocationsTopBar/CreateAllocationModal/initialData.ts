import { ICreateAllocationModalForm1 } from 'features/SimManagement/IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/models';

export const initialValues: ICreateAllocationModalForm1 = {
  accountId: '',
  allocationReference: '',
  ratePlanId: '',
  simProfile: '',
  msisdnFactor: '',
  file: null,
};

export const initialErrors = {
  accountId: '',
  title: '',
  rangeId: '',
  simProviderId: '',
  simType: '',
  quantity: '',
  ratePlanId: '',
};

export enum SimFactor {
  STANDARD = 'STANDARD',
  MICRO = 'MICRO',
  NANO = 'NANO',
  MFF2_eUICC = 'MFF2_eUICC',
  MFF2 = 'MFF2'
}
