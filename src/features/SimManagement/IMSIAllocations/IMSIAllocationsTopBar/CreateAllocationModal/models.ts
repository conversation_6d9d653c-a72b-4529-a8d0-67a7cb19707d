export interface ISliderMark {
  value: number
  label: string
}

export interface IEmptyIMSIRange {
  id: number
  title: string
  disabled: boolean
}

export interface ICreateAllocationModalForm {
  accountId: string
  title: string
  rangeId: string
  simProviderId: string
  simType: string
  quantity: number
  ratePlanId: string
}
export interface ICreateAllocationModalForm1 {
  accountId: string
  allocationReference: string
  ratePlanId: string,
  file: string |null
  simProfile: string | number
  msisdnFactor: string | number
}

export interface ICreateIMSIRanges{
  title: string
  formFactor: string
  file: string |null
}

export interface IFreeMsisdn{
  totalCount: number,
  national: number,
  international: number
}
