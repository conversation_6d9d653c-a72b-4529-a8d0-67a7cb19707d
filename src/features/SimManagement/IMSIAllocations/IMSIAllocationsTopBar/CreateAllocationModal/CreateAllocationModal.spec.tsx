import { fireEvent, waitFor } from '@testing-library/react';
import axios from 'axios';
import { coreAxios } from 'core/services/HTTPService';
import testRender from 'core/utilities/testUtils';
import imsiAllocationsMockData from 'features/SimManagement/IMSIAllocations/IMSIAllocationsMockData';
import IMSIAllocationsTopBar from 'features/SimManagement/IMSIAllocations/IMSIAllocationsTopBar/IMSIAllocationsTopBar';
import { mockAllocation } from 'features/SimManagement/IMSIRanges/IMSIRangesMockData';
import permissions from 'hooks/permissions';
import React from 'react';
import { toast } from 'react-toastify';

jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    access: permissions.result,
  }),
}));
jest.mock('core/services/HTTPService');
const mockedAxios = coreAxios as jest.Mocked<typeof axios>;

const mockAxiosSuccess = () => {
  mockedAxios.get.mockImplementationOnce(() => Promise.resolve({ data: mockAllocation }));
};

const getElementAndClickIt = (getter, param) => {
  const button = getter(param);
  fireEvent.click(button);
};

const openModal = (getByTestId) => {
  getElementAndClickIt(getByTestId, 'button-with-tooltip-icon');
};

const closeModal = (getByTestId) => {
  getElementAndClickIt(getByTestId, 'create-allocation-modal_close-button');
};

const cancelModal = (getByTestId) => {
  getElementAndClickIt(getByTestId, 'create-allocation-modal__body_buttons_cancel');
};

const confirmModal = (getByTestId) => {
  getElementAndClickIt(getByTestId, 'create-allocation-modal__body_buttons_confirm');
};

describe('StyledCreateAllocationModal', () => {
  test('should close modal', () => {
    const { getByTestId, queryByTestId } = testRender(<IMSIAllocationsTopBar
      fetchAccounts={() => undefined}
    />);
    openModal(getByTestId);
    closeModal(getByTestId);

    expect(queryByTestId('create-allocation-modal')).not.toBeInTheDocument();
  });

  test('should close modal after cancel', () => {
    const { getByTestId, queryByTestId } = testRender(<IMSIAllocationsTopBar
      fetchAccounts={() => undefined}
    />);

    openModal(getByTestId);
    cancelModal(getByTestId);

    expect(queryByTestId('create-allocation-modal')).not.toBeInTheDocument();
  });

  test('should successfully submit form', () => {
    const { getByTestId, container } = testRender(<IMSIAllocationsTopBar
      fetchAccounts={() => undefined}
    />);

    openModal(getByTestId);
    confirmModal(getByTestId);

    mockedAxios.post.mockImplementationOnce(() => Promise.resolve(imsiAllocationsMockData[0]));

    const spySuccessToast = jest.spyOn(toast, 'success');

    waitFor(() => {
      const toastBody = container.getElementsByClassName('Toastify__toast-body');

      expect(spySuccessToast).toHaveBeenCalled();
      expect(toastBody[0]).toHaveTextContent('Allocation was created successfully.');
    });
  });

  it('should render component check total simcount', async () => {
    mockAxiosSuccess();

    const { getByTestId, container } = testRender(<IMSIAllocationsTopBar
      fetchAccounts={() => undefined}
    />);

    openModal(getByTestId);
    confirmModal(getByTestId);

    waitFor(() => {
      const standard2ff = container.getElementsByClassName('standard2ff');
      const micro = container.getElementsByClassName('micro');
      const nano = container.getElementsByClassName('nano');

      expect(standard2ff).toHaveTextContent('9,888');
      expect(micro).toHaveTextContent('5');
      expect(nano).toHaveTextContent('9,888');
    });
  });
  /*
  test('should render list of ImsiRanges', () => {
    const { getByTestId, getByText } = testRender(<IMSIAllocationsTopBar {...mockProps} />);

    openModal(getByTestId);
    openRangeSelect(getByTestId);

    expect(getByText(imsiRangesMockData[0].title)).toBeInTheDocument();
    expect(getByText(imsiRangesMockData[imsiRangesMockData.length - 1].title)).toBeInTheDocument();
  });

  test('should remove allocations with remaining = 0 from the range reference list', () => {
    const imsiRangesMockDataItemNoRemaining = { ...imsiRangesMockDataItem };
    imsiRangesMockDataItemNoRemaining.remaining = 0;
    imsiRangesMockDataItemNoRemaining.title = 'imsiRangesMockDataItemNoRemainingTitle';

    const props = {
      ...mockProps,
      imsiRanges: [
        ...imsiRangesMockData,
        imsiRangesMockDataItemNoRemaining,
      ],
    };

    const {
      getByTestId, queryByText, getByText,
    } = testRender(<IMSIAllocationsTopBar {...props} />);

    openModal(getByTestId);
    openRangeSelect(getByTestId);

    expect(getByText(imsiRangesMockData[0].title)).toBeInTheDocument();
    expect(queryByText(imsiRangesMockDataItemNoRemaining.title)).not.toBeInTheDocument();
  });

  test('should render empty state if imsiRanges has no available items', () => {
    const imsiRangesMockDataItemNoRemaining = { ...imsiRangesMockDataItem };
    imsiRangesMockDataItemNoRemaining.remaining = 0;
    imsiRangesMockDataItemNoRemaining.title = 'imsiRangesMockDataItemNoRemainingTitle';

    const props = {
      ...mockProps,
      imsiRanges: [imsiRangesMockDataItemNoRemaining],
    };

    const {
      getByTestId, getByText,
    } = testRender(<IMSIAllocationsTopBar {...props} />);

    openModal(getByTestId);
    openRangeSelect(getByTestId);

    expect(getByText(emptyIMSIRange.title)).toBeInTheDocument();
  });

  test('should render empty state if imsiRanges is empty array', () => {
    const props = { ...mockProps, imsiRanges: [] };

    const {
      getByTestId, getByText,
    } = testRender(<IMSIAllocationsTopBar {...props} />);

    openModal(getByTestId);
    openRangeSelect(getByTestId);

    expect(getByText(emptyIMSIRange.title)).toBeInTheDocument();
  });

  test('should sort imsi ranges by date', () => {
    const imsiRangesMockDataItemLast = { ...imsiRangesMockDataItem };
    imsiRangesMockDataItemLast.title = 'should be last';
    imsiRangesMockDataItemLast.createdAt = '2023-01-05T05:18:01';

    const imsiRangesMockDataItemSecond = { ...imsiRangesMockDataItem };
    imsiRangesMockDataItemSecond.title = 'should be second';
    imsiRangesMockDataItemSecond.createdAt = '2023-02-05T05:18:01';

    const imsiRangesMockDataItemFirst = { ...imsiRangesMockDataItem };
    imsiRangesMockDataItemFirst.title = 'should be first';
    imsiRangesMockDataItemFirst.createdAt = '2023-02-25T05:18:01';

    const props = {
      ...mockProps,
      // should be mixed
      imsiRanges: [
        imsiRangesMockDataItemSecond,
        imsiRangesMockDataItemLast,
        imsiRangesMockDataItemFirst,
      ],
    };

    const {
      getByTestId, getAllByText,
    } = testRender(<IMSIAllocationsTopBar {...props} />);

    openModal(getByTestId);
    openRangeSelect(getByTestId);

    const selectItems = getAllByText(/should be /);

    // should be ordered
    expect(selectItems[0]).toHaveTextContent(imsiRangesMockDataItemFirst.title);
    expect(selectItems[1]).toHaveTextContent(imsiRangesMockDataItemSecond.title);
    expect(selectItems[2]).toHaveTextContent(imsiRangesMockDataItemLast.title);
  });
  */

  test('should disable submit button if no rate plan is selected', () => {
    const { getByTestId } = testRender(<IMSIAllocationsTopBar
      fetchAccounts={() => undefined}
    />);

    openModal(getByTestId);

    expect(getByTestId('create-allocation-modal__body_buttons_confirm')).toBeDisabled();
  });
});
