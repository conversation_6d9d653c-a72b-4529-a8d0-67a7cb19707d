import React from 'react';
import {
  <PERSON><PERSON><PERSON>, <PERSON>ack, TextField, Typography,
} from '@mui/material';
import { FormikProps } from 'formik';
import zerosToLetterFormatter from 'core/utilities/zerosToLetterFormatter';
import {
  ICreateAllocationModalForm,
  ISliderMark,
} from 'features/SimManagement/IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/models';

interface ICreateAllocationModalSliderProps {
  simQuantity: { totalSims: number, remainingSims: number }
  sliderMarks: ISliderMark[] | boolean
  formik: FormikProps<ICreateAllocationModalForm>
}

const CreateAllocationModalSlider = ({
  simQuantity, sliderMarks, formik,
}: ICreateAllocationModalSliderProps) => (
  <>
    <div className="create-allocation-modal__body_quantity-container">
      <Typography
        variant="body1"
        className="create-allocation-modal__body_quantity-container_subheading"
      >
        Total:
        <span
          className="create-allocation-modal__body_quantity-container_subheading_value"
        >
          {` ${simQuantity.totalSims}`}
        </span>
      </Typography>
      <Typography
        variant="body1"
        className="create-allocation-modal__body_quantity-container_subheading"
      >
        Remaining:
        <span className="create-allocation-modal__body_quantity-container_subheading_value">
          {` ${simQuantity.remainingSims}`}
        </span>
      </Typography>
      <TextField
        label="Quantity"
        variant="outlined"
        name="quantity"
        type="number"
        inputProps={{ min: 0, max: simQuantity.remainingSims }}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        value={formik.values.quantity}
        error={!!formik.touched.quantity && !!formik.errors.quantity}
        helperText={formik.touched.quantity && formik.errors.quantity}
      />
    </div>
    <Stack
      direction="row"
      alignItems="center"
      className="create-allocation-modal__body_quantity-slider"
    >
      <Slider
        aria-label="Quantity"
        name="quantity"
        valueLabelDisplay="on"
        min={0}
        max={simQuantity.remainingSims}
        marks={sliderMarks}
        valueLabelFormat={
        simQuantity.remainingSims > 1000
          ? zerosToLetterFormatter
          : String(formik.values.quantity)
        }
        onChange={formik.handleChange}
        value={formik.values.quantity || 0}
      />
    </Stack>
  </>
);

export default CreateAllocationModalSlider;
