import React from 'react';
import { render } from '@testing-library/react';
import CreateAllocationModalSlider from 'features/SimManagement/IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/CreateAllocationModalSlider';

const simQuantity = {
  totalSims: 1000,
  remainingSims: 500,
};

const sliderMarks = [
  { value: 0, label: '0' },
  { value: 500, label: '500' },
];

const formikProps = {
  handleChange: jest.fn(),
  handleBlur: jest.fn(),
  values: {
    quantity: 0,
  },
  touched: {},
  errors: {},
};

describe('CreateAllocationModalSlider', () => {
  test('renders the total and remaining SIM quantities', () => {
    const { getByText, getAllByText } = render(
      <CreateAllocationModalSlider
        simQuantity={simQuantity}
        sliderMarks={sliderMarks}
        formik={formikProps}
      />
    );

    const totalQuantityText = getByText(/Total:/i);
    expect(totalQuantityText).toBeInTheDocument();

    const remainingQuantityText = getByText(/Remaining:/i);
    expect(remainingQuantityText).toBeInTheDocument();

    const minQuantityValue = getAllByText(/0/i)[1];
    expect(minQuantityValue).toBeInTheDocument();

    const remainingQuantityValue = getAllByText(/500/i)[1];
    expect(remainingQuantityValue).toBeInTheDocument();
  });
});
