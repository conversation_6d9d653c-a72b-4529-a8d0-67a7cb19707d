import {
  FormControl, InputLabel, MenuItem, Select, SelectChangeEvent,
  Tooltip,
  Typography,
} from '@mui/material';
import { ratePlansPaths } from 'core/configs/paths';
import { toastError } from 'core/utilities/toastHelper';
import { IRatePlan } from 'features/SimManagement/SimManagement.models';
import { getRatePlansById } from 'features/SimManagement/api.service';
import { TOASTS } from 'features/constants';
import React, { useEffect, useState } from 'react';
import { AiOutlineReload } from 'react-icons/ai';
import { GrFormDown } from 'react-icons/gr';
import Batch from 'shared/Batch/Batch';
import Loader from 'shared/Loader';
import PrimaryButton from 'shared/PrimaryButton';

export interface ICreateAllocationModalRatePlanProps {
  ratePlans: IRatePlan[] | undefined
  selectedAccountId: string | undefined
  onChange: (event: SelectChangeEvent) => void
  value: string | undefined
  onSelectRatePlan: (ratePlanId: number | undefined) => void
  formik: any
}

const createRatePlansPath = (accountId: string) => `${ratePlansPaths.base}/${ratePlansPaths.create}?id=${accountId}`;
const onCreateNewRatePlan = (accountId: string) => window.open(createRatePlansPath(accountId), '_blank');

const CreateAllocationModalRatePlan = (
  {
    ratePlans, selectedAccountId, onChange, value = '', onSelectRatePlan, formik,
  }: ICreateAllocationModalRatePlanProps,
) => {
  const [isLoading, setIsLoading] = useState(false);
  const [accountRatePlans, setAccountRatePlans] = useState<IRatePlan[] | undefined>();

  useEffect(() => {
    if (!selectedAccountId || !ratePlans) return;
    setAccountRatePlans(ratePlans);
    onSelectRatePlan(ratePlans[0]?.id);
  }, [selectedAccountId, ratePlans]);

  const handleOnUpdateRatePlans = async () => {
    if (selectedAccountId) {
      try {
        setIsLoading(true);
        const newRatePlansData = await getRatePlansById(selectedAccountId);
        if (newRatePlansData.data.ratePlans.length) {
          const sortedRatePlans = newRatePlansData?.data?.ratePlans?.sort(
            (a, b) => Number(b?.isDefault) - Number(a?.isDefault),
          );
          setAccountRatePlans(sortedRatePlans);
          onSelectRatePlan(sortedRatePlans[0].id);
        }
      } catch (e) {
        toastError(TOASTS.REFRESH_RATE_PLAN_DATA_ERROR);
      } finally {
        setIsLoading(false);
      }
    }
  };

  if (!accountRatePlans?.length && selectedAccountId) {
    return (
      <>
        <div className="create-allocation-modal__body_empty-rate-plans">
          <Typography variant="body1" className="">
            No Rate Plans for selected account.
          </Typography>
          <div style={{ display: 'flex' }}>
            <PrimaryButton
              data-testid="create_new_rate_plan"
              // className="create-allocation-modal__body_empty-rate-plans_create-button"
              onClick={() => onCreateNewRatePlan(selectedAccountId || '')}
            >
              Create
            </PrimaryButton>
            <Tooltip title="Refresh" placement="top" arrow style={{ marginLeft: '10px' }}>
              <span>
                <PrimaryButton
                  data-testid="reload_rate_plans"
                  className="create-allocation-modal__body_empty-rate-plans_reload-button"
                  onClick={handleOnUpdateRatePlans}
                  disabled={isLoading}
                >
                  {
                    isLoading
                      ? <Loader staticColor="#f5f1fa" size={21} />
                      : <AiOutlineReload size={21} />
                  }
                </PrimaryButton>
              </span>
            </Tooltip>
          </div>
        </div>
        <Typography
          variant="body1"
          component="p"
          sx={{ color: formik?.errors?.ratePlanId || formik?.touched?.ratePlanId ? 'red' : 'unset' }}
          className={` ${formik?.errors?.ratePlanId && 'dropZone_error'}`}
        >
          {formik?.errors?.ratePlanId || formik?.touched?.ratePlanId}
        </Typography>

      </>

    );
  }

  return (
    <FormControl
      data-testid="select_rate_plans"
      className="create-allocation-modal__body_form-control rate-plan-select"
    >
      <InputLabel>Rate Plan</InputLabel>
      <Select
        IconComponent={() => (<GrFormDown className="select-arrow-icon" size={21} />)}
        disabled={!selectedAccountId}
        label="Rate Plan"
        name="ratePlanId"
        onChange={onChange}
        value={value}
        sx={{
          '.MuiSelect-select': {
            display: 'flex',
            justifyContent: 'space-between',
            paddingRight: '16px !important',
          },
        }}
      >
        {
          accountRatePlans?.map((ratePlan) => (
            <MenuItem
              key={ratePlan.id}
              value={ratePlan.id}
              style={{
                display: 'flex',
                justifyContent: 'space-between',
              }}
            >
              {ratePlan.name}
              {ratePlan?.isDefault && (
                <div style={{
                  marginRight: '26px',
                }}
                >
                  <Batch
                    label="Default"
                    sx={{
                      padding: '2px 8px',
                      marginBottom: '0px',
                      borderRadius: '4px',
                    }}
                  />
                </div>
              )}
            </MenuItem>
          ))
        }
      </Select>
    </FormControl>
  );
};

export default CreateAllocationModalRatePlan;
