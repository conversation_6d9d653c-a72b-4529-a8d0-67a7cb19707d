import { Box, styled } from '@mui/material';

export const StyledCreateAllocationModal = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 464,
  backgroundColor: 'white',
  borderTop: `6px solid ${theme.palette.primary.main}`,
  borderRadius: theme.shape.borderRadius,
  padding: '26px 32px 32px 32px',
  '& .MuiFormHelperText-root': {
    color: theme.palette.error.main,
  },
}));

export const StyledSimTypeContainer = styled('div')<{isSelected?: boolean}>(({ theme, isSelected }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: 10,
  paddingTop: 12,
  paddingBottom: 12,
  borderRadius: theme.shape.borderRadius,
  backgroundColor: isSelected ? '#fff5ff' : 'white',
  '& path': {
    stroke: isSelected ? theme.palette.secondary.main : '696969',
  },
}));
