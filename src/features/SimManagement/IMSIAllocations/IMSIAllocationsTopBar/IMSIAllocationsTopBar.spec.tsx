import { fireEvent, screen, waitFor } from '@testing-library/react';
import testRender from 'core/utilities/testUtils';
import permissions from 'hooks/permissions';
import React from 'react';
import IMSIAllocationsTopBar from './IMSIAllocationsTopBar';

jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    access: permissions.result,
  }),
}));
describe('IMSIAllocationsTopBar', () => {
  test('should render top bar', () => {
    testRender(<IMSIAllocationsTopBar
      fetchAccounts={() => undefined}
    />);

    expect(screen.getByTestId('top-bar')).toBeInTheDocument();
  });

  test('should open modal', () => {
    const { getByTestId } = testRender(<IMSIAllocationsTopBar
      fetchAccounts={() => undefined}
    />);
    const createButton = getByTestId('button-with-tooltip-icon');

    fireEvent.click(createButton);
    waitFor(() => {
      expect(getByTestId('create-allocation-modal')).toBeInTheDocument();
    });
  });
});
