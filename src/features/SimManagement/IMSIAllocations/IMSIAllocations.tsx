/** @format */

import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import axios, { AxiosResponse } from 'axios';
import {
  Box,
  MenuItem,
  Pagination,
  Select,
  Table,
  Typography,
} from '@mui/material';
import { StyledTableContainer } from 'features/SimManagement/componentUIparts';
import {
  IIMSIAllocationResponse,
  IIMSIAllocation,
} from 'features/SimManagement/SimManagement.models';
import { getSimAccounts } from 'features/SimManagement/api.service';
import useAbortController from 'core/hooks/useAbortController';
import Loader from 'shared/Loader';
import { toastError } from 'core/utilities/toastHelper';
import useMuiTableSearchParams from 'hooks/useMuiTableSearchParams';
import IMSIAllocationsTableHead from './IMSIAllocationsTableHead';
import IMSIAllocationsTableBody from './IMSIAllocationsTableBody/IMSIAllocationsTableBody';
import IMSIAllocationsTopBar from './IMSIAllocationsTopBar';
import './IMSIAllocations.scss';

const IMSIAllocations = () => {
  const [loading, setLoading] = useState(false);
  const [imsiAllocations, setImsiAllocations] = useState<IIMSIAllocation[]>([]);
  const [accounts, setAccounts] = useState<IIMSIAllocationResponse>();
  const { cancelPreviousRequest, setNewController } = useAbortController();
  const location = useLocation();
  const navigate = useNavigate();
  const { generateParamsForUrl, getParamsFromUrl } = useMuiTableSearchParams();
  const {
    page, pageSize, field, sort, search,
  } = getParamsFromUrl();

  const pageInt = parseInt(page.toString(), 10);
  const pageSizeInt = parseInt(pageSize.toString(), 10);

  const fetchAccounts = async (paramPage, paramPageSize, paramField?, paramSort?, paramSearch?) => {
    const newSearchParams = generateParamsForUrl(
      paramPage, paramPageSize, paramField, paramSort, paramSearch,
    );
    const newUrl = `${location?.pathname
      }?tab=imsi-allocations&${newSearchParams?.toString()}`;
    cancelPreviousRequest();
    const { signal } = setNewController();
    navigate(newUrl, { replace: true });

    try {
      setLoading(true);
      const accountsResponse: AxiosResponse = await getSimAccounts(
        paramPage,
        paramPageSize,
        paramField,
        paramSort,
        paramSearch,
        signal,
      );
      const { data } = accountsResponse;
      setAccounts(data);
      setImsiAllocations(data?.results);
      setLoading(false);
    } catch (error) {
      if (axios.isCancel(error)) {
        toastError('Request was canceled');
      }
      setImsiAllocations([]);
      setLoading(false);
    }
  };

  const handleChange = (event) => {
    const newPageSize = event?.target?.value;
    let newPage = parseInt(page.toString(), 10);
    const totalRecords = accounts?.totalCount || 1;
    if (newPage > Math.ceil(totalRecords / newPageSize)) {
      newPage = Math.ceil(totalRecords / newPageSize);
    }
    fetchAccounts(newPage, newPageSize, field, sort, search);
  };

  const handleChangePage = (event, pageValue) => {
    fetchAccounts(pageValue, pageSize, field, sort, search);
  };

  useEffect(() => {
    fetchAccounts(page, pageSize, field, sort, search);
  }, []);

  return (
    <div className="imsi-allocations" data-testid="imsi-allocations">
      <IMSIAllocationsTopBar
        fetchAccounts={() => fetchAccounts(page, pageSize, field, sort, search)}
      />
      <StyledTableContainer>
        <Table>
          <IMSIAllocationsTableHead />
          {!loading && imsiAllocations && (
            <IMSIAllocationsTableBody imsiAllocations={imsiAllocations} />
          )}
        </Table>
        {loading && (
          <div className="imsi-allocations__loader">
            <Loader staticColor="#f5f1fa" size={60} />
          </div>
        )}
      </StyledTableContainer>
      <Box
        display={!loading && accounts?.results?.length === 0 ? 'none' : 'flex'}
        alignItems="center"
        justifyContent="space-between"
        marginTop="8px"
        sx={{
          backgroundColor: '#f5f1fa',
          padding: '7px 10px',
          borderRadius: '4px',
          '& .MuiButtonBase-root, &.MuiPaginationItem-root': {
            borderRadius: '4px',
            minWidth: '40px',
            minHeight: '40px',
            padding: '4px 10px',
            fontWeight: 700,
            '&.Mui-selected': {
              color: '#5514b4',
              '&:hover': {
                backgroundColor: '#ccb9e9',
              },
              backgroundColor: '#ccb9e9',
            },
            '&:hover': {
              backgroundColor: '#ebe3f6',
            },
          },
        }}
      >
        <Box display="flex" alignItems="center" gap={3.5}>
          <Typography>Rows per page:</Typography>
          <Select
            labelId="demo-simple-select-label"
            id="demo-simple-select"
            value={pageSize}
            onChange={handleChange}
          >
            {[10, 20, 30, 40, 50].map((size) => (
              <MenuItem key={size} value={size}>
                {size}
              </MenuItem>
            ))}
          </Select>
        </Box>
        <Typography>
          {`${(pageInt - 1) * pageSizeInt + 1} - ${Math.min(
            pageInt * pageSizeInt,
            accounts?.totalCount || 0,
          )} of ${accounts?.totalCount || 0}`}
        </Typography>
        <Pagination
          count={accounts?.lastPage || 0}
          defaultPage={
            accounts?.page ? parseInt(accounts?.page.toString(), 10) : 1
          }
          siblingCount={0}
          boundaryCount={2}
          onChange={handleChangePage}
        />
      </Box>
    </div>
  );
};

export default IMSIAllocations;
