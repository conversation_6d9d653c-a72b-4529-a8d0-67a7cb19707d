import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import IMSIAllocationsActionExport from './IMSIAllocationsActionExport';

describe('IMSIAllocationsActionExport', () => {
  test('renders IMSIAllocationsActionExport component without errors', () => {
    render(<IMSIAllocationsActionExport
      rowCount={0}
      isLoading={false}
      onExportCSVFile={() => jest.fn()}
    />);
  });

  test('disables export button when isLoading is true', () => {
    const { container } = render(
      <IMSIAllocationsActionExport
        rowCount={0}
        isLoading={false}
        onExportCSVFile={() => jest.fn()}
      />);
    const exportButton = container.querySelector('.Mui-disabled');
    expect(exportButton).toBeDisabled();
  });

  test('disables export button when rowCount is 0', () => {
    const { getByTestId } = render(
      <IMSIAllocationsActionExport
        rowCount={0}
        isLoading={false}
        onExportCSVFile={() => jest.fn()}
      />);
    const exportButton = getByTestId('Export');
    expect(exportButton).toBeDisabled();
  });

  test('calls onExportCSVFile when export button is clicked', () => {
    const mockOnExportCSVFile = jest.fn();
    const { getByTestId } = render(
      <IMSIAllocationsActionExport
        rowCount={10}
        isLoading={false}
        onExportCSVFile={mockOnExportCSVFile}
      />);
    const exportButton = getByTestId('Export');
    fireEvent.click(exportButton);
    expect(mockOnExportCSVFile).toHaveBeenCalled();
  });
});
