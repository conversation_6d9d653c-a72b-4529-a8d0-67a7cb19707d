import { IHeader } from '../SimManagement.models';

export enum Alignment {
  Left = 'left',
  Right = 'right',
}

const headers: Array<IHeader> = [
  {
    id: 'Title',
    name: 'Title',
    width: 130,
  },
  {
    id: 'Account',
    name: 'Account',
    width: 200,
  },
  {
    id: 'Country',
    name: 'Country',
    width: 200,
  },
  {
    id: 'SimProvider',
    name: 'SIM Provider',
    width: 110,
  },
  {
    id: 'SimType',
    name: 'SIM Type',
    width: 130,
  },
  {
    id: 'Quantity',
    name: 'Quantity',
    width: 130,
    align: Alignment.Left,
  },
  {
    id: 'AllocationDate',
    name: 'Allocation Date',
    width: 240,
  },
  {
    id: 'Actions',
    name: 'Actions',
    width: 140,
  },
];

export default headers;
