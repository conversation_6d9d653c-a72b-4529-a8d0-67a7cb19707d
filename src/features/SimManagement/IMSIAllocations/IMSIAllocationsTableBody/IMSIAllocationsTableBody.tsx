import { TableBody } from '@mui/material';
import React, { FC } from 'react';

import {
  IIMSIAllocation,
} from 'features/SimManagement/SimManagement.models';
import Row from './Row';

interface IIMSIAllocationsTableBodyProps {
  imsiAllocations: IIMSIAllocation[];
}

const IMSIAllocationsTableBody: FC<IIMSIAllocationsTableBodyProps> = ({
  imsiAllocations,
}) => (
  <TableBody data-testid="imsi-allocations__table-body">
    {
      imsiAllocations.map((imsiAllocation) => (
        <Row
          key={imsiAllocation.id}
          imsiAllocation={imsiAllocation}
        />
      ))
    }
  </TableBody>
);

export default IMSIAllocationsTableBody;
