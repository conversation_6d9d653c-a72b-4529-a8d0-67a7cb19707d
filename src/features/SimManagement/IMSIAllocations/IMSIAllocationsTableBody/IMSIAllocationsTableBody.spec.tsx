import React from 'react';
import { screen } from '@testing-library/react';
import testRender from 'core/utilities/testUtils';
import imsiAllocationsMockData, {
  accountsMockData,
  mockRatePlansListData,
} from 'features/SimManagement/IMSIAllocations/IMSIAllocationsMockData';
import imsiRangesMockData from 'features/SimManagement/IMSIRanges/IMSIRangesMockData';
import IMSIAllocationsTableBody from './IMSIAllocationsTableBody';

const defaultMockProps = {
  imsiAllocations: imsiAllocationsMockData,
  imsiRanges: imsiRangesMockData,
  ratePlans: mockRatePlansListData,
  accounts: accountsMockData,
  selected: [1, 2, 3],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onChangeCheckbox: (id: number) => undefined,
};

describe('IMSIAllocationsTableBody', () => {
  const mockProps = {
    ...defaultMockProps,
  };

  test('should render table', () => {
    testRender(<IMSIAllocationsTableBody {...mockProps} />);

    expect(screen.getByTestId('imsi-allocations__table-body')).toBeInTheDocument();
  });
});
