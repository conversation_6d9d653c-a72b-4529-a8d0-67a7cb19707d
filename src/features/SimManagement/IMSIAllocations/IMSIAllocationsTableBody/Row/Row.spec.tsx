import React from 'react';
import { screen } from '@testing-library/react';
import testRender from 'core/utilities/testUtils';
import imsiAllocationsMockData, {
  accountsMockData,
  mockRatePlansListData,
} from 'features/SimManagement/IMSIAllocations/IMSIAllocationsMockData';
import { imsiRangesMockDataItem } from 'features/SimManagement/IMSIRanges/IMSIRangesMockData';
import Row from './Row';

const defaultMockProps = {
  imsiAllocation: imsiAllocationsMockData[0],
  imsiRange: imsiRangesMockDataItem,
  ratePlan: mockRatePlansListData[0],
  account: accountsMockData[0],
  selected: [1, 2, 3],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onChangeCheckbox: (id: number) => undefined,
};

describe('Row', () => {
  const mockProps = {
    ...defaultMockProps,
  };

  test('should render account', () => {
    testRender(<Row {...mockProps} />);

    expect(screen.getByText('Tesla')).toBeInTheDocument();
  });

  test('should render country', () => {
    testRender(<Row {...mockProps} />);

    expect(screen.getByText('GB')).toBeInTheDocument();
  });

  test('should render sim provider', () => {
    testRender(<Row {...mockProps} />);

    expect(screen.getByText('NR')).toBeInTheDocument();
  });

  test('should render type', () => {
    testRender(<Row {...mockProps} />);

    expect(screen.getByText('MICRO')).toBeInTheDocument();
  });

  test('should render quantity', () => {
    testRender(<Row {...mockProps} />);

    expect(screen.getByText(380)).toBeInTheDocument();
  });

  test('should render Date', () => {
    testRender(<Row {...mockProps} />);

    expect(screen.getByText('31-12-2022 11:55:13')).toBeInTheDocument();
  });
});
