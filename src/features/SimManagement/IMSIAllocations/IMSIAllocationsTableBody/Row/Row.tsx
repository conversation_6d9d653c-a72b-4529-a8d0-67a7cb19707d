import React, { FC, useState } from 'react';
import ReactCountryFlag from 'react-country-flag';

import { StyledTableCell, StyledTableRow } from 'features/SimManagement/componentUIparts';
import {
  IIMSIAllocation,
} from 'features/SimManagement/SimManagement.models';
import ImagePlaceholder from 'assets/images/ImagePlaceholder';
import { formatDateWithHours } from 'core/utilities/formatDate';
import { getNumberWithCommas } from 'core/utilities/toMoneyFormat';
import './Row.scss';
import { Tooltip } from '@mui/material';

interface IRowProps {
  imsiAllocation: IIMSIAllocation;
}

const Row: FC<IRowProps> = ({
  imsiAllocation,
}) => {
  const {
    id,
    title,
    accountName,
    quantity,
    createdAt,
    formFactor,
    provider,
    country,
    logoUrl,
  } = imsiAllocation;
  const [flagError, setFlagError] = useState(false);

  return (
    <StyledTableRow role="checkbox" key={id}>

      <StyledTableCell id={String(id)}>
        <Tooltip title={title} arrow>
          <span>
            {title.length > 30 ? `${title.slice(0, 30)}...` : title}
          </span>
        </Tooltip>
      </StyledTableCell>
      <StyledTableCell className="account-table-cell">
        <ImagePlaceholder logo={logoUrl} />
        {accountName}
      </StyledTableCell>
      <StyledTableCell>
        <div className="account-table-cell__country">
          {
            flagError && <ImagePlaceholder className="account-table-cell__country_flag" />
          }
          {
            !flagError && (
              <ReactCountryFlag
                className="account-table-cell__flag"
                style={{ width: '23px', marginRight: '10px' }}
                countryCode={country || ''}
                svg
                onError={() => setFlagError(true)}
              />
            )
          }
          {country}
        </div>
      </StyledTableCell>
      <StyledTableCell>{provider}</StyledTableCell>
      <StyledTableCell>{formFactor.replaceAll('_', ' ')}</StyledTableCell>
      <StyledTableCell>{getNumberWithCommas(quantity)}</StyledTableCell>
      <StyledTableCell>{formatDateWithHours(createdAt)}</StyledTableCell>
      <StyledTableCell />
    </StyledTableRow>
  );
};

export default Row;
