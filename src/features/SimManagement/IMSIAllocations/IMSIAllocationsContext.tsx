import React, { createContext, ReactElement } from 'react';

type contextType = { [key: string] : any }

const IMSIAllocationsContext = createContext<contextType>({});

interface IAppContextProvider {
  children: ReactElement | ReactElement[] | null;
  value: contextType
}

const IMSIAllocationsContextProvider = ({ children, value }: IAppContextProvider) => (
  <IMSIAllocationsContext.Provider value={value}>
    {children}
  </IMSIAllocationsContext.Provider>
);

const useIMSIAllocationsContext = () => React.useContext(IMSIAllocationsContext);

export { IMSIAllocationsContext, IMSIAllocationsContextProvider, useIMSIAllocationsContext };

export default IMSIAllocationsContextProvider;
