import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import SimManagementClientTableActionsLeft from 'features/SimManagement/SimManagementClient/SimManagementClientTableActionsLeft';

const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
}));

describe('SimManagementClientTableActionsLeft', () => {
  test('should navigate back when the button is clicked', () => {
    const { getByRole } = render(<SimManagementClientTableActionsLeft />);
    const backButton = getByRole('button');

    fireEvent.click(backButton);

    expect(mockNavigate).toHaveBeenCalledWith('/');
  });
});
