import React from 'react';
import { fireEvent } from '@testing-library/react';
import testRender from 'core/utilities/testUtils';
import permissions from 'hooks/permissions';
import SimManagement, { tabItemsConfig } from './SimManagement';
import { organizationTypes } from 'user.model';

const mockUser = {
  email: "<EMAIL>",
  firstName: "test",
  lastName: "user",
  id: "0f5e8253-2360-4898-9f32-30b8d9c9874a",
  organization: {
    name: "ABC",
    id: 91,
    parent_id: '1',
    type: organizationTypes.DISTRIBUTOR,
  },
};

jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    access: permissions.result,
  }),
}));
describe('SimManagement', () => {
  test('should be rendered', () => {
    const { getByTestId } = testRender(<SimManagement user={mockUser} />);

    expect(getByTestId('sim-management-page')).toBeInTheDocument();
  });

  test('should render tabs properly', () => {
    const { getByText } = testRender(<SimManagement user={mockUser} />);

    expect(getByText(tabItemsConfig[0].name)).toBeInTheDocument();
    expect(getByText(tabItemsConfig[1].name)).toBeInTheDocument();
  });

  test('should open first tab by default', () => {
    const { getByTestId } = testRender(<SimManagement user={mockUser} />);

    expect(getByTestId('imsi-ranges')).toBeInTheDocument();
  });

  test('should open first tab by default', () => {
    const { getByTestId } = testRender(<SimManagement user={mockUser} />);

    expect(getByTestId('imsi-ranges')).toBeInTheDocument();
  });

  test('should switch to second tab', () => {
    const { getByText, getByTestId } = testRender(<SimManagement user={mockUser} />);

    const secondTab = getByText(tabItemsConfig[1].name);
    fireEvent.click(secondTab);
    expect(getByTestId('imsi-allocations')).toBeInTheDocument();
  });

  test('should switch to second tab and back', () => {
    const { getByText, getByTestId } = testRender(<SimManagement user={mockUser} />);

    const secondTab = getByText(tabItemsConfig[1].name);

    fireEvent.click(secondTab);

    const firstTab = getByText(tabItemsConfig[0].name);

    fireEvent.click(firstTab);

    expect(getByTestId('imsi-ranges')).toBeInTheDocument();
  });

  test('should render second tab as a default one based on url', () => {
    const { getByTestId } = testRender(<SimManagement user={mockUser} />, '/', '?tab=imsi-allocations');

    expect(getByTestId('imsi-allocations')).toBeInTheDocument();
  });

  test('should render first tab as a default one based on url', () => {
    const { getByTestId } = testRender(<SimManagement user={mockUser} />, '/', '?tab=imsi-ranges');

    expect(getByTestId('imsi-ranges')).toBeInTheDocument();
  });
});
