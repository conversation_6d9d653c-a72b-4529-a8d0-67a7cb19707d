import { coreAxios } from "core/services/HTTPService";
import {
  IAccount,
  IAccountList,
  ICard,
  ICardsRemains,
  IIMSIAllocation,
  IIMSIAllocationBody,
  IIMSIAllocationResponse,
  IIMSIRange,
  IIMSIRangeResponse,
  IOperation,
  IOrderDetails,
  IRatePlanCompany,
  IRatePlanFullClient,
  IRateResponse,
  ISIMRequestBulk,
  ISimAction,
  ISimRequest,
  ISimResponse,
} from "features/SimManagement/SimManagement.models";
import {
  IConnectionHistory,
  ISMSConnectionHistory,
  IVoiceConnectionHistory,
  IMarketShare,
  IParseAuditTrailData,
  ISummary,
} from "features/SimManagementDetail/SimManagement.module";
import { IPagination, IPaginationTotal, ITotalRow } from "features/models";

export const getSimRanges = () =>
  coreAxios.get<IIMSIRange[]>("/glass/sim/ranges");

export const getSimRangesPaginated = (
  page: number,
  pageSize: number,
  field?: string,
  ordering?: string,
  search?: string,
  signal?: AbortSignal
) => {
  let url = `/glass/sim/ranges?page=${page}&page_size=${pageSize}`;
  if (ordering === "asc") url += `&ordering=${field}`;
  if (ordering === "desc") url += `&ordering=-${field}`;
  if (search) url += `&search=${search}`;

  if (signal) return coreAxios.get<IIMSIRangeResponse>(url, { signal });
  return coreAxios.get<IIMSIRangeResponse>(url);
};

export const getSimAllocations = () =>
  coreAxios.get<IIMSIAllocationResponse>("/glass/sim/allocations");

export const createSimAllocations = (allocation: IIMSIAllocationBody) =>
  coreAxios.post<IIMSIAllocation>("/glass/sim/allocations", allocation);

export const getRatePlans = () =>
  coreAxios.get<IRatePlanFullClient[]>("/glass/rate-plans");

export const getRatePlansByAccount = () =>
  coreAxios.get<IRateResponse<IRatePlanCompany>>(
    "/glass/rate-plans/by-accounts"
  );

export const getRatePlansById = (accountId: string) =>
  coreAxios.get<IRatePlanCompany>(`/glass/rate-plans/by-accounts/${accountId}`);

export const getAccountDetails = (accountId: number) =>
  coreAxios.get<IAccount>(`/glass/accounts/${accountId}`);

export const getAccounts = () => coreAxios.get<IAccountList>("/glass/accounts");

export const getAccountsByName = () =>
  coreAxios.get<IAccount[]>("/glass/account-names");

export const getCardsRemains = () =>
  coreAxios.get<ICardsRemains[]>("/glass/sim/cards/remains");

export const getUser = () => coreAxios.get("/users/me");

export const getAccountByOrganizationId = (organizationId) =>
  coreAxios.get(`/glass/accounts/${organizationId}/info`);

export const getSummaryApi = (imsi) =>
  coreAxios.get<ISummary>(`/glass/sim/cards/summary/${imsi}`);

export const getAccount = (id: string) =>
  coreAxios.get(`/glass/accounts/${id}`);

export const createSimAllocationsByImport = (formData, Paramas) =>
  coreAxios.post(
    "glass/sim/allocations/{allocation_reference}/account/{account_id}/rateplan/{rateplan_id}/custom",
    formData,
    Paramas
  );

export const uploadMsisdn = (formData) =>
  coreAxios.post("glass/sim/msisdn/documents", formData);

export const getMarketShareReport = (
  marketShareFor,
  marketshareValue,
  signal?,
  startDate?,
  endDate?
) => {
  let url;
  if (marketShareFor === "all") {
    url = "glass/market/accounts";
  } else if (marketShareFor === "account") {
    url = `glass/market/account/${marketshareValue}`;
  } else if (marketShareFor === "imsi") {
    url = `glass/market/accounts/imsi?imsi=${marketshareValue}`;
    if (startDate) url += `&from_date=${startDate}`;
    if (endDate) url += `&to_date=${endDate}`;

    return coreAxios.get<IMarketShare>(url, { signal });
  }

  if (startDate) url += `?from_date=${startDate}`;
  if (endDate) url += `&to_date=${endDate}`;

  return coreAxios.get<IMarketShare>(url, { signal });
};

export const getSimUsage = (accountId, page, pageSize) =>
  coreAxios.get(
    `glass/sim/cards/usage/${accountId}?page=${page}&page_size=${pageSize}`
  );

export const getConnectionHistory = (
  imsi,
  dateParam,
  page: number,
  pageSize: number,
  ordering?: string,
  search?: string,
  signal?
) => {
  let url = `glass/sim/connection/history?imsi=${imsi}&month=${dateParam}&page=${page}&page_size=${pageSize}`;
  if (ordering) url += `&ordering=${ordering}`;

  if (search) url += `&search=${search}`;

  return coreAxios.get<IPagination<IConnectionHistory[]>>(url, { signal });
};

export const getSmsConnectionHistory = (
  imsi,
  dateParam,
  page: number,
  pageSize: number,
  ordering?: string,
  search?: string,
  signal?
) => {
  let url = `glass/sim/sms/connection/history?imsi=${imsi}&month=${dateParam}&page=${page}&page_size=${pageSize}`;
  if (ordering) url += `&ordering=${ordering}`;
  if (search) url += `&search=${search}`;
  return coreAxios.get<IPagination<ISMSConnectionHistory[]>>(url, { signal });
};

export const getVoiceConnectionHistory = (
  imsi,
  dateParam,
  page: number,
  pageSize: number,
  ordering?: string,
  search?: string,
  signal?
) => {
  let url = `glass/sim/voice/connection/history?imsi=${imsi}&month=${dateParam}&page=${page}&page_size=${pageSize}`;
  if (ordering) url += `&ordering=${ordering}`;
  if (search) url += `&search=${search}`;
  return coreAxios.get<IPagination<IVoiceConnectionHistory[]>>(url, { signal });
};

export const getAuiditTrail = (
  imsi,
  dateParam,
  page: number,
  pageSize: number,
  ordering?: string,
  search?: string,
  signal?
) => {
  let url = `glass/sim/cards/audit?imsi=${imsi}&month=${dateParam}&page=${page}&page_size=${pageSize}`;
  if (ordering) url += `&ordering=${ordering}`;

  if (search) url += `&search=${search}`;

  return coreAxios.get<IPagination<IParseAuditTrailData[]>>(url, { signal });
};

export const getCards = (
  accountId: any,
  page: number,
  pageSize: number,
  ordering?: string,
  search?: string
) => {
  let url = `glass/sim/cards/usage?account_id=${accountId}&page=${page}&page_size=${pageSize}`;

  if (ordering) url += `&ordering=${ordering}`;

  if (search) url += `&search=${search}`;

  return coreAxios.get<IPaginationTotal<ICard[], ITotalRow>>(url);
};

export const getCardsExport = (
  accountId,
  ordering?: string,
  search?: string
) => {
  let url = "glass/sim/cards/usage/export";
  if (accountId) {
    url += `?account_id=${accountId}`;
  }

  if (ordering) {
    if (!accountId) {
      url += `?ordering=${ordering}`;
    } else {
      url += `&ordering=${ordering}`;
    }
  }

  if (search) {
    if (!ordering) {
      url += `?search=${search}`;
    } else {
      url += `&search=${search}`;
    }
  }

  return coreAxios.get<IPagination<ICard[]>>(url);
};

export const getConnectionHistoryExport = (
  imsi: string,
  date?: string,
  ordering?: string,
  search?: string
) => {
  let url = "glass/sim/connection/history/export";

  if (imsi) {
    url += `?imsi=${imsi}`;
  }

  if (date) {
    url += `&month=${date}`;
  }

  if (ordering) {
    url += `&ordering=${ordering}`;
  }

  if (search) {
    url += `&search=${search}`;
  }

  return coreAxios.get<IPagination<IConnectionHistory[]>>(url);
};

export const getSimConnectionHistoryExport = (
  imsi: string,
  date?: string,
  ordering?: string,
  search?: string
) => {
  let url = "glass/sim/sms/connection/history/export";
  if (imsi) {
    url += `?imsi=${imsi}`;
  }
  if (date) {
    url += `&month=${date}`;
  }
  if (ordering) {
    url += `&ordering=${ordering}`;
  }
  if (search) {
    url += `&search=${search}`;
  }
  return coreAxios.get<IPagination<ISMSConnectionHistory[]>>(url);
};

export const getVoiceConnectionHistoryExport = (
  imsi: string,
  date?: string,
  ordering?: string,
  search?: string
) => {
  let url = "glass/sim/voice/connection/history/export";
  if (imsi) {
    url += `?imsi=${imsi}`;
  }
  if (date) {
    url += `&month=${date}`;
  }
  if (ordering) {
    url += `&ordering=${ordering}`;
  }
  if (search) {
    url += `&search=${search}`;
  }
  return coreAxios.get<IPagination<IVoiceConnectionHistory[]>>(url);
};

export const setSimActive = (body: ISimRequest) =>
  coreAxios.post<ISimResponse>("/glass/sim/cards/activate", body);

export const setSimDeActive = (body: ISimRequest) =>
  coreAxios.post<ISimResponse>("/glass/sim/cards/deactivate", body);

export const bulkSimStatusChange = (simStatus, body: ISIMRequestBulk) =>
  coreAxios.post<ISimResponse>(`/glass/sim/cards/bulk/${simStatus}`, body);

export const getAccesss = () => coreAxios.get("/authorization/user/scope");

export const getSimAccounts = (
  page: number,
  pageSize: number,
  field?: string,
  ordering?: string,
  search?: string,
  signal?: AbortSignal
) => {
  let url = `/glass/sim/allocations?page=${page}&page_size=${pageSize}`;
  if (ordering === "asc") url += `&ordering=${field}`;
  if (ordering === "desc") url += `&ordering=-${field}`;
  if (search) url += `&search=${search}`;

  if (signal) return coreAxios.get(url, { signal });
  return coreAxios.get(url);
};

export const getAllMSISDN = (
  page: number,
  pageSize: number,
  field?: string,
  ordering?: string,
  search?: string,
  signal?: AbortSignal
) => {
  let url = `/glass/sim/msisdn/records?page=${page}&page_size=${pageSize}`;
  if (ordering === "asc") url += `&ordering=${field}`;
  if (ordering === "desc") url += `&ordering=-${field}`;
  if (search) url += `&search=${search}`;

  if (signal) return coreAxios.get(url, { signal });
  return coreAxios.get(url);
};

export const exportMsisdn = () => coreAxios.get("/glass/sim/msisdn/export");

export const totalMsisdn = () => coreAxios.get("/glass/sim/msisdn/count");

export const uploadBulkMsisdn = (formData, paramData) =>
  coreAxios.put(
    `/glass/sim/msisdn/profile/${paramData?.profileType}/factor/${paramData?.msisdnType}/documents`,
    formData
  );

export const getFreeMsisdn = (simProfile) =>
  coreAxios.get(`/glass/sim/msisdn/factor/${simProfile}`);

export const updateCard = (rowData: any, simProfile, msisdnNumber) =>
  coreAxios.patch(
    `/glass/sim/imsi/${rowData?.imsi}/profile/${simProfile}/msisdn/${msisdnNumber}`
  );

export const createImsiRanges = (formData, paramData) =>
  coreAxios.post("/glass/sim/documents", formData, paramData);

export const selectedCardDetails = (simData) =>
  coreAxios.put("/glass/sim/msisdn", simData);

export const downloadErrorCSV = (id) =>
  coreAxios.get(`/auditlog/request/${id}/document`);

export const getOperationList = (
  page: number,
  pageSize: number,
  ordering?: string,
  search?: string,
  user?: number
) => {
  let url = `/auditlog/request?page=${page}&page_size=${pageSize}`;

  if (ordering) url += `&ordering=${ordering}`;

  if (search) url += `&search=${search}`;

  if (user) url += `&account_id=${user}`;

  return coreAxios.get<IPaginationTotal<IOperation[], ITotalRow>>(url);
};

export const getOperationDetails = (traceId) =>
  coreAxios.get(`/auditlog/request/${traceId}`);

export const getSimActionByImsi = (
  payload,
  month: string | null,
  page: number,
  pageSize: number,
  ordering?: string,
  search?: string,
  signal?
) => {
  let url = `/auditlog/sim_action/records?page=${page}&page_size=${pageSize}`;
  if (month) url += `&month=${month}`;
  if (ordering) url += `&ordering=${ordering}`;
  if (search) url += `&search=${search}`;
  return coreAxios.post<IPagination<ISimAction[]>>(url, payload, { signal });
};

export const sendFlushSim = (simData) =>
  coreAxios.post("/glass/sim/cards/flush", simData);

export const sendPOD = (simData) =>
  coreAxios.post("/glass/sim/cards/POD", simData);

export const sendSMS = (simData) =>
  coreAxios.post("glass/sim/cards/SMS", simData);

export const simOrderingList = (
  page: number,
  pageSize: number,
  ordering?: string,
  search?: string,
  signal?: AbortSignal,
  accountId?: number,
) => {
  let url = `/glass/orders?page=${page}&page_size=${pageSize}`;
  if (ordering) url += `&ordering=${ordering}`;
  if (search) url += `&search=${search}`;
  if (accountId) url += `&account_id=${accountId}`;

  if (signal) return coreAxios.get<IPaginationTotal<IOrderDetails[], ITotalRow>>(url, { signal });
  return coreAxios.get<IPaginationTotal<IOrderDetails[], ITotalRow>>(url);
};

export const createSimOrder = (formData) => coreAxios.post('glass/orders', formData);

export const getSimOrderDetails = (
  orderId: number | string | undefined,
) => {
  let url = `/glass/orders/${orderId}`;

    return coreAxios.get<IOrderDetails>(url);
};

export const changeSimOrderStatus = (
  orderId: number | string | undefined,
  payload: any
) => {
  let url = `/glass/orders/${orderId}/status`;
  return coreAxios.patch<any>(url, payload);
};
