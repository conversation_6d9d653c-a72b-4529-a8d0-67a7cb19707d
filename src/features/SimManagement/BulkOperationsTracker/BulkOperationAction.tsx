import {
  Box,
  IconButton, Tooltip,
} from '@mui/material';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import { REPOSITORY, ROUTE_PERMISSION } from 'core/utilities/constants';
import React from 'react';
import { AiOutlineEye } from 'react-icons/ai';

const BulkOperationAction = (row, onBulkOperationActionClick) => {
  return (
    <Box>
        <CommonAuthwrapper
          permission={[ROUTE_PERMISSION.VIEW_TRACE_DETAILS]}
          repository={[REPOSITORY.AUDIT]}
        >
          <Tooltip title="View Report" arrow placement="top">
            <IconButton
              className="toggleActionButton imsi-reallocations__top-bar_create-button"
              sx={{
                visibility: 'hidden',
              }}
              onClick={(e) => onBulkOperationActionClick(e, row)}
            >
              <AiOutlineEye
                className="icon-color"
                data-testid="close-icon"
                strokeWidth={12}
                size={24}
              />
            </IconButton>
          </Tooltip>
        </CommonAuthwrapper>
    </Box>
  );
};
export default BulkOperationAction;
