import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import BulkOperationsTracker from '../BulkOperationsTracker';
import { getOperationList } from '../api.service';
import useBulkOperationsTrackerColumns from 'hooks/useBulkOperationsTrackerColumns';
import useSimManagementClientSearchParams from 'hooks/useSimManagementClientSearchParams';
import useMuiTableSearchParams from 'hooks/useMuiTableSearchParams';
import { organizationTypes } from 'user.model';

jest.mock('../api.service');
jest.mock('hooks/useBulkOperationsTrackerColumns');
jest.mock('hooks/useSimManagementClientSearchParams');
jest.mock('hooks/useMuiTableSearchParams');
jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    primaryColor: '#123456',
    getBrandColors: jest.fn().mockReturnValue({
      primary: '#123456',
      secondary: '#654321'
    })
  })
}));

const mockUser = {
  email: "<EMAIL>",
  firstName: "test",
  lastName: "user",
  id: "0f5e8253-2360-4898-9f32-30b8d9c9874a",
  organization: {
    name: "ABC",
    id: 91,
    parent_id: '1',
    type: organizationTypes.DISTRIBUTOR,
  },
};

const mockOperationData = {
    page: 1,
    pageSize: 10,
    lastPage: 1,
    totalCount: 2,
    results: [
      {
        id: "6821b69789196e4ec362b8766",
        requestId: "3fa85f64-5717-4562-b3fc-2c963f66afa0",
        accountId: "2",
        accountName: "xyuz",
        user: "<EMAIL>",
        createdDate: "2025-05-12 08:51:35.968000",
        clientIp: "127.0.0.1",
        action: "custom_allocation",
        field: "imsi",
        status: "pending",
      },
      {
        id: "6821b56a46434f0e05fb5bff",
        requestId: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        accountId: "1",
        accountName: "ABC",
        user: "<EMAIL>",
        createdDate: "2025-05-12 08:46:34.894000",
        clientIp: "127.0.0.1",
        action: "custom_allocation",
        field: "imsi",
        status: "pending",
      },
    ],
};

describe('BulkOperationsTracker', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    (getOperationList as jest.Mock).mockResolvedValue(mockOperationData);
    
    (useBulkOperationsTrackerColumns as jest.Mock).mockReturnValue({
      columns: [
        { field: 'requestId', headerName: 'Request ID' },
        { field: 'operationType', headerName: 'Operation Type' },
        { field: 'status', headerName: 'Status' }
      ]
    });
    
    (useSimManagementClientSearchParams as jest.Mock).mockReturnValue({
      setParamsToUrl: jest.fn(),
      getParamsFromUrl: jest.fn().mockReturnValue({
        page: 1,
        pageSize: 10,
        field: 'createdAt',
        sort: 'desc',
        search: ''
      }),
      defaultPagination: { page: 1, pageSize: 10 },
      defaultSort: { field: 'createdAt', sort: 'desc' },
      initialSearchValue: ''
    });
    
    (useMuiTableSearchParams as jest.Mock).mockReturnValue({
      generateParamsForUrl: jest.fn().mockReturnValue(new URLSearchParams())
    });
  });

  const renderComponent = () => {
    return render(
      <MemoryRouter initialEntries={['/bulk-operations']}>
        <Routes>
          <Route path="/bulk-operations" element={<BulkOperationsTracker user={mockUser} />} />
        </Routes>
      </MemoryRouter>
    );
  };

  test('handles API error gracefully', async () => {
    // Mock API error
    (getOperationList as jest.Mock).mockRejectedValue({
      response: {
        status: 500,
        data: { detail: 'Server error' }
      }
    });
    
    renderComponent();
    
    await waitFor(() => {
      expect(getOperationList).toHaveBeenCalled();
    });
    
    await waitFor(() => {
      expect(screen.getByText(/No available bulk operations logs./i)).toBeInTheDocument();
    });
  });

  test('handles 404 error with specific message', async () => {
    (getOperationList as jest.Mock).mockRejectedValue({
      response: {
        status: 404
      }
    });
    
    renderComponent();
    
    await waitFor(() => {
      expect(getOperationList).toHaveBeenCalled();
    });
    
    await waitFor(() => {
      expect(screen.getByText(/No available bulk operations logs./i)).toBeInTheDocument();
    });
  });

  test('opens operation details dialog when action is clicked', async () => {
    let actionClickHandler;
    (useBulkOperationsTrackerColumns as jest.Mock).mockImplementation(({ onBulkOperationsTrackerActionClick }) => {
      actionClickHandler = onBulkOperationsTrackerActionClick;
      return {
        columns: [
          { field: 'requestId', headerName: 'Request ID' },
          { field: 'operationType', headerName: 'Operation Type' },
          { field: 'status', headerName: 'Status' },
          { field: 'action', headerName: 'Operation' }
        ]
      };
    });
    
    renderComponent();
    
    await waitFor(() => {
      expect(getOperationList).toHaveBeenCalled();
    });
    
    const mockEvent = { stopPropagation: jest.fn() };
    const mockRow = { requestId: '1', operationType: 'ACTIVATE' };
    
    actionClickHandler(mockEvent, mockRow);
    
    expect(mockEvent.stopPropagation).toHaveBeenCalled();
    
    await waitFor(() => {
      expect(screen.getByTestId('sim-management-client')).toBeInTheDocument();
    });
  });
});
