import { Box } from '@mui/material';
import MuiTable from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';
import { MuiTableProvider } from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable/MuiTableContext';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import { useAppContext } from 'AppContextProvider';
import SimIconHeader from 'assets/images/SimIconHeader';
import { RESPONSE } from 'core/utilities/constants';
import { TGetCurrentThemeColors } from 'features/models';
import { IOperation } from 'features/SimManagement/SimManagement.models';
import { descending, sortFieldNames } from './constant';
import SimManagementClientTableActionsLeft from 'features/SimManagement/SimManagementClient/SimManagementClientTableActionsLeft';
import useBulkOperationsTrackerColumns from 'hooks/useBulkOperationsTrackerColumns';
import useMuiTableSearchParams from 'hooks/useMuiTableSearchParams';
import useSimManagementClientSearchParams from 'hooks/useSimManagementClientSearchParams';
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import ViewOperationDetailsDialog from './ViewOperationDetailsDialog';
import { getOperationList } from '../api.service';
import './BulkOperationsTracker.scss';
import { IUser } from 'user.model';

interface SimManagementClientProps {
  user: IUser | undefined,
}

const BulkOperationsTracker = ({ user }: SimManagementClientProps) => {
  const { primaryColor, getBrandColors } = useAppContext();
  const {
    setParamsToUrl,
    getParamsFromUrl,
    defaultPagination,
    defaultSort,
    initialSearchValue,
  } = useSimManagementClientSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [confirmDialog, setConfirmDialog] = useState({
    isEnable: false, rowData: null,
  });
  const onBulkOperationsTrackerActionClick = (event, row) => {
    event.stopPropagation();
    setConfirmDialog({
      isEnable: true, rowData: row,
    });
  };
  const { columns } = useBulkOperationsTrackerColumns({ onBulkOperationsTrackerActionClick });
  const [operations, setOperations] = useState<IOperation[]>([]);
  const [cardsTotalCount, setCardsTotalCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);
  const [noData, setNoData] = useState({});
  const { generateParamsForUrl } = useMuiTableSearchParams();

  const loadCards = async (page, pageSize, field, sort, search) => {
    const newSearchParams = generateParamsForUrl(
      page, pageSize, field, sort, search,
    );
  const newUrl = `${location?.pathname
    }?tab=bulk-operation&${newSearchParams?.toString()}`;
  navigate(newUrl, { replace: true });

    let ordering = sortFieldNames[field];

    if (sort === descending) {
      ordering = `-${ordering}`;
    }
    let userProfile = JSON.parse(localStorage.getItem('userDetails') || 'null');    
    const { data: operationData } = await getOperationList(page, pageSize, ordering, search, user?.organization.type === 'CLIENT' ? userProfile?.accountId : null); 
    const {
      results: resultsCards,
      totalCount,
    } = operationData;
    setOperations(resultsCards);
    setCardsTotalCount(totalCount);
  };

  const loadData = async (page, pageSize, field, sort, search) => {
    setParamsToUrl(page, pageSize, field, sort, search);
    try {
      setIsLoading(true);
      await loadCards(page, pageSize, field, sort, search);
    } catch (errorObj: any) {
      let message = '';
      if (errorObj?.response?.status === 404) {
        message = RESPONSE.GET_ALLOCATION_SEARCH_EMPTY;
      } else if (errorObj?.response?.status === 500) {
        message = RESPONSE.HTTP_STATUS_500;
      } else {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        message = errorObj?.response?.data?.detail;
      }
      setNoData({
        icon: <SimIconHeader />,
        title: 'No available bulk operations logs.',
        description: 'Please change filters or search query',
      });
      setCardsTotalCount(0);
      setOperations([]);
    } finally {
      setIsLoading(false);
    }
  };

  const onChange = ({ page, pageSize }, { field, sort }, search) => {
    loadData(page, pageSize, field, sort, search);
  };
  const getApiData = () => {
    const {
      page, pageSize, field, sort, search,
    } = getParamsFromUrl();

    loadData(page, pageSize, field, sort, search);
  };

  useEffect(() => {
    getApiData();
  }, []);

  return (
    <Box
      data-testid="sim-management-client"
      className="sim-management-client"
      sx={{
        background: styles.lightColor50,
        "& .MuiDataGrid-row-bold": {
          "& .MuiCheckbox-root": {
            display: "none !important",
          },
          "& .MuiDataGrid-main": {
            overflow: "hidden !important",
          },
          ":hover": {
            cursor: "default !important",
          },
        },
        "& .MuiDataGrid-columnHeaders": {
          backgroundColor: "#F5F5F9 !important",
        },
        "& .MuiDataGrid-virtualScrollerContent": {
          minHeight: "160px !important",
        },
        "& .MuiDataGrid-columnHeaderTitleContainerContent": {
          "& .MuiCheckbox-root": {
            display: "none !important",
          },
        },
        "& .interactions__actions": {
          justifyContent: "flex-start",
          "& .interactions__actions-selections": {
            display: "none",
          },
        },
      }}
    >
      <div className="">
        <MuiTableProvider
          defaultPagination={defaultPagination}
          onChange={onChange}
          onChangePagination={onChange}
          onChangeSearch={onChange}
          initialSearchValue={initialSearchValue}
          onChangeSort={onChange}
          defaultSort={defaultSort}
        >
          <MuiTable
            sx={{
              "& .MuiDataGrid-row:hover": {
                cursor: "pointer",
                "& .MuiButtonBase-root": {
                  visibility: "visible ",
                },
              },
            }}
            ActionsLeft={SimManagementClientTableActionsLeft}
            rows={operations}
            columns={columns}
            loading={isLoading}
            rowCount={cardsTotalCount}
            primaryColor={primaryColor as string}
            getCurrentThemeColors={getBrandColors as TGetCurrentThemeColors}
            showFirstLastPageButtons
            isVisibleSearchInput
            getRowId={(row) => `${row?.id}`}
            hideFooter={!cardsTotalCount}
            noDataConfig={noData}
          />
        </MuiTableProvider>
      </div>
      <ViewOperationDetailsDialog
        open={confirmDialog.isEnable}
        onClose={() => setConfirmDialog({ rowData: null, isEnable: false })}
        selectedRows={confirmDialog.rowData}
      />
    </Box>
  );
};

export default BulkOperationsTracker;
