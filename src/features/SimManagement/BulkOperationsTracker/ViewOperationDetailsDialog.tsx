import {
  Box, Button,
  Grid,
  Typography
} from '@mui/material';
import { toastError, toastSuccess } from 'core/utilities/toastHelper';
import React, { FC, useEffect, useState } from 'react';
import Dialog from 'shared/Dialog/Dialog';
import Loader from 'shared/Loader';
import { downloadErrorCSV, getOperationDetails } from '../api.service';
import exportCSVFile from 'core/utilities/exportCSVFile';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import { REPOSITORY, ROUTE_PERMISSION } from 'core/utilities/constants';
import formatImportantWords from 'core/utilities/formatImportantWords';

interface ViewOperationDetailsDialogProps {
  open: boolean;
  onClose: () => void;
  selectedRows: any;
}

const ViewOperationDetailsDialog: FC<ViewOperationDetailsDialogProps> = ({
  open, onClose, selectedRows,
}) => {
  const [loader, setLoader] = useState(false);
  const [details, setDetails] = useState<any>({});

  const fetchOperationData = async () => {
    try {
      setLoader(true);
      const { data } = await getOperationDetails(selectedRows?.id);
      setDetails(data);
      setLoader(false);
    } catch (error: any) {
      setLoader(false);
      console.log('error: ', error);
    }
  };

  const handleSubmit = async () => {
    try {
      setLoader(true);
      let fileName = "download.csv";
      const csvFileResponse = await downloadErrorCSV(details?.id);
      const contentDisposition = csvFileResponse.headers['content-disposition'];
      if (contentDisposition && contentDisposition.includes("filename=")) {
        fileName = contentDisposition.split("filename=")[1].trim();
      }
      exportCSVFile(csvFileResponse.data, fileName);
      toastSuccess('Data Downloaded Successfully');
      onClose();
      setLoader(false);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error: any) {
      toastError('Failed to download file.');
      setLoader(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchOperationData();
    }
    return () => {
      setDetails({});
    }
  }, [open]);

  const CompletedState = () => (
    <Box sx={{ width: "100%" }}>
      <Box>
        <Box sx={{ pb: "20px" }}>
          <Typography variant="body1">Request ID</Typography>
          <Typography variant="body1" sx={{ fontWeight: 700 }}>
            {details?.requestId}
          </Typography>
        </Box>
        <Box
          sx={{
            backgroundColor: "#F7F7F9",
            p: "16px",
            borderRadius: "4px",
            width: "100%",
          }}
        >
          <Grid container spacing="30px">
            <Grid item xs={4}>
              <Typography variant="body1">Total Records</Typography>
              <Typography variant="body1" sx={{ fontWeight: 700 }}>
                {details?.payload?.success_count +
                  details?.payload?.failure_count}
              </Typography>
            </Grid>
            <Grid item xs={4}>
              <Typography variant="body1">Success</Typography>
              <Typography variant="body1" sx={{ fontWeight: 700 }}>
                {details?.payload?.success_count}
              </Typography>
            </Grid>
            <Grid item xs={4}>
              <Typography variant="body1">Failed</Typography>
              <Typography variant="body1" sx={{ fontWeight: 700 }}>
                {details?.payload?.failure_count}
              </Typography>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </Box>
  );

  const RenderContentBasedOnStatus = () => {
    switch (details?.status) {
      case "Pending":
        return (
          <Box>
            <Typography variant="body1">
              Your report will be available shortly. Please check back after
              some time. Thanks for waiting!
            </Typography>
          </Box>
        );
      case "Completed":
        return <CompletedState />;
      case "Partially Completed":
        return <CompletedState />;
      case "Failed":
        if (details?.payload?.success_count === 0 && details?.payload?.failure_count === 0) {
          return (
            <Box sx={{ width: "100%" }}>
              <Box>
                <Box sx={{ pb: "20px" }}>
                  <Typography variant="body1">Request ID</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 700 }}>
                    {details?.requestId}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    backgroundColor: "#F7F7F9",
                    p: "16px",
                    borderRadius: "4px",
                    width: "100%",
                  }}
                >
                  <Grid container spacing="30px">
                    <Grid item xs={12}>
                      <Typography variant="body1" color="error">
                        {details?.payload?.message}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              </Box>
            </Box>
          );
        } else {
          return <CompletedState />;
        }
      default:
        return (
          <Box>
            <Typography variant="body1">
              No information available for this operation.
            </Typography>
          </Box>
        );
    }
  }; 
  return (
    <Dialog
      title={
        details?.status === "Pending"
          ? `We have received your request for ${formatImportantWords(details?.action) || ""}.`
          : `${formatImportantWords(details?.action) || ""}`
      }
      open={open}
      onClose={onClose}
      maxWidth="xs"
      sx={{
        "& .MuiDialogTitle-root": {
          p: 8,
        },
      }}
      footerchildren={
        !loader && (
          <Box
            display="flex"
            alignItems="flex-start"
            gap="15px"
            justifyContent="space-between"
          >
            <CommonAuthwrapper
              permission={[ROUTE_PERMISSION.EXPORT_TRACE_ERROR_DATA]}
              repository={[REPOSITORY.AUDIT]}
            >
              {details?.payload?.failure_count > 0 &&
              details?.status !== "Pending" ? (
                <Button
                  sx={{ p: "0px 25px", minWidth: "110px" }}
                  variant="contained"
                  color="primary"
                  disabled={loader || details?.payload?.failure_count <= 0}
                  onClick={() => handleSubmit()}
                >
                  {`Download Invalid Details`}
                </Button>
              ) : (
                <></>
              )}
            </CommonAuthwrapper>
            <Button
              sx={{ p: "0px 25px", backgroundColor: "#ebe3f6", border: "0px" }}
              variant="outlined"
              onClick={onClose}
            >
              Ok
            </Button>
          </Box>
        )
      }
    >
      <Box
        width={380}
        height={details?.status !== "Pending" ? 150 : "auto"}
        minHeight={loader ? 230 : 100}
        pb={5}
        display="flex"
        alignItems="start"
        flexDirection="column"
        position="relative"
      >
        {/* Loader overlay that appears on top of all content */}
        {loader ? (
          <Box
            sx={{
              position: "absolute",
              width: "100%",
              height: "100%",
              display: "flex",
              backdropFilter: "blur(1px)",
              zIndex: 3,
            }}
          >
            <Loader size={60} />
          </Box>
        ) : (
          <RenderContentBasedOnStatus />
        )}
      </Box>
    </Dialog>
  );
};

export default ViewOperationDetailsDialog;
