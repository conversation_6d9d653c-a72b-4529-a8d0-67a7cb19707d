import React from 'react';

import testRender from 'core/utilities/testUtils';
import SimManagementPlaceholder from './SimManagementPlaceholder';

const localRender = (title: string, state) => (
  testRender(<SimManagementPlaceholder
    title={title}
    loading={state}
  />)
);

describe('SimManagementPlaceholder', () => {
  test('should be rendered', () => {
    const { getByTestId } = localRender('header test', true);
    const placeholder = getByTestId('sim-management-placeholder');

    expect(placeholder).toBeInTheDocument();
  });
  test('should render proper loader', () => {
    const { getByTestId } = localRender('header test', true);
    const placeholderHeader = getByTestId('sim-management-loader');

    expect(placeholderHeader).toBeInTheDocument();
  });
});
