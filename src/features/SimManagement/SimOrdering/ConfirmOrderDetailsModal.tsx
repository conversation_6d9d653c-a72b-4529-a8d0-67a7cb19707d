import { Box, Button, Grid, IconButton, Modal, Typography } from "@mui/material";
import React from "react";
import { GrClose } from "react-icons/gr";
import { StyledCreateAllocationModal } from "../IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/componentUIparts";
import "./confirmOrderDetailsModal.scss";
import { displayFullFormFactor } from "../IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants";
import { LoadingButton } from "@mui/lab";

interface IConfirmOrderProps {
  open: boolean;
  setOpen: Function;
  handleSubmitForm: Function;
  formValues: any;
  loading: boolean;
}

const ConfirmOrderDetailsModal: React.FC<IConfirmOrderProps> = ({
  open,
  setOpen,
  handleSubmitForm,
  formValues,
  loading,
}) => {
  const handleClose = () => {
    setOpen(false);
  };

  const handleBackDrop = (event, reason) => {
    if (reason && (reason === "backdropClick" || reason === "escapeKeyDown")) {
      return;
    }
    handleClose();
  };

  return (
    <Box>
      <Modal open={open} onClose={handleBackDrop}>
        <StyledCreateAllocationModal
          data-testid="confirm-order-modal"
          className="confirm-order-modal"
        >
          <Box>
            <div className="confirm-order-modal__header">
              <Typography
                variant="h2"
                component="h3"
                fontWeight={700}
                className="confirm-order-modal__title"
              >
                Confirm Order
              </Typography>
              <IconButton
                data-testid="confirm-order-modal__close-button"
                type="button"
                onClick={handleClose}
                
              >
                <GrClose size={21} />
              </IconButton>
            </div>
            <div className="confirm-order-modal__confirmation-text">
              <Typography variant="body1">
                Are you sure you want to place this order?
              </Typography>
            </div>
            <div className="confirm-order-modal__order-details">
              <Grid
                container
                spacing={2}
                className="confirm-order-modal__table-header"
              >
                <Grid item xs={6}>
                  <Typography
                    variant="body1"
                    fontWeight={700}
                    className="confirm-order-modal__table-header-text"
                  >
                    SIM Types
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography
                    variant="body1"
                    fontWeight={700}
                    className="confirm-order-modal__table-header-text"
                  >
                    Quantity
                  </Typography>
                </Grid>
              </Grid>
              {formValues?.values?.orderItems?.map((val, index) => (
                <Grid
                  container
                  spacing={2}
                  key={index}
                  className="confirm-order-modal__table-row"
                >
                  <Grid item xs={6}>
                    <Typography
                      variant="body1"
                      className="confirm-order-modal__table-cell-text"
                    >
                      {displayFullFormFactor[val?.simType || 'SIM']}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body1"
                      className="confirm-order-modal__table-cell-text"
                    >
                      {val?.quantity}
                    </Typography>
                  </Grid>
                </Grid>
              ))}
            </div>
          </Box>
          <div className="confirm-order-modal__buttons">
            <LoadingButton
              onClick={(e) => {
                e.preventDefault();
                handleSubmitForm(formValues?.values);
              }}
              component="button"
              sx={{
                p: "0px 25px",
                minWidth: "110px",
                textTransform: "uppercase",
                fontSize: "12px !important",
              }}
              variant="contained"
              color="primary"
              onMouseDown={(e) => e.preventDefault()}
              data-testid="confirm-order-modal__confirm-button"
              className="confirm-order-modal__confirm-button"
              loading={loading}
            >
              Confirm
            </LoadingButton>
            <Button
              className="confirm-order-modal__cancel-button"
              sx={{
                p: "0px 25px",
                backgroundColor: "#ebe3f6",
                border: "0px",
                textTransform: "uppercase",
                fontSize: "12px !important",
              }}
              variant="outlined"
              disabled={loading}
              onClick={handleClose}
              onMouseDown={(e) => e.preventDefault()}
              data-testid="confirm-order-modal__cancel-button"
            >
              Cancel
            </Button>
          </div>
        </StyledCreateAllocationModal>
      </Modal>
    </Box>
  );
};

export default ConfirmOrderDetailsModal;
