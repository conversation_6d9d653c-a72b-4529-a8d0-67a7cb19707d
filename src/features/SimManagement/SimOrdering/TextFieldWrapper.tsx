import { TextField } from "@mui/material";
import { getIn, useField } from "formik";
import React from "react";
import { TextFieldWrapperProps } from "../SimManagement.models";

const TextFieldWrapper: React.FC<TextFieldWrapperProps> = ({
  name,
  formikValue,
  ...otherProps
}) => {
  const [field, meta] = useField(name);
  const {pattern, ...restProps} = otherProps;
  
  const configTextField = {
    ...field,
    ...restProps,
    value: getIn(formikValue.values, name) || "",
    fullWidth: true,
    variant: "outlined" as const,
    ...(!restProps?.helperText && {
      helperText: meta.touched && meta.error ? meta.error : "",
    }),
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      if (pattern) {
        if (pattern.test(e.target.value)) formikValue.setFieldValue(name, e.target.value);
      } else {
        formikValue.setFieldValue(name, e.target.value);
      }
    },
    onBlur: () => formikValue.setFieldTouched(name, true),
  };
  
  return <TextField {...configTextField} />;
};

export default TextFieldWrapper;
