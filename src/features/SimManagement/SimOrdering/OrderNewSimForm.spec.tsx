import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import SimDetailsDrawer from './SimDetailsDrawer';
import { changeSimOrderStatus, getSimOrderDetails } from '../api.service';
import { toastError, toastSuccess } from 'core/utilities/toastHelper';
import { GetAuthorization } from 'PrivateRotes';
import { IUser, organizationTypes } from 'user.model';

// Mock dependencies
jest.mock('../api.service');
jest.mock('core/utilities/toastHelper');
jest.mock('PrivateRotes');
jest.mock('core/utilities/constants', () => ({
  simOrderStatusColorMap: () => ({
    shipped: '#4caf50',
    cancelled: '#f44336',
    approved: '#2196f3',
    pending: '#ff9800'
  }),
  ROUTE_PERMISSION: {
    UPDATE_ORDER_STATUS: 'UPDATE_ORDER_STATUS'
  },
  REPOSITORY: {
    SIM_ORDERS: 'SIM_ORDERS'
  }
}));

const mockedChangeSimOrderStatus = changeSimOrderStatus as jest.MockedFunction<typeof changeSimOrderStatus>;
const mockedGetSimOrderDetails = getSimOrderDetails as jest.MockedFunction<typeof getSimOrderDetails>;
const mockedToastError = toastError as jest.MockedFunction<typeof toastError>;
const mockedToastSuccess = toastSuccess as jest.MockedFunction<typeof toastSuccess>;
const mockedGetAuthorization = GetAuthorization as jest.MockedFunction<typeof GetAuthorization>;

const theme = createTheme();

const mockOrderDetails = {
  orderTracking: '',
  orderId: 123,
  orderBy: 'John Doe',
  customerAccountName: 'ABC Corp',
  orderDate: '2023-12-01T10:00:00Z',
  customerEmail: '<EMAIL>',
  customerPhone: '+**********',
  status: 'APPROVED',
  comments: '',
  customerAccountLogoUrl: '',
  orderItem: [
    { quantity: 10, simType: 'Standard SIM' },
    { quantity: 5, simType: 'Micro SIM' }
  ],
};

const mockSimOrderDetails = {
  customerDetails: {
    customerReference: 'CUST-001',
    personPlacingOrder: 'Jane Smith',
    customerEmail: '<EMAIL>',
    customerContactNo: '+**********'
  },
  shippingDetails: {
    contactName: 'John Doe',
    addressLine1: '123 Main St',
    addressLine2: 'Apt 4B',
    city: 'New York',
    postalCode: '10001',
    stateOrRegion: 'NY',
    country: 'US',
    otherInformation: 'Leave at front door'
  },
  status: 'PENDING',
  comments: '',
  orderTracking: {
    referenceUrl: '',
    referenceId: ''
  }
};

const mockUser: IUser = {
  id: 'dec3c',
  email: '<EMAIL>',
  firstName: 'name',
  lastName: 'last name',
  organization: {
    id: 12,
    name: 'oName',
    parent_id: '12dd3',
    type: organizationTypes.DISTRIBUTOR,
  },
};

const defaultProps = {
  user: mockUser,
  open: true,
  onClose: jest.fn(),
  orderDetails: mockOrderDetails,
  listFunction: jest.fn()
};

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('SimDetailsDrawer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockedGetSimOrderDetails.mockResolvedValue({
      data: mockSimOrderDetails
    } as any);
    mockedGetAuthorization.mockReturnValue(true);
  });

  describe('Component Rendering', () => {
    it('should render drawer with order details when authorized', async () => {
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      expect(screen.getByText('Order Summary')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });

    it('should display loading skeletons while fetching data', () => {
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      expect(screen.getAllByTestId('skeleton')).toBeTruthy();
    });

    it('should render order status dropdown when authorized', async () => {
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByRole('combobox')).toBeInTheDocument();
      });
    });
  });

  describe('Form Validation', () => {
    it('should validate required tracking fields when status is SHIPPED', async () => {
      const user = userEvent.setup();
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByRole('combobox')).toBeInTheDocument();
      });

      // Change status to SHIPPED
      const statusSelect = screen.getByRole('combobox');
      await user.click(statusSelect);
      
      const shippedOption = screen.getByText('Ship Order');
      await user.click(shippedOption);

      // Submit without required fields
      const confirmButton = screen.getByText('CONFIRM');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(screen.getByText('Tracking URL is required when order is shipped')).toBeInTheDocument();
        expect(screen.getByText('Tracking reference is required when order is shipped')).toBeInTheDocument();
      });
    });

    it('should validate tracking URL format', async () => {
      const user = userEvent.setup();
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByRole('combobox')).toBeInTheDocument();
      });

      // Change status to SHIPPED
      const statusSelect = screen.getByRole('combobox');
      await user.click(statusSelect);
      
      const shippedOption = screen.getByText('Ship Order');
      await user.click(shippedOption);

      // Enter invalid URL
      const trackingUrlField = screen.getByLabelText('Tracking URL');
      await user.type(trackingUrlField, 'invalid-url');

      const confirmButton = screen.getByText('CONFIRM');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter a valid URL, eg: https://www.tracking.com')).toBeInTheDocument();
      });
    });
  });

  describe('Status Change Workflows', () => {
    it('should handle SHIPPED status change successfully', async () => {
      const user = userEvent.setup();
      mockedChangeSimOrderStatus.mockResolvedValue({} as any);
      
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByRole('combobox')).toBeInTheDocument();
      });

      // Change status to SHIPPED
      const statusSelect = screen.getByRole('combobox');
      await user.click(statusSelect);
      
      const shippedOption = screen.getByText('Ship Order');
      await user.click(shippedOption);

      // Fill required fields
      const trackingUrlField = screen.getByLabelText('Tracking URL');
      await user.type(trackingUrlField, 'https://tracking.example.com/123');

      const trackingRefField = screen.getByLabelText('Tracking Reference');
      await user.type(trackingRefField, 'TRK-123');

      const confirmButton = screen.getByText('CONFIRM');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(mockedChangeSimOrderStatus).toHaveBeenCalledWith(123, {
          status: 'SHIPPED',
          tracking: {
            referenceId: 'TRK-123',
            referenceUrl: 'https://tracking.example.com/123'
          }
        });
        expect(mockedToastSuccess).toHaveBeenCalledWith('Order status updated successfully');
        expect(defaultProps.listFunction).toHaveBeenCalledWith(1, 10, '-orderDate');
        expect(defaultProps.onClose).toHaveBeenCalled();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle API error when fetching order details', async () => {
      mockedGetSimOrderDetails.mockRejectedValue(new Error('API Error'));
      
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(mockedToastError).toHaveBeenCalledWith('Failed to load SIM order details');
        expect(defaultProps.onClose).toHaveBeenCalled();
      });
    });
  });

  describe('User Interactions', () => {
    it('should handle form field blur events', async () => {
      const user = userEvent.setup();
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByRole('combobox')).toBeInTheDocument();
      });

      const statusSelect = screen.getByRole('combobox');
      await user.click(statusSelect);
      await user.tab(); // Blur the field
      
      // Field should be marked as touched
      expect(statusSelect).toBeInTheDocument();
    });

    it('should handle tracking field blur events', async () => {
      const user = userEvent.setup();
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByRole('combobox')).toBeInTheDocument();
      });

      // Change status to SHIPPED to show tracking fields
      const statusSelect = screen.getByRole('combobox');
      await user.click(statusSelect);
      
      const shippedOption = screen.getByText('Ship Order');
      await user.click(shippedOption);

      await waitFor(() => {
        expect(screen.getByLabelText('Tracking URL')).toBeInTheDocument();
      });

      const trackingUrlField = screen.getByLabelText('Tracking URL');
      await user.click(trackingUrlField);
      await user.tab(); // Blur the field
      
      expect(trackingUrlField).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty order items array', async () => {
      const propsWithEmptyItems = {
        ...defaultProps,
        orderDetails: { ...mockOrderDetails, orderItem: [] }
      };
      
      renderWithTheme(<SimDetailsDrawer {...propsWithEmptyItems} />);
      
      expect(screen.getByText('Order Summary')).toBeInTheDocument();
    });

    it('should handle missing customer details', async () => {
      const orderDetailsWithMissingData = {
        ...mockSimOrderDetails,
        customerDetails: {
          customer_name: '',
          customer_reference: '',
          person_placing_order: '',
          customer_email: '',
          customer_contact_no: ''
        }
      };
      
      mockedGetSimOrderDetails.mockResolvedValue({
        data: orderDetailsWithMissingData
      } as any);
      
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('Customer Details')).toBeInTheDocument();
      });
    });

    it('should handle null/undefined other information', async () => {
      const orderDetailsWithNullInfo = {
        ...mockSimOrderDetails,
        shippingDetails: {
          ...mockSimOrderDetails.shippingDetails,
          otherInformation: null
        }
      };
      
      mockedGetSimOrderDetails.mockResolvedValue({
        data: orderDetailsWithNullInfo
      } as any);
      
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('N/A')).toBeInTheDocument();
      });
    });

    it('should handle country mapping in shipping address', async () => {
      const orderDetailsWithCountry = {
        ...mockSimOrderDetails,
        shippingDetails: {
          ...mockSimOrderDetails.shippingDetails,
          country: 'US'
        }
      };
      
      mockedGetSimOrderDetails.mockResolvedValue({
        data: orderDetailsWithCountry
      } as any);
      
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('Customer Shipping Address')).toBeInTheDocument();
      });
    });

    it('should handle form reinitialization when orderDetails change', async () => {
      const { rerender } = renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      const newOrderDetails = {
        ...mockOrderDetails,
        orderId: 456,
        status: 'APPROVED',
      };
      
      const newProps = {
        ...defaultProps,
        orderDetails: newOrderDetails,
      };
      
      rerender(
        <ThemeProvider theme={theme}>
          <SimDetailsDrawer {...newProps} />
        </ThemeProvider>
      );
      
      expect(screen.getByText('Order Summary')).toBeInTheDocument();
    });

    it('should handle drawer close on backdrop click', async () => {
      const user = userEvent.setup();
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      // Simulate clicking outside the drawer
      const backdrop = document.querySelector('.MuiBackdrop-root');
      if (backdrop) {
        await user.click(backdrop);
        expect(defaultProps.onClose).toHaveBeenCalled();
      }
    });
  });

  describe('Authorization Tests', () => {
    it('should call GetAuthorization with correct parameters', () => {
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      expect(mockedGetAuthorization).toHaveBeenCalledWith(
        ['UPDATE_ORDER_STATUS'],
        ['SIM_ORDERS']
      );
    });
  });

  describe('Form State Management', () => {
    it('should set editableForm to false for PENDING status', async () => {
      const pendingOrderDetails = {
        ...mockSimOrderDetails,
        status: 'PENDING',
        comments: '',
      };
      
      mockedGetSimOrderDetails.mockResolvedValue({
        data: pendingOrderDetails
      } as any);
      
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        const statusSelect = screen.getByRole('combobox');
        expect(statusSelect).not.toBeDisabled();
      });
    });

    it('should set editableForm to false for APPROVED status', async () => {
      const approvedOrderDetails = {
        ...mockSimOrderDetails,
        status: 'APPROVED',
        comments: '',
      };
      
      mockedGetSimOrderDetails.mockResolvedValue({
        data: approvedOrderDetails
      } as any);
      
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        const statusSelect = screen.getByRole('combobox');
        expect(statusSelect).not.toBeDisabled();
      });
    });

    it('should handle form values when tracking data is missing', async () => {
      const orderDetailsWithoutTracking = {
        ...mockSimOrderDetails,
        status: 'PENDING',
        comments: '',
        orderTracking: {
          referenceUrl: '',
          referenceId: ''
        }
      };
      
      mockedGetSimOrderDetails.mockResolvedValue({
        data: orderDetailsWithoutTracking
      } as any);
      
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByRole('combobox')).toBeInTheDocument();
      });
    });

    it('should handle form field changes correctly', async () => {
      const user = userEvent.setup();
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByRole('combobox')).toBeInTheDocument();
      });

      // Change to SHIPPED status
      const statusSelect = screen.getByRole('combobox');
      await user.click(statusSelect);
      
      const shippedOption = screen.getByText('Ship Order');
      await user.click(shippedOption);

      await waitFor(() => {
        expect(screen.getByLabelText('Tracking URL')).toBeInTheDocument();
      });

      // Type in tracking URL
      const trackingUrlField = screen.getByLabelText('Tracking URL');
      await user.type(trackingUrlField, 'https://test.com');

      expect(screen.getByDisplayValue('https://test.com')).toBeInTheDocument();
    });
  });

  describe('Data Loading and Error States', () => {
    it('should handle loading state correctly', async () => {
      // Mock a delayed response
      mockedGetSimOrderDetails.mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({ data: mockSimOrderDetails } as any), 100)
        )
      );
      
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      // Should show skeletons during loading
      expect(screen.getAllByTestId('skeleton')).toBeTruthy();
      
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      }, { timeout: 200 });
    });

    it('should handle API timeout error', async () => {
      mockedGetSimOrderDetails.mockRejectedValue(new Error('Request timeout'));
      
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(mockedToastError).toHaveBeenCalledWith('Failed to load SIM order details');
        expect(defaultProps.onClose).toHaveBeenCalled();
      });
    });

    it('should handle network error', async () => {
      mockedGetSimOrderDetails.mockRejectedValue(new Error('Network Error'));
      
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(mockedToastError).toHaveBeenCalledWith('Failed to load SIM order details');
        expect(defaultProps.onClose).toHaveBeenCalled();
      });
    });

    it('should handle malformed API response', async () => {
      mockedGetSimOrderDetails.mockResolvedValue({
        data: null
      } as any);
      
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('Order Summary')).toBeInTheDocument();
      });
    });
  });

  describe('Component Lifecycle', () => {
    it('should fetch data when orderDetails.orderId changes', async () => {
      const { rerender } = renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      expect(mockedGetSimOrderDetails).toHaveBeenCalledWith(123);
      
      const newProps = {
        ...defaultProps,
        orderDetails: { ...mockOrderDetails, orderId: 456 }
      };
      
      rerender(
        <ThemeProvider theme={theme}>
          <SimDetailsDrawer {...newProps} />
        </ThemeProvider>
      );
      
      await waitFor(() => {
        expect(mockedGetSimOrderDetails).toHaveBeenCalledWith(456);
      });
    });

    it('should cleanup on unmount', () => {
      const { unmount } = renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      unmount();
      
      // Component should unmount without errors
      expect(true).toBe(true);
    });

    it('should handle rapid prop changes', async () => {
      const { rerender } = renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      // Rapidly change order IDs
      for (let i = 200; i < 205; i++) {
        const newProps = {
          ...defaultProps,
          orderDetails: { ...mockOrderDetails, orderId: i }
        };
        
        rerender(
          <ThemeProvider theme={theme}>
            <SimDetailsDrawer {...newProps} />
          </ThemeProvider>
        );
      }
      
      // Should handle rapid changes without errors
      expect(mockedGetSimOrderDetails).toHaveBeenCalledTimes(6); // Initial + 5 changes
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', async () => {
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByRole('combobox')).toBeInTheDocument();
      });
      
      expect(screen.getByRole('combobox')).toHaveAttribute('aria-expanded');
    });

    it('should have proper form labels', async () => {
      const user = userEvent.setup();
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByRole('combobox')).toBeInTheDocument();
      });

      // Change to SHIPPED to show form fields
      const statusSelect = screen.getByRole('combobox');
      await user.click(statusSelect);
      
      const shippedOption = screen.getByText('Ship Order');
      await user.click(shippedOption);

      await waitFor(() => {
        expect(screen.getByLabelText('Tracking URL')).toBeInTheDocument();
        expect(screen.getByLabelText('Tracking Reference')).toBeInTheDocument();
      });
    });

    it('should announce form errors to screen readers', async () => {
      const user = userEvent.setup();
      renderWithTheme(<SimDetailsDrawer {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByRole('combobox')).toBeInTheDocument();
      });

      // Change to SHIPPED
      const statusSelect = screen.getByRole('combobox');
      await user.click(statusSelect);
      
      const shippedOption = screen.getByText('Ship Order');
      await user.click(shippedOption);

      // Submit without filling required fields
      const confirmButton = screen.getByText('CONFIRM');
      await user.click(confirmButton);

      await waitFor(() => {
        const errorMessage = screen.getByText('Tracking URL is required when order is shipped');
        expect(errorMessage).toHaveAttribute('id');
      });
    });
  });
});
