/* stylelint-disable property-no-vendor-prefix, selector-attribute-quotes */
.customer-account-page {
  background: $white-color;
  border-radius: 4px;

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type=number] {
    -moz-appearance: textfield;
  }

  &_header {
    padding: 20px 20px 10px;
  }

  &_title {
    margin-right: 40px !important;
    margin-left: 10px !important;
    color: $dark-3;
  }

  &_button {
    text-transform: capitalize !important;
  }

  &__loader {
    display: flex;
    min-height: 500px;
  }
}
