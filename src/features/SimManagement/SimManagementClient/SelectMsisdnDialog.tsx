import {
  Box, Button, FormControl, InputLabel, MenuItem, Select,
  Typography,
} from '@mui/material';
import { RESPONSE, SIMPROFILE_OPTIONS } from 'core/utilities/constants';
import { toastError, toastSuccess } from 'core/utilities/toastHelper';
import React, { FC, useEffect, useState } from 'react';
import { GrFormDown } from 'react-icons/gr';
import Dialog from 'shared/Dialog/Dialog';
import Loader from 'shared/Loader';
import { selectedCardDetails, totalMsisdn } from '../api.service';
import { IFreeMsisdn } from '../IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/models';
import { MSISDN_TYPE_OPTIONS } from '../IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants';

interface selectedMsisdnDialogProps {
  open: boolean;
  onClose: () => void;
  selectedIMSIs: string[];
  selectedRows: string[];
  sims: any;
  reCallData: any,
  paramState: any,
  setSelectedRows: React.Dispatch<React.SetStateAction<string[]>>
}

const SelectMsisdnDialog: FC<selectedMsisdnDialogProps> = ({
  open, onClose, selectedIMSIs, selectedRows, sims, reCallData, paramState, setSelectedRows,
}) => {
  const [selectedProfile, setSelectedProfile] = useState('');
  const [msisdnType, setMsisdnType] = useState('');
  const [loader, setLoader] = useState(false);
  const [totalRemainingCount, setTotalRemainingCount] = useState<IFreeMsisdn>({
    totalCount: 0,
    national: 0,
    international: 0,
  });
  const firstSelectedSim = sims.find(
    (sim) => sim?.simId?.toString() === selectedRows[0]?.toString(),
  );
  const defaultSimProfile = firstSelectedSim?.simProfile?.ref;
  const defaultMsisdnType = firstSelectedSim?.msisdnFactor?.ref;

  const fetchTotalRemainingCount = async () => {
    try {
      setLoader(true);
      const { data } = await totalMsisdn();
      setTotalRemainingCount(data);
      setLoader(false);
    } catch (error: any) {
      setLoader(false);
      console.log('error: ', error);
    }
  };

  const filteredArray = sims
    .filter((sim) => selectedRows.includes(sim.simId.toString()))
    .map(({ imsi, msisdn }) => ({ imsi, msisdn }));

  const handleSubmit = async () => {
    try {
      setLoader(true);
      const payload = {
        msisdnFactor: msisdnType === '-2' ? defaultMsisdnType : msisdnType,
        simProfile: selectedProfile === '-2' ? defaultSimProfile : selectedProfile,
        // msisdn: null,
        // imsis: selectedIMSIs,
        msisdnMap: filteredArray,
      };
      const selectedImsi = await selectedCardDetails(payload);
      toastSuccess(selectedImsi?.data?.message);
      onClose();
      setLoader(false);
      const {
        page, pageSize, field, sort, search,
      } = paramState;
      reCallData(page, pageSize, field, sort, search);
      setSelectedRows([]);
    } catch (error: any) {
      toastError('Failed to update the selected IMSIs. Please try again later.');
      setLoader(false);
    }
  };

  const isCountAvailable = msisdnType === '-2' ? true : totalRemainingCount?.[msisdnType.toLowerCase()] > 0;

   
  const checkInternationandSimprofile = msisdnType === 'INTERNATIONAL' && (selectedProfile === 'DATA_ONLY' ? false : selectedProfile === 'VOICE_SMS_DATA' || defaultSimProfile === 'VOICE_SMS_DATA');

  const filteredProfileOptions = SIMPROFILE_OPTIONS.filter(
    (option) => option.value !== defaultSimProfile
      && defaultMsisdnType !== 'INTERNATIONAL',
  );

  const filteredMsisdnType = MSISDN_TYPE_OPTIONS.filter(
    (option) => option.value !== defaultMsisdnType,
  );

  const getUpdatedMsisdnOptions = (apiResponse) => filteredMsisdnType.map((option) => ({
    ...option,
    count: apiResponse[option.value.toLowerCase()] || 0,
  }));

  useEffect(() => {
    if (open) {
      setSelectedProfile('-2');
      setMsisdnType('-2');
      fetchTotalRemainingCount();
    }
  }, [open]);

  return (
    <Dialog
      title="Bulk Update IMSI(s)"
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiDialogTitle-root': {
          p: 8,
        },
      }}
      footerchildren={(
        <Box display="flex" alignItems="flex-start" gap="15px" justifyContent="space-between">
          <Button
            sx={{ p: '0px 25px', minWidth: '110px' }}
            variant="contained"
            color="primary"
            disabled={
              (!selectedProfile || selectedProfile === '-2')
              && (!msisdnType || msisdnType === '-2')
              || checkInternationandSimprofile
              || loader
              || !isCountAvailable
            }
            onClick={() => handleSubmit()}
          >
            Confirm
          </Button>
          <Button
            sx={{ p: '0px 25px', backgroundColor: '#ebe3f6', border: '0px' }}
            variant="outlined"
            onClick={onClose}
          >
            Cancel
          </Button>
        </Box>
      )}
    >
      <Box width={380} pb={5} display="flex" alignItems="start" flexDirection="column" position="relative">
        {/* { !loader ? ( */}
        <Box sx={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: loader ? 'flex' : 'none',
          backdropFilter: 'blur(1px)',
          zIndex: 3,
          // background: 'rgba(0, 0, 0, 0.5)',
        }}
        >
          <Loader size={60} />
        </Box>
        {/* )
    : ( */}
        {/* <> */}
        <Typography variant="body1">
          After confirmation,
          {' '}
          <b>
            {selectedIMSIs.length}
          </b>
          {' '}
          IMSI(s) will be updated.
        </Typography>

        <FormControl
          fullWidth
          className="sim-profile-select"
          sx={{
            margin: '20px 10px 4px 0px',
            '#sim-profile-label.MuiInputLabel-root': {
              transform: 'translate(20px, 9px) scale(1) !important',
            },
            '#sim-profile-label.MuiInputLabel-shrink': {
              transform: 'translate(14px, -6px) scale(0.75) !important',
              padding: '0px 5px',
              background: 'white',
            },
            '.select-arrow-icon': {
              position: 'absolute',
              right: '12px',
              zIndex: 1,
              polyline: {
                stroke: '#696969',
              },
            },
          }}
        >
          <InputLabel id="sim-profile-label">SIM Profile</InputLabel>
          <Select
            IconComponent={() => (<GrFormDown className="select-arrow-icon" size={21} />)}
            labelId="sim-profile-label"
            value={selectedProfile}
            onChange={(event) => setSelectedProfile(event.target.value)}
          >
            {[
              { title: 'No Changes', value: '-2' },
              ...filteredProfileOptions,
            ].map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.title}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Typography
          variant="body1"
          fontWeight="600"
          fontSize={11}
          color="#5514B4"
          mb={4}
          visibility={selectedProfile === '-2' ? 'visible' : 'hidden'}
        >
          SIM Profile will not be updated
        </Typography>

        <FormControl
          fullWidth
          className="sim-profile-select"
          sx={{
            margin: msisdnType ? '0px 10px 5px 0px' : '0px 10px 20px 0px',
            '#sim-profile-label.MuiInputLabel-root': {
              transform: 'translate(20px, 9px) scale(1) !important',
            },
            '#sim-profile-label.MuiInputLabel-shrink': {
              transform: 'translate(14px, -6px) scale(0.75) !important',
              padding: '0px 5px',
              background: 'white',
            },
            '.select-arrow-icon': {
              position: 'absolute',
              right: '12px',
              zIndex: 1,
              polyline: {
                stroke: '#696969',
              },
            },
          }}
        >
          <InputLabel id="sim-profile-label">MSISDN Type</InputLabel>
          <Select
            IconComponent={() => (<GrFormDown className="select-arrow-icon" size={21} />)}
            labelId="sim-profile-label"
            value={msisdnType}
            onChange={(event) => { setMsisdnType(event.target.value); }}
          >
            {[{ title: 'No Changes', value: '-2', count: null }, ...getUpdatedMsisdnOptions(totalRemainingCount)]?.map((msisdn) => (
              <MenuItem key={msisdn.value} value={msisdn.value}>
                {msisdn?.title}
                &nbsp;
                <span style={{
                  display: msisdn?.value === '-2' ? 'none' : 'block',
                  color: msisdn?.count > 0 ? 'green' : 'red',
                }}
                >
                  {msisdn?.count > 0 ? `(Remaining ${msisdn?.count})` : '(Not Available)'}
                </span>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {
          checkInternationandSimprofile && (
            <Typography
              variant="body1"
              fontWeight="600"
              fontSize={11}
              color="#5514B4"
              mb={4}
            >
              {RESPONSE?.MSISDN_TYPE_CASE}
            </Typography>
          )
        }

        <Typography
          variant="body1"
          fontWeight="600"
          fontSize={11}
          color="#5514B4"
          mb={4}
          visibility={msisdnType === '-2' ? 'visible' : 'hidden'}
          display={msisdnType === '-2' ? 'block' : 'none'}
        >
          MSISDN Number will not be updated
        </Typography>
        {/* </> */}
        {/* )} */}
      </Box>

    </Dialog>
  );
};

export default SelectMsisdnDialog;
