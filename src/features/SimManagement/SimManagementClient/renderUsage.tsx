import { Box } from '@mui/material';
import { bytesToMb, getNumberWithComasEsacpe } from 'core/utilities/toMoneyFormat';
import React from 'react';
import { Link } from 'react-router-dom';

export default function renderUsage(row) {
  const dataUsage = row?.usage || 0;
  if (row?.id !== 'TOTAL') {
    return (
      <Box sx={{
        cursor: 'pointer',
        '& .sim-usage-btn': {
          textDecoration: 'none',
          color: 'rgba(0, 0, 0, 0.87)',
          fontFamily: 'BT Curve,sans-serif',
          fontWeight: 400,
          fontSize: '14px',
          lineHeight: 1.43,
          cursor: 'pointer',
        },
      }}
      >
        <Link to={`${row.imsi}/sim-detail#connection-history`} className="sim-usage-btn">{getNumberWithComasEsacpe(bytesToMb(dataUsage))}</Link>

      </Box>
    );
  }
  return bytesToMb(row.totalUsage);
}
