export const sortFieldNames = {
  iccid: 'iccid',
  msisdn: 'msisdn',
  imsi: 'imsi',
  formFactor: 'form_factor',
  status: 'sim_status',
  countryName: 'country_name',
  carrierName: 'carrier_name',
  sessionStarttime: 'sessionStarttime',
  sessionEndtime: 'sessionEndtime',
  duration: 'duration',
  dataVolume: 'dataVolume',
  allocationReference: 'allocationReference',
  allocationDate: 'allocationDate',
  simStatus: 'simStatus',
  ratePlan: 'ratePlan',
  userName: 'userName',
  date: 'date',
  newValue: 'newValue',
  createdBy: 'createdBy',
  priorValue: 'priorValue',
  createdDate: 'createdDate',
};

export const sortSmsFieldNames = {

  dateSent: 'date_sent',
  sentFrom: 'sent_from',
  sentTo: 'sent_to',
  countryName: 'country_name',
  carrierName: 'carrier_name',
};

export const sortVoiceFieldNames = {
  callDate: 'call_date',
  callNumber: 'call_number',
  callMinutes: 'call_minutes',
  countryName: 'country_name',
  carrierName: 'carrier_name',
};

export const descending = 'desc';

export const defaultPage = 1;
export const defaultPgeSize = 10;

export const bulkMsisdnUpdateErrorMessage = 'Bulk actions require SIMs with a matching inactive status and configuration. Please revise your selection and try again.';
