.dialogueContent_sim {
  overflow-x: auto !important;
}

.upload-msisdn-modal {
  &__header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;

    h2 {
      font-weight: 700;
    }

    button {
      background: white;
      border: none;
      cursor: pointer;
    }
  }

  .dropZone_error {
    padding-bottom: 10px;
  }

  &__body {
    .select-arrow-icon {
      position: absolute;
      right: 12px;

      polyline {
        stroke: $dark-color-300;
      }
    }

    &_form-control {
      &.MuiFormControl-root {
        width: 100%;
        margin-bottom: 24px;

        label {
          left: -25px;
        }

        label.MuiInputLabel-shrink {
          left: 0;
          transform: translate(14px, -6px) scale(0.75) !important;
        }

        &.rate-plan-select {
          margin-bottom: 28px;
        }
      }
    }

    &_empty-rate-plans {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      margin-bottom: 5px;

      &_message.MuiTypography-body1 {
        font-size: 14px;
        font-weight: 400;
        color: #333;
      }

      &_create-button.MuiButton-root {
        margin-right: 15px;
      }

      &_reload-button.MuiButton-root {
        width: 40px;
      }

      &_create-button.MuiButton-root,
      &_reload-button.MuiButton-root {
        height: 40px;
        font-size: 14px;
        font-weight: 700;
        color: #6f37bf !important;
        background-color: #ebe3f6;

        &:hover {
          color: #6f37bf !important;
          background-color: #ebe3f6;
        }

        &:disabled {
          background-color: #ebe3f6;
        }
      }
    }

    &_subheading.MuiTypography-body2 {
      font-size: 13px;
      font-weight: 700;
      margin-bottom: 7px;
    }

    &_quantity-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      &_subheading {
        font-size: 14px;
        font-weight: 400;
        color: #696969;

        &_value {
          color: #333;
        }
      }

      .MuiTextField-root {
        width: 140px;
      }

      input.MuiInputBase-input {
        width: calc(100% - 5px);
      }
    }

    &_quantity-slider {
      margin: 0 15px 18px;

      span.MuiSlider-thumb {
        background-color: white;
        border: 2px solid;
      }

      span.MuiSlider-rail {
        border: 1px dashed #dfdfe7;
        color: transparent;
        height: 1px;
      }

      span.MuiSlider-track {
        background-color: transparent;
        height: 1px;
        border-style: dashed;
      }

      span.MuiSlider-mark {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        border: 1px solid #dfdfe7;
        background-color: transparent;
      }

      span.MuiSlider-valueLabelOpen {
        background: transparent;
        color: #333;
        transform: translateY(115%) scale(1);
      }

      .MuiSlider-root {
        color: #aa8ada;
      }
    }

    &_buttons {
      margin-top: 15px;

      button {
        height: 41px;
        font-size: 14px;
        font-weight: 700;
        margin-right: 17px;
      }
    }

    .MuiFormHelperText-root.Mui-error {
      margin-bottom: -17px;
    }

    &_sim-types {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
    }

    &_cancel-button.MuiButton-root {
      color: #6f37bf !important;
      background-color: #ebe3f6;

      &:hover {
        color: #6f37bf !important;
        background-color: #ebe3f6;
      }
    }
  }

  &__loader {
    display: flex;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: white;
    opacity: 0.6;
  }
}

/* stylelint-disable selector-id-pattern */
#menu-accountId {
  .MuiMenu-paper {
    max-height: 450px !important;
  }
}
