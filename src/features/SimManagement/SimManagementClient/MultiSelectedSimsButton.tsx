import { Box, IconButton, Tooltip } from '@mui/material';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import { PERMISSION, REPOSITORY, ROUTE_PERMISSION } from 'core/utilities/constants';
import { toastError, toastSuccess } from 'core/utilities/toastHelper';
import React from 'react';
import { AiOutlineCheckCircle, AiOutlineCloseCircle } from 'react-icons/ai';
import { GrUpdate } from 'react-icons/gr';
import { ICard } from '../SimManagement.models';
import { bulkSimStatusChange } from '../api.service';

interface IImsiActionProps {
  selectedRows: string[];
  sims: ICard[],
  enableMaltiSelectButton: boolean,
  setConfirmDialog: (param) => void,
  setSelectedRows: (param: string[]) => void,
  getApiData: () => void,
  checkMsisdnType: boolean,
  setMsisdnValidation: (param) => void,
  onBulkUpdateIMSI: () => void,
  onSelectedUpdateIMSI: () => void
}
const MultiSelectedSimsButton = ({
  sims,
  selectedRows,
  enableMaltiSelectButton,
  setConfirmDialog,
  setSelectedRows,
  getApiData,
  checkMsisdnType,
  setMsisdnValidation,
  onBulkUpdateIMSI,
  onSelectedUpdateIMSI,
}: IImsiActionProps) => {
  const firstSelectedSim = sims.find(
    (sim) => sim?.simId?.toString() === selectedRows[0]?.toString());

  let displayActiveIcon = false;
  let displayDeactiveIcon = false;
  let currentStatus = '';

  if (firstSelectedSim) {
    const { simStatus } = firstSelectedSim;
    displayActiveIcon = !!((simStatus === 'Deactivated' || simStatus === 'Ready for Activation'));
    displayDeactiveIcon = !!(simStatus === 'Active');
    currentStatus = simStatus;
  }

  const updateStatus = async () => {
    const user = localStorage.getItem('userDetails');
    const userProfile = user ? JSON.parse(user) : '';
    const result = sims.filter(
      (row) => selectedRows.some((itemId) => row?.simId?.toString() === itemId?.toString()));
    const imsiList = result.map((item) => item.imsi);
    // const firstSimProfile = result[0]?.simProfile?.ref;
    const data = {
      imsi: imsiList,
      createdBy: userProfile?.email,
    };
    try {
      const statusEndpoint = displayActiveIcon ? 'Active' : 'Deactivated';
      const response = await bulkSimStatusChange(statusEndpoint, data);
      toastSuccess(response.data.message);
    } catch (errorObj: any) {
      if (errorObj?.response?.status === 400) {
        toastError(errorObj?.response?.data?.detail);
        return;
      }
      toastError(errorObj?.response?.data?.detail);
    } finally {
      setSelectedRows([]);
      // Refresh data after status update
      // getApiData();
    }
  };
  const openDialogConfirm = async (event) => {
    event.stopPropagation();
    setConfirmDialog({
      isEnable: true, status: currentStatus, count: selectedRows.length, callback: updateStatus,
    });
  };
  return (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      gap: '5px',
    }}
    >
      {selectedRows.length > 0 && enableMaltiSelectButton && displayDeactiveIcon && (
        <CommonAuthwrapper
          permission={[ROUTE_PERMISSION.SIM_CEASE]}
          repository={[REPOSITORY.SIM_MANAGEMENT]}
        >
          <Tooltip title={displayActiveIcon ? 'Activate' : 'Deactivate'} arrow placement="top">
            <IconButton
              onClick={!checkMsisdnType ? openDialogConfirm : setMsisdnValidation}
              size="large"
              sx={{
                '.svg_icons:': {
                  height: '28px',
                  width: '28px',
                },
              }}
            >
              <AiOutlineCloseCircle
                data-testid="close-icon"
                className="icon-color"
              />
            </IconButton>
          </Tooltip>
        </CommonAuthwrapper>
      )}

      {selectedRows.length > 0 && enableMaltiSelectButton && displayActiveIcon && (
        <CommonAuthwrapper
          permission={[ROUTE_PERMISSION.SIM_PROVIDE]}
          repository={[REPOSITORY.SIM_MANAGEMENT]}
        >
          <Tooltip title={displayActiveIcon ? 'Activate' : 'Deactivate'} arrow placement="top">
            <IconButton
              onClick={!checkMsisdnType ? openDialogConfirm : setMsisdnValidation}
              size="large"
              sx={{
                '.svg_icons:': {
                  height: '28px',
                  width: '28px',
                },
              }}
            >
              <AiOutlineCheckCircle
                data-testid="check-icon"
                className="icon-color"
              />
            </IconButton>
          </Tooltip>
        </CommonAuthwrapper>
      )}
      <CommonAuthwrapper
        permission={[PERMISSION.UPDATE_SELECTED_MSISDN_RECORDS]}
        repository={[REPOSITORY.SIM_MANAGEMENT]}
      >
        <Box display={selectedRows?.length === 0 ? 'block' : 'none'}>
          <Tooltip title="Bulk Update IMSI(s)" arrow placement="top">
            <IconButton
              onClick={() => {
                onBulkUpdateIMSI();
              }}
              data-testid="check-icon"
            >
              <GrUpdate className="icon-color" />
            </IconButton>
          </Tooltip>
        </Box>
      </CommonAuthwrapper>
      <CommonAuthwrapper
        permission={[PERMISSION.UPDATE_SELECTED_MSISDN_RECORDS]}
        repository={[REPOSITORY.SIM_MANAGEMENT]}
      >
        <Box display={selectedRows?.length > 1 ? 'block' : 'none'}>
          <Tooltip title="Update Selected IMSI(s)" arrow placement="top">
            <IconButton
              onClick={() => {
                onSelectedUpdateIMSI();
              }}
              data-testid="check-icon"
            >
              <GrUpdate className="icon-color" />
            </IconButton>
          </Tooltip>
        </Box>
      </CommonAuthwrapper>
    </Box>

  );
};

export default MultiSelectedSimsButton;
