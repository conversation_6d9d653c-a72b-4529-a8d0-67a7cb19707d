 
import { Box, Button, Typography } from '@mui/material';
import React, { FC, useState } from 'react';
import Dialog from 'shared/Dialog/Dialog';
import { SIMPROFILE_OPTIONS } from 'core/utilities/constants';
import { toastError, toastSuccess } from 'core/utilities/toastHelper';
import Loader from 'shared/Loader';
import { updateCard } from '../api.service';

interface UpdateMsisdnDialogProps {
    open: boolean;
    onClose: () => void;
    rowData: any;
    selectedProfile: string;
    msisdnNumber: string;
    reCallData: any;
    paramState: any;
    msisdnType: string
}

const MsisdnConfirm: FC<UpdateMsisdnDialogProps> = ({
  open, onClose, rowData, selectedProfile, msisdnNumber, reCallData, paramState, msisdnType,
}) => {
  const [loading, setLoading] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const selectedProfileTitle = SIMPROFILE_OPTIONS
    .find((option) => option.value === selectedProfile)?.title || selectedProfile;

  const patchUpdateMsisdn = async (): Promise<void> => {
    try {
      const profileCheck = selectedProfile === '-2' ? rowData?.simProfile?.ref : selectedProfile;
      const msisdnNumberCheck = msisdnType === '-2' ? rowData?.msisdn : msisdnNumber?.length ? msisdnNumber : rowData?.msisdn;
      setLoading(true);
      await updateCard(rowData, profileCheck, msisdnNumberCheck);
      const {
        page, pageSize, field, sort, search,
      } = paramState;
      reCallData(page, pageSize, field, sort, search);
      onClose();
      setLoading(false);
      toastSuccess('IMSI updated successfully');
    } catch (error: any) {
      onClose();
      toastError(error?.response?.data?.detail || 'Something went wrong while Update IMSI');
      setLoading(false);
    }
  };

  const isSimProfileUpdated = selectedProfile !== '-2';
  const isMsisdnTypeUpdated = msisdnType !== '-2';
  const isMsisdnNumberUpdated = msisdnNumber?.length > 0 && msisdnNumber !== rowData?.msisdn;

  const updatedFields: any[] = [];
  if (isSimProfileUpdated) {
    updatedFields.push(
      <>
        SIM Profile will change from
        {' '}
        <b>{rowData?.simProfile?.name.replace(/_/g, ' ')}</b>
        {' '}
        to&nbsp;
        <b>{selectedProfile.replace(/_/g, ' ')}</b>
      </>,
    );
  }

  if (isMsisdnTypeUpdated) {
    updatedFields.push(
      <>
        MSISDN Type will change from
        {' '}
        <b>{rowData?.msisdnFactor?.ref}</b>
        {' '}
        to
        {' '}
        <b>{msisdnType}</b>
      </>,
    );
  }

  if (isMsisdnNumberUpdated) {
    updatedFields.push(
      <>
        MSISDN Number will change from
        {' '}
        <b>{rowData?.msisdn}</b>
        {' '}
        to
        {' '}
        <b>{msisdnNumber}</b>
      </>,
    );
  }

  return (
    <Dialog
      title="Are you sure?"
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiDialogTitle-root': {
          p: 8,
        },
      }}
      footerchildren={(
        <Box display="flex" alignItems="flex-start" gap="15px" justifyContent="space-between">
          <Button
            sx={{ p: '0px 25px', minWidth: '110px' }}
            variant="contained"
            color="primary"
            disabled={loading}
            onClick={() => patchUpdateMsisdn()}
          >
            Confirm
          </Button>
          <Button
            sx={{ p: '0px 25px', backgroundColor: '#ebe3f6', border: '0px' }}
            variant="outlined"
            onClick={onClose}
          >
            Cancel
          </Button>
        </Box>
            )}
    >
      <Box width={380} pb={5} display="flex" alignItems="center">
        {
                    loading ? <Loader size={50} /> : (
                      <Typography
                        variant="body1"
                        sx={{
                          ul: {
                            paddingLeft: '20px',
                          },
                          li: {
                            listStyleType: 'disc',
                            marginBottom: '4px',
                          },
                        }}
                      >
                        <ul>
                          {updatedFields.map((msg, index) => (
                            <li key={index}>{msg}</li>
                          ))}
                        </ul>
                      </Typography>
                    )
                }
      </Box>

    </Dialog>
  );
};

export default MsisdnConfirm;
