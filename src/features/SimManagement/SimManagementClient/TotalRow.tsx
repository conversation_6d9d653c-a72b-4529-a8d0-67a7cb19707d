import React, { FC } from 'react';

import StatusText from 'shared/Status/StatusText';
import { Stack } from '@mui/material';
import TableRows from 'shared/Container/TableRows';
import TableCells from 'shared/Container/TableCells';

export interface IAllocationTotalRow {
    title:string,
    totalcycletdata:number,
    ActivesCount:number,
    DeactiveCount:number
  }
  interface IRowProps {
    row: IAllocationTotalRow;
  }
const TotalRow: FC<IRowProps> = ({ row }) => {
  const {
    title,
    totalcycletdata,
    ActivesCount,
    DeactiveCount,
  } = row;

  return (
    <TableRows>
      <TableCells />
      <TableCells bold colSpan={2}>{title}</TableCells>
      <TableCells />
      <TableCells />
      <TableCells />
      <TableCells />
      <TableCells bold>{totalcycletdata}</TableCells>
      <TableCells bold align="center" width={200}>
        <Stack direction="row" spacing={3}>
          <StatusText status="active" message={ActivesCount} />
          <StatusText status="error" message={DeactiveCount} />
        </Stack>
      </TableCells>
      <TableCells />
    </TableRows>
  );
};

export default TotalRow;
