import React from 'react';
import { useNavigate } from 'react-router-dom';
import { AiOutlineLeft } from 'react-icons/ai';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

const SimManagementClientTableActionsLeft = () => {
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate('/');
  };

  return (
    <button
      type="button"
      onClick={handleGoBack}
      className="sim-management-page__back"
    >
      <AiOutlineLeft fontSize={24} color={styles.darkColor300} />
    </button>
  );
};

export default SimManagementClientTableActionsLeft;
