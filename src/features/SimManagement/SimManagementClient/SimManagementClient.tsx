import {
  Box, IconButton, Tooltip,
} from '@mui/material';
import MuiTable from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';
import { MuiTableProvider } from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable/MuiTableContext';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import { useAppContext } from 'AppContextProvider';
import Pie<PERSON><PERSON> from 'assets/images/PieChart';
import SimIconHeader from 'assets/images/SimIconHeader';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import {
  REPOSITORY, RESPONSE, ROUTE_PERMISSION,
} from 'core/utilities/constants';
import exportCSVFile from 'core/utilities/exportCSVFile';
import { getSearchSortModel, onExportCSVFileHandle } from 'core/utilities/exportCSVMAP';
import { toastError, toastInfo, toastSuccess } from 'core/utilities/toastHelper';
import { getNumberWithCommas } from 'core/utilities/toMoneyFormat';
import { TGetCurrentThemeColors } from 'features/models';
import {
  getAccountByOrganizationId,
  getAccountDetails,
  getCards, getCardsExport, getRatePlans, setSimActive, setSimDeActive,
} from 'features/SimManagement/api.service';
import { ICard, IRatePlanFullClient } from 'features/SimManagement/SimManagement.models';
import { bulkMsisdnUpdateErrorMessage, descending, sortFieldNames } from 'features/SimManagement/SimManagementClient/constants';
import SimManagementClientTableActionsLeft from 'features/SimManagement/SimManagementClient/SimManagementClientTableActionsLeft';
import { IMSI_DETAILS_TABS_TABS } from 'features/SimManagementDetail/SimManagement.models';
import useSimManagementClientColumns from 'hooks/useSimManagementClientColumns';
import useSimManagementClientSearchParams from 'hooks/useSimManagementClientSearchParams';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import GenericDialog from 'shared/Dialog/GenericDialog';
import { IUser } from 'user.model';
import IMSIAllocationsActionExport from '../IMSIAllocations/IMSIAllocationsActionExport';
import IMSIAllocationsContextProvider from '../IMSIAllocations/IMSIAllocationsContext';
import BulkMsisdnUpdate from './BulkMsisdnUpdate';
import DialogConfirm from './DialogConfirm';
import MarketShareDialog from './MarketShare/MarketShareDialog';
import MultiSelectedSimsButton from './MultiSelectedSimsButton';
import SelectMsisdnDialog from './SelectMsisdnDialog';
import './SimManagementClient.scss';
import UpdateMsisdnDialog from './UpdateMsisdnDialog';
import useMuiTableSearchParams from 'hooks/useMuiTableSearchParams';
import SimStatusSocket from './Socket/SimStatusSocket';

interface SimManagementClientProps {
  user: IUser | undefined,
}

const SimManagementClient = ({ user }: SimManagementClientProps) => {
  const { primaryColor, getBrandColors } = useAppContext();
  const [isEIDVisible, setIsEIDVisible] = useState(false);
  const {
    setParamsToUrl,
    getParamsFromUrl,
    defaultPagination,
    defaultSort,
    initialSearchValue,
  } = useSimManagementClientSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [confirmDialog, setConfirmDialog] = useState({
    isEnable: false, imsi: '', status: '', count: '', callback: null, rowData: null,
  });
  const onSimManagmentClientActionClick = (event, row) => {
    event.stopPropagation();
    setConfirmDialog({
      isEnable: true, imsi: row.imsi, status: row.simStatus, count: '', callback: null, rowData: row,
    });
  };
  interface searchparam {
    page: string | number, pageSize: string | number, field: string | null, sort: string | null,
    search: string | null,
  }
  const [paramState, setParamState] = useState<searchparam>({
    page: 1, pageSize: 10, field: '', sort: '', search: '',
  });
  const { columns } = useSimManagementClientColumns({ onSimManagmentClientActionClick, isEIDVisible });
  const [cards, setCards] = useState<ICard[]>();
  const [ratePlans, setRatePlans] = useState<IRatePlanFullClient[]>();
  const [activateProfile, setActivateProfile] = useState('');
  const [accountDetails, setAccountDetails] = useState<any>({});
  const [cardsTotalCount, setCardsTotalCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingExport, setIsLoadingExport] = useState(false);
  const [accountUsage, setAccountUsage] = useState({});
  const [selectedRows, setSelectedRows] = React.useState<string[]>([]);
  const [selectedIMSIs, setSelectedIMSIs] = React.useState<string[]>([]);
  const [enableBulkImsiUpdate, setEnableBulkImsiUpdate] = useState(false);
  const [isEnableMaltiSelectButton, setIsEnableMaltiSelectButton] = useState(false);
  const [noData, setNoData] = useState({});
  const [toggle, setToggle] = useState(false);
  const [dynamicTitle, setDynamicTitle] = useState(false);
  const [bulkMsisdnModal, setBulkMsisdnModal] = useState(false);
  const [updateMsisdnModal, setUpdateMsisdnModal] = useState({
    isEnable: false,
    status: '',
    callback: null,
    row: null,
  });
  const [, setIsActivityGoing] = useState(false);
  const [selectedMsisdnModal, setSelectedMsisdnModal] = useState(false);
  const [msisdnValidation, setMsisdnValidation] = useState(false);
  const [checkMsisdnType, setCheckMsisdnType] = useState(false);
  const [checkSimProfile, setCheckSimProfile] = useState(false);
  const [accountOrganization, setAccountOrganization] = useState(null);

  const { generateParamsForUrl } = useMuiTableSearchParams();

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const loadRatePlans = async () => {
    const { data } = await getRatePlans();
    setRatePlans(data);
    return data;
  };

  const loadAccountId = async () => {
    try {

      const accountDetails = JSON.parse(localStorage.getItem('userDetails') || 'null');
      if (accountDetails) {
        setAccountOrganization(accountDetails?.accountId)
      } else {
        const account = await getAccountByOrganizationId(user?.organization?.id);
        setAccountOrganization(account.data.id)
      }
    } catch (err) {
      toastError('Account not found')
      navigate('/not-found')
    }
  }

  useEffect(() => {
    if (accountOrganization) {
      getApiData();
    }
  }, [accountOrganization])

  const loadCards = async (page, pageSize, field, sort, search, loadedRatePlans) => {
    const newSearchParams = generateParamsForUrl(page, pageSize, field, sort, search);
    const newUrl = `${location?.pathname
      }?tab=sim-management&${newSearchParams?.toString()}`;
    navigate(newUrl, { replace: true });

    let ordering = sortFieldNames[field];

    if (sort === descending) {
      ordering = `-${ordering}`;
    }

    const { data: cardsData } = await getCards(accountOrganization, page, pageSize, ordering, search);

    const { results: resultsCards, summary, totalCount } = cardsData;
    const cardsWithRatePlans = resultsCards.map((card) => {
      const ratePlan = loadedRatePlans?.find(({ id }) => id === card?.ratePlanId);

      return {
        ...card,
        ratePlanName: ratePlan?.name,
        id: card.iccid,
      };
    });
    const eidExists = cardsWithRatePlans.some((row: any) => !!row.eid);
    setIsEIDVisible(eidExists);
    setCards(cardsWithRatePlans);
    setAccountUsage({
      id: 'TOTAL',
      imsi: 'TOTAL',
      simId: 'TOTAL',
      totalUsage: summary.totalUsage,
      simStatus: {
        totalActiveSims: getNumberWithCommas(summary.totalActiveSims),
        totalDeactivatedSims: getNumberWithCommas(summary.totalDeactivatedSims),
        totalPendingSims: getNumberWithCommas(summary.totalPendingSims),
        totalReadyActivationSims: getNumberWithCommas(summary.totalReadyActivationSims),
        totalSims: getNumberWithCommas(
          summary.totalDeactivatedSims
          + summary.totalPendingSims
          + summary.totalReadyActivationSims),

      },
    });
    setCardsTotalCount(totalCount);
  };

  const loadData = async (page, pageSize, field, sort, search) => {
    setParamsToUrl(page, pageSize, field, sort, search);
    try {
      setIsLoading(true);

      const loadedRatePlans = ratePlans;

      if (!ratePlans?.length) {
        // loadedRatePlans = await loadRatePlans();
      }

      await loadCards(page, pageSize, field, sort, search, loadedRatePlans);
    } catch (errorObj: any) {
      let message = '';
      if (errorObj?.response?.status === 404) {
        message = RESPONSE.GET_ALLOCATION_SEARCH_EMPTY;
      } else if (errorObj?.response?.status === 500) {
        message = RESPONSE.HTTP_STATUS_500;
      } else {
        message = errorObj?.response?.data?.detail;
      }
      setNoData({
        icon: <SimIconHeader />,
        title: 'IMSIs are not found for your Account.',
        description: message,
      });
      setCardsTotalCount(0);
      setCards([]);
    } finally {
      setIsLoading(false);
    }
  };

  const onChange = ({ page, pageSize }, { field, sort }, search) => {
    setParamState({
      page, pageSize, field, sort, search,
    });
    loadData(page, pageSize, field, sort, search);
  };
  const getApiData = () => {
    const {
      page, pageSize, field, sort, search,
    } = getParamsFromUrl();

    loadData(page, pageSize, field, sort, search);
  };

  const makeAutoReferesh = (page, pageSize, field, sort, search) => {
    loadData(page, pageSize, field, sort, search);
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const getuserData = async () => {
    try {
      const userLocal = JSON.parse(localStorage.getItem('userDetails') || 'null');
      const response = await getAccountDetails(
        user?.organization?.id ?? userLocal?.organization?.id,
      );
      setAccountDetails(response.data);
    } catch (error) {
      console.log('error: ', error);
    }
  };

  useEffect(() => {
    loadAccountId();
    // getuserData();
  }, []);
  const updateStatus = async (setSimStatusFunc, data, sucessState, errorState) => {
    try {
      await setSimStatusFunc(data);
      toastSuccess(sucessState);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (exception) {
      toastError(errorState);
    } finally {
      // Refresh data after status update
      // getApiData();
    }
  };
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const toggleSimAction = async (imsi, status, rowData) => {
    const userDetail = localStorage.getItem('userDetails');
    const userProfile = userDetail ? JSON.parse(userDetail) : '';
    const displayActiveIcon = !!((status === 'Deactivated' || status === 'Ready for Activation'));
    const displayDeactiveIcon = !!(status === 'Active');
    setConfirmDialog({
      isEnable: false, imsi: '', status: '', count: '', callback: null, rowData: null,
    });
    const data = {
      imsi,
      createdBy: userProfile?.email,
    };
    if (displayActiveIcon) {
      updateStatus(
        setSimActive,
        data,
        RESPONSE.SET_SIM_ACTIVATE, RESPONSE.SET_SIM_ACTIVATE_ERROR,
      );
    } else if (displayDeactiveIcon) {
      updateStatus(
        setSimDeActive,
        data,
        RESPONSE.SET_SIM_DEACTIVATE,
        RESPONSE.SET_SIM_DEACTIVATE_ERROR);
    }
  };
  const newResult = useMemo(() => {
    if (cards && cards.length > 0) {
      return [accountUsage, ...cards];
    }
    return [];
  }, [accountUsage, cards]);

  const isRowSelectable = (params) => {
    const status = true;
    if (params.row.simStatus === 'Pending' || params.id === 'TOTAL') {
      return false;
    }
    return status;
  };
  const setSelectedRowsEmpty = () => {
    setSelectedRows([]);
  };
  // 30 seond auto refresh api call
  // useEffect(() => {
  //   let intervalId;
  //   if (cards) {
  //     intervalId = setInterval(async () => {
  //       const {
  //         page, pageSize, field, sort, search,
  //       } = paramState;
  //       makeAutoReferesh(page, pageSize, field, sort, search);
  //     }, 30000);
  //   }
  //   return () => {
  //     clearInterval(intervalId);
  //   };
  // }, [JSON.stringify(cards)]);
  const getRowClassName = ({ id }) => {
    if (id === 'TOTAL') return 'MuiDataGrid-row-bold';
    return '';
  };
  const getSelectedSimStatus = (simList, selectedIds) => {
    const result = simList.filter(
      (row) => selectedIds.some((itemId) => row?.simId?.toString() === itemId?.toString(),
      ));
    const isDeactiveEnable = result.filter((row) => (row.simStatus === 'Deactivated' || row.simStatus === 'Ready for Activation')).length > 0;
    const isActiveEnable = result.filter((row) => row.simStatus === 'Active').length > 0;
    const msisdnTypes = result.map((row) => row.msisdnFactor?.ref);
    const isDifferentMsisdnType = new Set(msisdnTypes).size > 1;
    const simType = result.map((row) => row.simProfile?.ref);
    const isDifferentSimProfile = new Set(simType).size > 1;
    const proceedOperation = !(isActiveEnable && isDeactiveEnable);
    setEnableBulkImsiUpdate(isActiveEnable);
    setIsEnableMaltiSelectButton(proceedOperation);
    setCheckMsisdnType(isDifferentMsisdnType);
    setCheckSimProfile(isDifferentSimProfile);
    return proceedOperation;
  };

  const getSelectedSimIMSI = (simList, selectedIds) => {
    const selectedIMSIsList = simList
      .filter((row) => selectedIds.includes(row?.simId?.toString()))
      .map((row) => row.imsi);
    return selectedIMSIsList;
  };

  const selectmaltipleRows = (ids) => {
    const selectedIDs = [...ids];
    const isDifferent = getSelectedSimStatus(newResult, selectedIDs);
    setSelectedRows(selectedIDs);
    const selectedIMSIList = getSelectedSimIMSI(newResult, selectedIDs);
    setSelectedIMSIs(selectedIMSIList);
    if (!isDifferent) {
      toastInfo(RESPONSE.DIFFERENT_STATUS_SIM_SELECTION_ERROR);
    }
  };

  const onExportCSVFile = async () => {
    const { ordering, search, formattedDate } = getSearchSortModel(
      getParamsFromUrl, setIsLoadingExport,
    );
    const fileName = `SIMManagement_${formattedDate}.csv`;
    const { data: csvData } = await getCardsExport(accountOrganization, ordering, search ?? '');
    onExportCSVFileHandle(csvData, fileName, exportCSVFile, setIsLoadingExport);
  };

  const openDialogConfirmMsisdn = (operationOfMsisdn) => {
    setUpdateMsisdnModal(operationOfMsisdn);
  };

  const setCardsRef = useRef(cards);

  useEffect(() => {
    setCardsRef.current = cards;
  }, [cards]);

  return (
    <Box
      data-testid="sim-management-client"
      className="sim-management-client"
      sx={{
        background: styles.lightColor50,
        '& .MuiDataGrid-row-bold': {
          '& .MuiCheckbox-root': {
            display: 'none !important',
          },
          '& .MuiDataGrid-main': {
            overflow: 'hidden !important',
          },
          ':hover': {
            cursor: 'default !important',
          },
        },
        '& .MuiDataGrid-columnHeaders': {
          backgroundColor: '#F5F5F9 !important',
        },
        '& .MuiDataGrid-virtualScrollerContent': {
          minHeight: '160px !important',
        },
        '& .MuiDataGrid-columnHeaderTitleContainerContent': {
          '& .MuiCheckbox-root': {
            display: 'none !important',
          },
        },
        '& .interactions__actions': {
          justifyContent: 'flex-start',
          '& .interactions__actions-selections': {
            display: 'none',
          },
        },
      }}
    >
      {newResult.length > 0 && (
        <SimStatusSocket simUsageRef={setCardsRef} setSimUsage={setCards} />
      )}
      <IMSIAllocationsContextProvider
        value={{
          openDialogConfirmMsisdn,
          setIsActivityGoing,
        }}
      >
        <div className="sim-details__main">
          <MuiTableProvider
            defaultPagination={defaultPagination}
            onChange={onChange}
            onChangePagination={onChange}
            onChangeSearch={onChange}
            initialSearchValue={initialSearchValue}
            onChangeSort={onChange}
            defaultSort={defaultSort}
          >
            <MuiTable
              sx={{
                '& .MuiDataGrid-row:hover': {
                  cursor: 'pointer',
                  '& .MuiButtonBase-root': {
                    visibility: 'visible ',

                  },
                },
              }}
              Actions={() => (
                <Box display="flex" justifyContent="space-between" sx={{ width: '100%' }}>
                  <Box display="flex" columnGap={2} marginLeft={3}>
                    <CommonAuthwrapper
                      permission={[ROUTE_PERMISSION.EXPORT_SIM_MANAGEMENT]}
                      repository={[REPOSITORY.MARKET_SHARE_REPORT]}
                    >
                      <IMSIAllocationsActionExport
                        rowCount={cardsTotalCount}
                        onExportCSVFile={onExportCSVFile}
                        isLoading={isLoadingExport}
                      />
                    </CommonAuthwrapper>
                    {/* {(selectedRows.length > 0) && ( */}
                    <MultiSelectedSimsButton
                      selectedRows={selectedRows}
                      setSelectedRows={setSelectedRows}
                      sims={newResult as ICard[]}
                      getApiData={getApiData}
                      enableMaltiSelectButton={isEnableMaltiSelectButton}
                      setConfirmDialog={setConfirmDialog}
                      checkMsisdnType={checkMsisdnType}
                      setMsisdnValidation={() => {
                        setMsisdnValidation(true);
                        setDynamicTitle(true);
                      }}
                      onBulkUpdateIMSI={() => setBulkMsisdnModal(!bulkMsisdnModal)}
                      onSelectedUpdateIMSI={() => {
                        if (!enableBulkImsiUpdate && !checkMsisdnType && !checkSimProfile) {
                          setSelectedMsisdnModal(true);
                        } else {
                          setMsisdnValidation(true);
                        }
                      }}
                    />
                    {/* )} */}
                  </Box>
                  <Box
                    display="flex"
                    gap={2}
                    alignItems="center"
                  >
                    <CommonAuthwrapper
                      permission={[ROUTE_PERMISSION.MARKET_SHARE_FOR_ACCOUNT]}
                      repository={[REPOSITORY.MARKET_SHARE_REPORT]}
                    >
                      <Tooltip title="Market Share Report" arrow placement="top">
                        <IconButton
                          sx={{
                            '&:hover path': {
                              stroke: 'unset',
                            },

                          }}
                          data-testid="marketshare-buttons"
                          onClick={() => setToggle(!toggle)}
                        >
                          <PieChart />
                        </IconButton>
                      </Tooltip>
                    </CommonAuthwrapper>
                  </Box>
                </Box>
              )}
              ActionsLeft={SimManagementClientTableActionsLeft}
              rows={newResult}
              columns={columns}
              loading={isLoading}
              rowCount={cardsTotalCount}
              primaryColor={primaryColor as string}
              getCurrentThemeColors={getBrandColors as TGetCurrentThemeColors}
              showFirstLastPageButtons
              isVisibleSearchInput
              checkboxSelection
              isRowSelectable={isRowSelectable}
              selectionModel={selectedRows}
              onSelectionModelChange={setSelectedRowsEmpty}
              onRowSelectionModelChange={selectmaltipleRows}
              getRowClassName={getRowClassName}
              getRowId={(row) => `${row?.simId}`}
              hideFooter={!cardsTotalCount}
              rowSelectionModel={selectedRows}
              onRowClick={(e) => {
                if ((e.id)?.toString()?.toLocaleLowerCase() !== 'total') {
                  return navigate(`${e.row.imsi}/sim-detail#${IMSI_DETAILS_TABS_TABS.CONNECTION_HISTORY}`);
                }
                return null;
              }}
              noDataConfig={noData}
              fixedColumns={{
                0: 50,
                1: 150,
              }}
              fixedColumnsRight={{
                0: 165,
              }}
            />
          </MuiTableProvider>
          <DialogConfirm
            status={confirmDialog.status}
            activateProfile={activateProfile}
            setActivateProfile={setActivateProfile}
            accountDetails={accountDetails}
            onSuccesh={() => {
              const toggleChangeStatusCallback: any = confirmDialog?.callback || null;

              if (confirmDialog?.count && toggleChangeStatusCallback != null) {
                toggleChangeStatusCallback();
                setConfirmDialog({
                  ...confirmDialog,
                  isEnable: false,
                  count: '',
                  callback: null,
                });
              } else {
                toggleSimAction(confirmDialog.imsi, confirmDialog.status, confirmDialog?.rowData);
              }
            }}
            open={confirmDialog.isEnable}
            simsCount={confirmDialog?.count}
            onClose={() => {
              setConfirmDialog({
                ...confirmDialog,
                isEnable: false,
                count: '',
                callback: null,
              });
            }}
          />
        </div>
      </IMSIAllocationsContextProvider>
      {
        toggle && (
          <MarketShareDialog
            open={toggle}
            onClose={() => setToggle(false)}
            marketShareFor="account"
          />
        )
      }

      {bulkMsisdnModal && (
        <BulkMsisdnUpdate
          open={bulkMsisdnModal}
          accountDetails={accountDetails}
          onClose={() => setBulkMsisdnModal(false)}
          paramState={paramState}
          reCallData={makeAutoReferesh}
        />
      )}

      <SelectMsisdnDialog
        open={selectedMsisdnModal}
        onClose={() => setSelectedMsisdnModal(false)}
        selectedIMSIs={selectedIMSIs}
        selectedRows={selectedRows}
        sims={newResult}
        paramState={paramState}
        reCallData={makeAutoReferesh}
        setSelectedRows={setSelectedRows}
      />

      <GenericDialog
        open={msisdnValidation}
        title={dynamicTitle ? 'Bulk Update Status ' : 'Bulk Update IMSI(s)'}
        onClose={() => { setMsisdnValidation(false); setDynamicTitle(false); }}
        onSuccess={() => { setMsisdnValidation(false); setDynamicTitle(false); }}
        firstButtonText="OK"
        displaySecondButton={false}
      >
        {bulkMsisdnUpdateErrorMessage}
      </GenericDialog>

      <UpdateMsisdnDialog
        open={updateMsisdnModal?.isEnable}
        onClose={() => {
          setIsActivityGoing(false);
          openDialogConfirmMsisdn({
            ...updateMsisdnModal,
            isEnable: false,
            callback: null,
            row: null,
          });
        }}
        rowData={updateMsisdnModal?.row}
        paramState={paramState}
        reCallData={makeAutoReferesh}
      />
      {/* )} */}
    </Box>
  );
};

export default SimManagementClient;
