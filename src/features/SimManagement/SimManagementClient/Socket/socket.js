import { io } from "socket.io-client";

let socketInstance = null;

const SOCKET_PATH = "/socket/ws";

const getSocketUrl = () => {
  const currentHost = window.location.hostname;

  switch (true) {
    case currentHost.includes('app.dev'):
      return process.env.REACT_APP_DEV_DOMAIN;
    case currentHost.includes('app.test'):
      return process.env.REACT_APP_TEST_DOMAIN;
    case currentHost.includes('app.stage'):
      return process.env.REACT_APP_STAGE_DOMAIN;
    case currentHost.includes('app.iotportal'):
      return process.env.REACT_APP_PROD_DOMAIN;
    default:
      return '/';
  }
};

export const initializeSocket = () => {
  if (!socketInstance) {
    const url = getSocketUrl();

    socketInstance = io(url, {
      path: SOCKET_PATH,
      transports: ["websocket"],
      secure: true, // for local make it false
      reconnection: true,
      reconnectionAttempts: 10,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      timeout: 60000,
      autoConnect: true,
    });

    socketInstance.on("connect", () => { });
    socketInstance.on("disconnect", () => { });
    socketInstance.on("connect_error", () => { });
    socketInstance.on("reconnect_attempt", () => { });
  }

  return socketInstance;
};

export const getSocket = () => socketInstance;