import React, { useEffect, useCallback } from 'react';
import { toastInfo } from 'core/utilities/toastHelper';
import { getSocket } from './socket';
import { ISIMUsage } from 'features/SimManagementDetail/SimManagement.models';
import { ICard } from 'features/SimManagement/SimManagement.models';

interface SimStatusSocketProps {
  simUsageRef: any;
  setSimUsage: any;
}

interface SimStatusData {
  imsi: string;
  simStatus: string;
  [key: string]: any;
}

function SimStatusSocket({ simUsageRef, setSimUsage }: SimStatusSocketProps) {
  const socket = getSocket();

  const updateSimStatus = useCallback((updates: SimStatusData[]) => {
    setSimUsage(prev => {
      const updated = prev.map(sim => {
        if (sim.imsi === 'Total') return sim;
        const match = updates.find(update => update.imsi === sim.imsi);
        if (match && sim.simStatus !== match.simStatus) {
          return { ...sim, simStatus: match.simStatus };
        }else{
          return {...sim}
        }
      });
      // Toast Notification on socket status update
      // const updatedSims = updates
      //   .filter(update => prev.some(sim => sim.imsi === update.imsi))
      //   .map(update => update.imsi);
      // if (updatedSims.length) {
      //   toastInfo(
      //     updatedSims.length === 1
      //       ? `SIM ${updatedSims[0]} updated`
      //       : `${updatedSims.length} SIMs updated`
      //   );
      // }

      return updated;
    });
  }, [setSimUsage]);


  useEffect(() => {

    const onSimStatusUpdate = (data: { imsi: string[]; simStatus: string }) => {

      if (!Array.isArray(data?.imsi) || typeof data?.simStatus !== "string") {
        console.warn("[Socket] Invalid data format received:", data);
        return;
      }

      const updates: SimStatusData[] = data?.imsi
        .filter(imsi => typeof imsi === "string")
        .map(imsi => ({ imsi, simStatus: data?.simStatus }));

      if (updates?.length === 0) {
        console.warn("[Socket] No valid IMSIs to update:", data?.imsi);
        return;
      }

      const currentIMSIs = new Set(
        simUsageRef.current.map(sim => sim.imsi).filter(Boolean)
      );
      const validUpdates = updates.filter(u => currentIMSIs.has(u.imsi));

      if (validUpdates?.length) {
        updateSimStatus(validUpdates);
      } else {
        console.warn("[Socket] No matching IMSIs found:", updates?.map(u => u.imsi));
      }
    };


    socket.on('sim_details_update', onSimStatusUpdate);
    if (!socket.connected) {
      socket.on('connect', () => {});
    }

    return () => {
      socket.off('sim_details_update', onSimStatusUpdate);
    };
  }, [updateSimStatus, simUsageRef]);

  return null;
}

export default SimStatusSocket;
