import { Stack, Tooltip } from '@mui/material';
import React from 'react';
import StatusText from 'shared/Status/StatusText';
import RenderStatusList from './RenderStatusList';

const renderStatus = (
  row,
) => {
  const status = row?.simStatus;

  if (row.id === 'TOTAL') {
    const { totalActiveSims, totalSims } = row.simStatus;
    return (
      <Stack direction="row" spacing={1}>
        <Tooltip
          title="Active"
          arrow
          placement="top"
        >
          <span>
            <StatusText status="totalactive" message={`${totalActiveSims}`} />
          </span>
        </Tooltip>
        <Tooltip
          title={(
            <RenderStatusList
              totalDeactivatedSims={row.simStatus.totalDeactivatedSims}
              totalPendingSims={row.simStatus.totalPendingSims}
              totalReadyActivationSims={row.simStatus.totalReadyActivationSims}
            />
          )}
          arrow
          placement="top"
        >
          <span>
            <StatusText
              status="totalothers"
              message={`${totalSims} `}
            />
          </span>
        </Tooltip>
      </Stack>
    );
  }

  return (
    <StatusText status={status} message={status} />
  );
};

export default renderStatus;
