import React from 'react';
import { render } from '@testing-library/react';

import { simCardsUserMockData } from 'features/SimManagement/IMSIAllocations/IMSIAllocationsMockData';
import { cloneDeep } from 'lodash';
import SimManagementClient from './SimManagementClient';

const mockNavigate = jest.fn();
const mockLocation = {
  search: '',
  pathname: '/sim-management/client',
};
const mockSetSearchParams = jest.fn();
const mockSearchParams = new URLSearchParams();

jest.mock('react-router-dom', () => ({
  __esModule: true,
  useNavigate: () => mockNavigate,
  useLocation: () => mockLocation,
  useSearchParams: () => [mockSearchParams, mockSetSearchParams],
}));


jest.mock('@nv2/nv2-pkg-js-shared-components/lib/MuiTable', () => jest.fn(() => null));

const mockCardsData = cloneDeep(simCardsUserMockData);

jest.mock('features/SimManagement/api.service', () => ({
  getCards: jest.fn(() => Promise.resolve({ data: mockCardsData })),
  getRatePlans: jest.fn(() => Promise.resolve({ data: [] })),
}));
jest.mock('hooks/useSimManagementClientSearchParams', () => (
  jest.fn(() => ({
    setParamsToUrl: jest.fn(),
    getParamsFromUrl: jest.fn(() => ({
      page: 1, pageSize: 10, sort: '', field: '', search: '',
    })),
    defaultPagination: {},
    defaultSort: {},
    initialSearchValue: null,
  }))
));

describe('SimManagementClient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component without crashing', async () => {
    const { getByTestId } = render(<SimManagementClient />);
    expect(getByTestId('sim-management-client')).toBeInTheDocument();
  });
});
