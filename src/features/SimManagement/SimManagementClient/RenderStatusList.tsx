import React from 'react';
import StatusText from 'shared/Status/StatusText';

export const RenderStatusList = (
  row,
) => {
  const { totalDeactivatedSims, totalPendingSims, totalReadyActivationSims } = row;

  return (
    <>
      <StatusText status="deactivated" message={`${totalDeactivatedSims} Deactivated`} />
      <StatusText status="pending" message={`${totalPendingSims} Pending`} />
      <StatusText status="ready for activation" message={`${totalReadyActivationSims} Ready for activation`} />
    </>
  );
};

export default RenderStatusList;
