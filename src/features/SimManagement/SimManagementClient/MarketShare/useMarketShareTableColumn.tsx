import React, { Box, useMediaQuery } from '@mui/material';

const useMarketShareTableColumn = () => {
  const isSmallScreen = useMediaQuery('(max-width:1600px)');

  return [
    {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      renderHeader: (params) => (
        <Box
          fontFamily="BT Curve, sans-serif"
          fontSize="14px"
          fontWeight={700}
          lineHeight="20px"
          color="#1C1C28"
        >
          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#
        </Box>
      ),
      field: 'id',
      sortable: false,
      align: 'center',
      headerClassName: 'report-id',
      width: isSmallScreen ? 70 : 85,
    }, {
      headerName: 'Carrier',
      field: 'carrier',
      width: isSmallScreen ? 100 : 110,
      sortable: false,

    }, {
      headerName: 'Data Usage (MB)',
      field: 'usage',
      width: isSmallScreen ? 100 : 140,
      align: 'right',
      headerClassName: 'data-usage-mb',
      cellClassName: 'data-usage-cell',
      sortable: false,
    }, {
      headerName: 'Percentage',
      field: 'percentage',
      sortable: false,
      align: 'right',
      width: isSmallScreen ? 100 : 100,
    },
  ];
};

export default useMarketShareTableColumn;
