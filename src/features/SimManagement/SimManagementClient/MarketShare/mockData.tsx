import { BYTES_TO_MB_DIVIDER, CHARTCOLORS } from 'core/utilities/constants';
import { getNumberWithComasEsacpe } from 'core/utilities/toMoneyFormat';

export const mockMarketShareData = {

  totalUsage: 5652,
  results: [
    {
      carrier: 'GBRMT',
      usage: 300000000,
    },
    {
      carrier: 'GBRME',
      usage: 200000000,
    },
    {
      carrier: 'GBRMO',
      usage: 400000000,
    },
    {
      carrier: 'GBRMT',
      usage: 300000000,
    },
    {
      carrier: 'GBRME',
      usage: 200000000,
    },
    {
      carrier: 'GBRMO',
      usage: 400000000,
    },

  ],

};

export const parseMarketData = (marketShareData, totalUsage) => {
  if (!marketShareData) return { summary: [], chartData: [] };
  let totalPercent = 0;
  let sumTotalUsage = 0;
  const marketShareDataList = marketShareData.map((marketData, index) => {
    const mb = marketData.usage / BYTES_TO_MB_DIVIDER;
    const totalUsageMb = (totalUsage / BYTES_TO_MB_DIVIDER);
    const percenData = (mb / totalUsageMb) * 100;
    totalPercent += percenData;
    sumTotalUsage += Number(mb.toFixed(4));
    return {
      ...marketData,
      usage: getNumberWithComasEsacpe(mb, 4),
      id: index + 1,
      color: CHARTCOLORS[index],
      percentage: `${percenData.toFixed(2)}%`,
      name: marketData.carrier,
      value: mb,
    };
  });

  marketShareDataList.push({
    carrier: '',
    usage: getNumberWithComasEsacpe(sumTotalUsage, 4),
    id: 'Total',
    percentage: `${totalPercent.toFixed(2)}%`,
    name: '',
    color: '',
    value: 0,
  });
  const newData = marketShareDataList.slice(0, marketShareDataList.length - 1);
  const chartDataParse = newData.map((item) => ({
    name: item.name, value: item.value, pchartValue: item.percentage, color: item.color,
  }),
  );
  return { summary: marketShareDataList, chartData: chartDataParse };
};

export const chartParseData = (data) => {
  if (!data) return [];
  const newData = data.slice(0, data.length - 1);
  return newData.map((item) => ({
    name: item.name, value: item.value, pchartValue: item.percentage, color: item.color,
  }),
  );
};
