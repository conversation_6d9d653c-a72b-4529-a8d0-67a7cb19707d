import React from 'react';
import {
  render, screen, act, waitFor,
} from '@testing-library/react';
import testRender from 'core/utilities/testUtils';
import { MuiTableProvider } from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable/MuiTableContext';
import MuiTable from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';
import MarketShareDialog from './MarketShareDialog';

const mockMarketShareData = {

  results: [
    {
      carrier: 'GBRMT',
      usage: 300000000,
    },
    {
      carrier: 'GBRME',
      usage: 200000000,
    },
    {
      carrier: 'GBRMO',
      usage: 400000000,
    },
    {
      carrier: 'GBRMT',
      usage: 300000000,
    },
    {
      carrier: 'GBRME',
      usage: 200000000,
    },
    {
      carrier: 'GBRMO',
      usage: 400000000,
    },

  ],

};
describe('MarketShareDialog', () => {
  const defaultProps = {
    columns: [
      {
        headerName: '#',
        field: 'id',
        sortable: false,
      }, {
        headerName: 'Carrier',
        field: 'carrier',
        sortable: false,

      }, {
        headerName: 'Data Usage (MB)',
        field: 'usage',
        sortable: false,
      }, {
        headerName: 'Percentage',
        field: 'percentage',
        sortable: false,
      },
    ],
    rows: mockMarketShareData,
    maxTableHeight: '300px',
  };

  test('should be rendered MarketShare Dialog', () => {
    testRender(<MarketShareDialog open onClose={jest.fn()} />);

    expect(screen.getByTestId('Market-Share-Report')).toBeInTheDocument();
  });

  const renderMuiTable = (props, fixedColumns = {}) => render(
    <MuiTableProvider
      onChange={null}
      onChangePagination={null}
      onChangeSearch={null}
      onChangeSort={null}
      defaultSort={undefined}
    >
      <MuiTable {...props} fixedColumns={fixedColumns} />
    </MuiTableProvider>,
  );

  test('should renders MuiTable without crashing', () => {
    const { getByTestId } = renderMuiTable(defaultProps);

    expect(getByTestId('mui-table')).toBeInTheDocument();
  });

  test('should render Table Data', async () => {
    await act(() => renderMuiTable(defaultProps));

    await waitFor(() => {
      const firstColumnHeader = screen.getAllByRole('columnheader')[1];

      expect(firstColumnHeader.textContent).toBe(defaultProps.columns[1].headerName);
    });
  });
  test('should render Table Header Data', async () => {
    await act(() => renderMuiTable(defaultProps));

    await waitFor(() => {
      expect(screen.getByText('#')).toBeInTheDocument();
    });
  });
  test('should render column Data Usage (MB) Table Header Data', async () => {
    await act(() => renderMuiTable(defaultProps));

    await waitFor(() => {
      expect(screen.getByText('Data Usage (MB)')).toBeInTheDocument();
    });
  });
});
