/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useState } from 'react';
import { Box, Button } from '@mui/material';
import Dialog from 'shared/Dialog/Dialog';
import useAbortController from 'core/hooks/useAbortController';
import { RESPONSE } from 'core/utilities/constants';
import MarketShare from 'shared/MarketShare/MarketShare';
import { currentYearMonthDay, lastDayOfSelectedMonth } from 'core/utilities/formatDate';
import { bytesToMb, getNumberWithCommas } from 'core/utilities/toMoneyFormat';
import { useParams } from 'react-router-dom';
import { getAccount, getMarketShareReport } from 'features/SimManagement/api.service';
import { IMarketShare } from 'features/SimManagementDetail/SimManagement.module';
import useMarketShareTableColumn from './useMarketShareTableColumn';
import { parseMarketData } from './mockData';

interface IMarketShareDialog {
  open: boolean,
  onClose: () => void,
  marketShareFor?:string
}

const MarketShareDialog = ({
  open, onClose, marketShareFor,
}: IMarketShareDialog) => {
  const [loading, setLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string | Date>(currentYearMonthDay);
  const [marketShareData, setMarketShareData] = useState<IMarketShare | undefined>();
  const [errorMessage, setErrorMessage] = useState('');
  const { cancelPreviousRequest, setNewController } = useAbortController();
  const columns = useMarketShareTableColumn();
  const { imsi, id } = useParams();
  const user = localStorage.getItem('userDetails');
  const userProfile = user ? JSON.parse(user) : '';
  const getData = async () => {
    let marketshareValue;
    if (marketShareFor === 'imsi') {
      marketshareValue = imsi;
    } else if (marketShareFor === 'account') {
      marketshareValue = userProfile?.accountId;
    }
    cancelPreviousRequest();
    const { signal } = setNewController();
    try {
      setLoading(true);
      if (errorMessage !== '') { setErrorMessage(''); }
      const endDate = lastDayOfSelectedMonth(selectedDate);
      const
        marketSharResponse = await
        getMarketShareReport(marketShareFor, marketshareValue, signal, selectedDate, endDate);

      if (marketSharResponse && marketSharResponse?.data) {
        const responseSummary = marketSharResponse.data.summary;
        const { summary, chartData } = parseMarketData(
          responseSummary, marketSharResponse.data.totalUsage);
        const totalUsageMB = summary.find((item) => item.id === 'Total')?.usage || 0;
        let warningEE = false;
        let warningThreshold;
        if (marketShareFor !== 'all' && userProfile?.accountId) {
          const accountResponse = await getAccount(userProfile?.accountId);
          if (accountResponse && accountResponse.data) {
            warningThreshold = accountResponse.data.warningThreshold;
            const eeSummary = summary.find((item) => item.name === 'EE')?.percentage || '0%';
            const eeSummaryNumber = parseFloat(eeSummary.replace('%', ''));
            if (eeSummaryNumber && warningThreshold && eeSummaryNumber < warningThreshold) {
              warningEE = true;
              warningThreshold = (100 - warningThreshold);
            }
            const mareketShareParse = {
              ...marketShareData,
              summary,
              chartData,
              totalUsage: totalUsageMB,
              warningThreshold: warningEE,
              warningThresholdAccountValue: warningThreshold,
            };
            setMarketShareData(mareketShareParse);
            return;
          }
        }

        const mareketShareParse = {
          ...marketShareData,
          summary,
          chartData,
          totalUsage: totalUsageMB,
          warningThreshold: warningEE,
        };
        setMarketShareData(mareketShareParse);
      }
    } catch (errorObj: any) {
      let message = '';
      if (errorObj?.response?.status === 404) {
        message = RESPONSE.GET_MARKETSHARE_EMPTY;
      } else if (errorObj?.response?.status === 500) {
        message = RESPONSE.HTTP_STATUS_500;
      } else {
        message = errorObj?.response?.data?.detail;
      }
      setErrorMessage(message);
      setMarketShareData(undefined);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getData();
  }, [selectedDate]);

  return (

    <Dialog
      title="Market Share Report"
      data-testid="Market-Share-Report"
      onClose={() => onClose()}
      open={open}
      maxWidth="lg"
      footerchildren={(
        <Box
          display="flex"
          alignItems="flex-start"
          justifyContent="space-between"
        >

          <Button
            sx={{ p: '12px 44px' }}
            variant="contained"
            color="primary"
            onClick={() => onClose()}
          >
            OK
          </Button>

        </Box>
      )}
    >
      <MarketShare
        columns={columns}
        loading={loading}
        marketShareData={marketShareData}
        errorMessage={errorMessage}
        onChangeDate={(dateParam) => {
          setSelectedDate(dateParam);
        }}
        chartData={marketShareData?.chartData || []}
      />
    </Dialog>
  );
};
MarketShareDialog.defaultProps = {
  marketShareFor: 'all',
};
export default MarketShareDialog;
