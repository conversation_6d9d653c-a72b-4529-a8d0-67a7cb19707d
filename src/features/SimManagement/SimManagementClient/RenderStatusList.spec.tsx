import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import StatusText from 'shared/Status/StatusText';
import { Tooltip } from '@mui/material';
import RenderStatusList from './RenderStatusList';

const row = {
  totalDeactivatedSims: 5,
  totalPendingSims: 3,
  totalReadyActivationSims: 7,
};
const row1 = {
  simStatus: {
    totalDeactivatedSims: 5,
    totalPendingSims: 3,
    totalReadyActivationSims: 7,
  },
  totalSims: 15,
};

describe('RenderStatusList', () => {
  it('renders the correct number of StatusText components', async () => {
    const { getByText } = render(
      <Tooltip
        title={(
          <RenderStatusList
            totalDeactivatedSims={row1.simStatus.totalDeactivatedSims}
            totalPendingSims={row1.simStatus.totalPendingSims}
            totalReadyActivationSims={row1.simStatus.totalReadyActivationSims}
          />
    )}
        arrow
        placement="top"
      >
        <span>
          <StatusText
            status="totalothers"
            message={`${row1.totalSims} `}
          />
        </span>
      </Tooltip>);
    const icon = getByText('15');

    act(() => {
      fireEvent(
        icon,
        new MouseEvent('mouseover', {
          bubbles: true,
        }),
      );
    });

    const { findByDisplayValue } = render(<RenderStatusList row={row} />);
    // Wait for the tooltip to show up
    findByDisplayValue('5 Deactivated').then((tooltipText) => {
      expect(tooltipText).toBeInTheDocument();
    });
    findByDisplayValue('3 Pending').then((tooltipText) => {
      expect(tooltipText).toBeInTheDocument();
    });
    findByDisplayValue('7 Ready for activation').then((tooltipText) => {
      expect(tooltipText).toBeInTheDocument();
    });
  });

  it('renders the correct status messages', () => {
    const { getByText } = render(
      <Tooltip
        title={(
          <RenderStatusList
            totalDeactivatedSims={row1.simStatus.totalDeactivatedSims}
            totalPendingSims={row1.simStatus.totalPendingSims}
            totalReadyActivationSims={row1.simStatus.totalReadyActivationSims}
          />
    )}
        arrow
        placement="top"
      >
        <span>
          <StatusText
            status="totalothers"
            message={`${row1.totalSims} `}
          />
        </span>
      </Tooltip>);
    const icon = getByText('15');

    act(() => {
      fireEvent(
        icon,
        new MouseEvent('mouseover', {
          bubbles: true,
        }),
      );
    });

    const { findByDisplayValue } = render(<RenderStatusList row={row} />);
    // Wait for the tooltip to show up
    findByDisplayValue('Deactivated').then((tooltipText) => {
      expect(tooltipText).toBeInTheDocument();
    });
    findByDisplayValue('Pending').then((tooltipText) => {
      expect(tooltipText).toBeInTheDocument();
    });
    findByDisplayValue('Ready for activation').then((tooltipText) => {
      expect(tooltipText).toBeInTheDocument();
    });
  });
});
