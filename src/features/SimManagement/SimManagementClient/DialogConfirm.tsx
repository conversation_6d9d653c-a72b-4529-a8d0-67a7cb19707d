import {
  Box, Button,
  Typography,
} from '@mui/material';
import React, { useEffect } from 'react';
import Dialog from 'shared/Dialog/Dialog';
 

interface IDialogConfirm{
    open:boolean,
    onSuccesh:()=>void,
    onClose:()=>void,
    status:string,
    simsCount: any,
    setActivateProfile: any,
    activateProfile:string
    accountDetails:any
}

const DialogConfirm = ({
  open, onSuccesh, onClose, status, simsCount,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  setActivateProfile, activateProfile, accountDetails,
}:IDialogConfirm) => {
  const displayActiveIcon = status === 'Deactivated' || status === 'Ready for Activation';
  const displayDeactiveIcon = status === 'Active';
  let statusMessage;
  if (displayActiveIcon) {
    statusMessage = 'Active';
  } else if (displayDeactiveIcon) {
    statusMessage = 'Deactivated';
  }

  useEffect(() => {
    // setActivateProfile((accountDetails?.simProfile as IOption)?.ref || '');
  }, [accountDetails]);

  return (

    <Dialog
      title="Are you sure?"
      onClose={() => onClose()}
      open={open}
      sx={{
        '& .MuiDialogTitle-root': {
          p: 8,
        },

      }}
      footerchildren={(
        <Box
          display="flex"
          alignItems="flex-start"
          gap="15px"
          justifyContent="space-between"
        >
          <Button
            sx={{ p: '0px 25px' }}
            variant="contained"
            color="primary"
            onClick={() => onSuccesh()}
          >
            Confirm

          </Button>

          <Button
            sx={{ p: '0px 25px', backgroundColor: '#ebe3f6', border: '0px' }}
            variant="outlined"
            onClick={() => onClose()}
          >
            Cancel
          </Button>

        </Box>
          )}
    >
      <Box>

        <Typography
          variant="body1"
          component="p"
          sx={{ paddingBottom: '32px' }}
        >
          {(simsCount && simsCount !== '' && simsCount > 1) ? (
            <Box display="flex">
              After confirmation, selected
              <Box component="span" sx={{ fontWeight: 'bold', marginLeft: '5px', marginRight: '5px' }}>{simsCount}</Box>
              SIM statuses will be changed to
              {' '}
              {statusMessage}
            </Box>
          ) : (
            <div>
              After confirmation, selected  SIM status will be changed to
              {' '}
              {statusMessage}
            </div>
          )}
        </Typography>
      </Box>

    </Dialog>

  );
};

export default DialogConfirm;
