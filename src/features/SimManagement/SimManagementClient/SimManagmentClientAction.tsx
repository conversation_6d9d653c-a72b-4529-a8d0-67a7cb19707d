import {
  Box,
  IconButton, Tooltip,
} from '@mui/material';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import { PERMISSION, REPOSITORY, ROUTE_PERMISSION } from 'core/utilities/constants';
import React from 'react';
import { AiOutlineCheckCircle, AiOutlineCloseCircle } from 'react-icons/ai';
import { GrUpdate } from 'react-icons/gr';
import { useIMSIAllocationsContext } from '../IMSIAllocations/IMSIAllocationsContext';

const SimManagmentClientAction = (row, onSimManagmentClientActionClick) => {
  const { simStatus } = row;
  const {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    setIsActivityGoing, openDialogConfirmMsisdn,
  } = useIMSIAllocationsContext();

  if (!simStatus || simStatus === 'Pending' || typeof simStatus === 'object') {
    return null;
  }


  const displayActiveIcon = !!((simStatus === 'Deactivated' || simStatus === 'Ready for Activation'));
  const displayDeactiveIcon = !!(simStatus === 'Active');

  const openUpdateMsisdnDialog = async (event, rowData) => {
    event.stopPropagation();
    openDialogConfirmMsisdn({
      isEnable: true,
      status: simStatus,
      row: rowData,
    });
  };

  return (
    <Box>
      {displayDeactiveIcon && (
        <CommonAuthwrapper
          permission={[ROUTE_PERMISSION.SIM_CEASE]}
          repository={[REPOSITORY.SIM_MANAGEMENT]}
        >
          <Tooltip title={displayActiveIcon ? 'Activate' : 'Deactivate'} arrow placement="top">
            <IconButton
              sx={{
                visibility: 'hidden',
              }}
              onClick={(e) => onSimManagmentClientActionClick(e, row)}
            >
              <AiOutlineCloseCircle
                className="icon-color"
                data-testid="close-icon"
                strokeWidth={12}
                size={22}
              />
            </IconButton>
          </Tooltip>
        </CommonAuthwrapper>
      )}
      {(displayActiveIcon) && (
        <CommonAuthwrapper
          permission={[ROUTE_PERMISSION.SIM_PROVIDE]}
          repository={[REPOSITORY.SIM_MANAGEMENT]}
        >
          <Tooltip title={displayActiveIcon ? 'Activate' : 'Deactivate'} arrow placement="top">
            <IconButton
              sx={{
                visibility: 'hidden',
              }}
              onClick={(e) => onSimManagmentClientActionClick(e, row)}
            >
              <AiOutlineCheckCircle
                className="icon-color"
                data-testid="check-icon"
                strokeWidth={12}
                size={22}
              />
            </IconButton>
          </Tooltip>
        </CommonAuthwrapper>
      )}
      {displayActiveIcon && (
        <CommonAuthwrapper
          permission={[PERMISSION?.UPDATE_SIM_CARD_DETAILS]}
          repository={[REPOSITORY.SIM_MANAGEMENT]}
        >
          <Tooltip title="Update IMSI" arrow placement="top">
            <IconButton
              className="toggleActionButton imsi-reallocations__top-bar_create-button"
              sx={{
                visibility: 'hidden',
              }}
              onClick={(event) => {
                event.stopPropagation();
                openUpdateMsisdnDialog(event, row);
              }}
            >
              <GrUpdate
                className="icon-color"
                data-testid="direction-icon"
                size={18}
                strokeWidth={12}
              />
            </IconButton>
          </Tooltip>
        </CommonAuthwrapper>
      )}
    </Box>
  );
};
export default SimManagmentClientAction;
