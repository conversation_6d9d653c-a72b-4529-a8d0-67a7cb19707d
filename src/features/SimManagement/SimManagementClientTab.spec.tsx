import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material';
import { MemoryRouter, Routes, Route, useSearchParams } from 'react-router-dom';
import SimManagementClientTab from './SimManagementClientTab';
import SimManagementClient from './SimManagementClient/SimManagementClient';
import { organizationTypes } from 'user.model';

jest.mock('./SimManagementClient/SimManagementClient', () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid="sim-management-client">SimManagementClient</div>),
}));

jest.mock('features/SimManagement/BulkOperationsTracker', () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid="bulk-operations-tracker">BulkOperationsTracker</div>),
}));

jest.mock('assets/images/SimIcon', () => ({
  __esModule: true,
  default: () => <div data-testid="sim-icon">SimIcon</div>,
}));

jest.mock('react-icons/ai', () => ({
  AiOutlineCloudServer: () => <div data-testid="cloud-server-icon">CloudServerIcon</div>,
}));

jest.mock('@nv2/nv2-pkg-js-shared-components/lib/TabPanel', () => ({
  __esModule: true,
  default: ({ children, value, index }) => (
    <div data-testid={`tab-panel-${index}`} hidden={value !== index}>
      {value === index && children}
    </div>
  ),
}));

jest.mock('@nv2/nv2-pkg-js-shared-components/lib/Tabs', () => ({
  __esModule: true,
  default: ({ selectedNewTab, tabItemsConfig, tabIndex }) => (
    <div data-testid="tabs-component">
      {tabItemsConfig.map((tab, index) => (
        <button
          key={index}
          data-testid={`tab-button-${index}`}
          onClick={(e) => selectedNewTab(e, index)}
          disabled={tab.disabled}
          aria-selected={tabIndex === index}
        >
          {tab.name}
        </button>
      ))}
    </div>
  ),
}));

jest.mock('core/CommonAuthWrapper', () => ({
  __esModule: true,
  default: ({ children, repository }) => (
    <div data-testid={`auth-wrapper-${repository?.[0] || 'default'}`}>{children}</div>
  ),
}));

jest.mock('shared/FadeIn', () => ({
  __esModule: true,
  default: ({ children }) => <div data-testid="fade-in">{children}</div>,
}));

const SearchParamsSpy = ({ onParamsChange }) => {
  const [searchParams] = useSearchParams();
  React.useEffect(() => {
    onParamsChange(searchParams);
  }, [searchParams, onParamsChange]);
  return null;
};

const mockUser = {
  email: "<EMAIL>",
  firstName: "test",
  lastName: "user",
  id: "0f5e8253-2360-4898-9f32-30b8d9c9874a",
  organization: {
    name: "ABC",
    id: 91,
    parent_id: '1',
    type: organizationTypes.CLIENT,
  },
};

const theme = createTheme();

const renderWithRouter = (ui, { route = '/', initialEntries = [route] } = {}) => {
  const onParamsChangeMock = jest.fn();
  
  const result = render(
    <MemoryRouter initialEntries={initialEntries}>
      <ThemeProvider theme={theme}>
        <SearchParamsSpy onParamsChange={onParamsChangeMock} />
        <Routes>
          <Route path="*" element={ui} />
        </Routes>
      </ThemeProvider>
    </MemoryRouter>
  );
  
  return {
    ...result,
    onParamsChangeMock
  };
};

describe('SimManagementClientTab', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders the component correctly', () => {
    renderWithRouter(<SimManagementClientTab user={mockUser} />);
    
    expect(screen.getByTestId('sim-management-page')).toBeInTheDocument();
    expect(screen.getByTestId('tabs-component')).toBeInTheDocument();
  });

  jest.mock('@nv2/nv2-pkg-js-shared-components/lib/TabPanel', () => ({
    __esModule: true,
    default: ({ children, value, index }) => {
      const isHidden = value !== index;
      const props = {
        'data-testid': `tab-panel-${index}`,
        ...(isHidden && { hidden: true })
      };
      
      return (
        <div {...props}>
          {value === index && children}
        </div>
      );
    },
  }));

  test('updates URL when tab is changed', async () => {
    const { onParamsChangeMock } = renderWithRouter(<SimManagementClientTab user={mockUser} />);
    
    fireEvent.click(screen.getByTestId('tab-button-1'));
    
    await waitFor(() => {
      expect(onParamsChangeMock).toHaveBeenCalled();
      const lastCall = onParamsChangeMock.mock.calls.length - 1;
      const searchParams = onParamsChangeMock.mock.calls[lastCall][0];
      expect(searchParams.get('tab')).toBeTruthy();
    });
  });

  test('sets default tab when no tab parameter is provided', async () => {
    jest.mock('PrivateRotes', () => ({
      GetAuthorization: jest.fn().mockReturnValue(true)
    }));
    
    const { onParamsChangeMock } = renderWithRouter(<SimManagementClientTab user={mockUser} />);
    
    await waitFor(() => {
      expect(onParamsChangeMock).toHaveBeenCalled();
      const lastCall = onParamsChangeMock.mock.calls.length - 1;
      const searchParams = onParamsChangeMock.mock.calls[lastCall][0];
      expect(searchParams.get('tab')).toBeTruthy();
    });
  });

  test('passes user prop to child components', () => {
    renderWithRouter(<SimManagementClientTab user={mockUser} />);
    
    expect(SimManagementClient).toHaveBeenCalledWith(
      expect.objectContaining({ user: mockUser }),
      expect.anything()
    );
  });

  test('handles undefined user prop', () => {
    renderWithRouter(<SimManagementClientTab user={undefined} />);
    
    expect(SimManagementClient).toHaveBeenCalledWith(
      expect.objectContaining({ user: undefined }),
      expect.anything()
    );
  });
});
