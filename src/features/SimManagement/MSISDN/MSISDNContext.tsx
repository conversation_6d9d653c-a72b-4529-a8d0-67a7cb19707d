import React, { createContext, ReactElement } from 'react';

type contextType = { [key: string] : any }

const MSISDNContext = createContext<contextType>({});

interface IAppContextProvider {
  children: ReactElement | ReactElement[] | null;
  value: contextType
}

const MSISDNContextProvider = ({ children, value }: IAppContextProvider) => (
  <MSISDNContext.Provider value={value}>
    {children}
  </MSISDNContext.Provider>
);

const useMSISDNContextProvider = () => React.useContext(MSISDNContext);

export { MSISDNContext, MSISDNContextProvider, useMSISDNContextProvider };

export default MSISDNContextProvider;
