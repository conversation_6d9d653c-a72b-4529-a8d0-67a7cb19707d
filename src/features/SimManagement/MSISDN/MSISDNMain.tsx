import { Box, Typography } from '@mui/material';
import MuiTable from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';
import { MuiTableProvider } from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable/MuiTableContext';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import useAbortController from 'core/hooks/useAbortController';
import useMuiTableSearchParams from 'core/hooks/useMuiTableSearchParams';
import { getNumberWithCommas } from 'core/utilities/toMoneyFormat';
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
 
import CommonAuthwrapper from 'core/CommonAuthWrapper';
 
import { useAppContext } from 'AppContextProvider';
import SimIconHeader from 'assets/images/SimIconHeader';
import exportCSVFile from 'core/utilities/exportCSVFile';
import { getSearchSortModel, onExportCSVFileHandle } from 'core/utilities/exportCSVMAP';
import { TGetCurrentThemeColors } from 'features/models';
import { toastError } from 'core/utilities/toastHelper';
import Loader from 'shared/Loader';
import { PERMISSION, REPOSITORY } from 'core/utilities/constants';
import { GetAuthorization } from 'PrivateRotes';
import { AiOutlineLeft } from 'react-icons/ai';
import IMSIRangeSimModalIcon from '../IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/IMSIRangeSimModalIcon';
import { StyledTotalButton, StyledTotalSimData } from '../IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/IMSIRangeSimModalUI';
import { DataStates } from '../IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants';
import { exportMsisdn, getAllMSISDN, totalMsisdn } from '../api.service';
import MSISDNContextProvider from './MSISDNContext';
import MSISDNExport from './MSISDNExport';
import UploadMsisdn from './UploadMsisdn';
import useMSISDNColummns from './useMSISDNColumns';
import MSISDNTotalModal from './MSISDNTotalModal';

export const totalRowIccid = 'Total';

const getRowClassName = ({ id }) => {
  if (id !== totalRowIccid) return '';

  return 'MuiDataGrid-row-bold';
};

const MSISDNMain = () => {
  interface searchparam {
    page: string | number;
    pageSize: string | number;
    field: string | null;
    sort: string | null;
    search: string | null;
  }
  const navigate = useNavigate();
  const location = useLocation();
  const { cancelPreviousRequest, setNewController } = useAbortController();
  const { primaryColor, getBrandColors } = useAppContext();
  const [totalRemainingCountState, setTotalRemainingCountState] = useState(false);
  const [totalRemainingCount, setTotalRemainingCount] = useState({
    totalCount: 0,
    national: 0,
    international: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [totalMsisdnModal, setTotalMsisdnModal] = useState(false);
  const { columns } = useMSISDNColummns();
  const [noData, setNoData] = useState({});
  const [loading, setLoading] = useState(false);
  const [rowCount, setRowCount] = useState(0);
  const [msisdnList, setMsisdnList] = useState<any>([]);
  const [paramState, setParamState] = useState<searchparam>({
    page: 1,
    pageSize: 10,
    field: '',
    sort: '',
    search: '',
  });
  const [dataState, setDataState] = useState<DataStates>(DataStates.Loaded);

  const {
    generateParamsForUrl,
    getParamsFromUrl,
    defaultPagination,
    defaultSort,
    initialSearchValue,
  } = useMuiTableSearchParams();

  const getData = async (page, pageSize, field, sort, search) => {
    cancelPreviousRequest();
    const { signal } = setNewController();

    const newSearchParams = generateParamsForUrl(
      page,
      pageSize,
      field,
      sort,
      search,
    );
    // newSearchParams.set('tab', TabsName.MSISDN);
    const newUrl = `${location.pathname}?tab=msisdn&${newSearchParams.toString()}`;
    navigate(newUrl, { replace: true });
    try {
      setLoading(true);
      const { data, data: { results, totalCount } } = await
      getAllMSISDN(page, pageSize, field, sort, search, signal);
      setMsisdnList(results);
      setRowCount(totalCount);
      setParamState({ ...paramState, page: data.page, pageSize: data.page_size });
      setLoading(false);
    } catch (error: any) {
      if (error?.code !== "ERR_CANCELED") {
        setLoading(false);
      }
      if (error?.response?.status === 404) {
        setMsisdnList([]);
        setRowCount(0);
      }
      setNoData({
        icon: <SimIconHeader />,
        title: 'No MSISDN Numbers available.',
        description: 'You can upload new by clicking on ‘Upload MSISDN’ button',
      });
    }
  };

  const onExportCSVFile = async () => {
    const { formattedDate } = getSearchSortModel(
      getParamsFromUrl,
      setIsLoading,
    );
    const fileName = `MSISDN_${formattedDate}.csv`;
    try {
      const { data: csvData } = await exportMsisdn();
      onExportCSVFileHandle(csvData, fileName, exportCSVFile, setIsLoading);
    } catch (error: any) {
      toastError(error?.response?.data?.detail);
      console.log('error: ', error);
      setIsLoading(false);
    }
  };

  const onChange = ({ page, pageSize }, { field, sort }, search) => {
    setParamState({
      page,
      pageSize,
      field,
      sort,
      search,
    });
    getData(page, pageSize, field, sort, search);
  };

  const checkMsisdnCount = GetAuthorization(
    [PERMISSION?.FREE_MSISDN_COUNT], [REPOSITORY?.SIM_MANAGEMENT]);

  const fetchTotalRemainingCount = async () => {
    if (!checkMsisdnCount) return;

    try {
      setDataState(DataStates.Loading);
      setTotalRemainingCountState(true);
      const { data } = await totalMsisdn();
      setTotalRemainingCount(data);
      setTotalRemainingCountState(false);
      setDataState(DataStates.Loaded);
    } catch (error: any) {
      setTotalRemainingCountState(false);
      setDataState(DataStates.Error);
      console.log('error: ', error);
    }
  };

  useEffect(() => {
    const {
      page, pageSize, field, sort, search,
    } = getParamsFromUrl();
    getData(page, pageSize, field, sort, search);
    fetchTotalRemainingCount();
  }, []);

  const navigateTo = window.location.origin;

  const navigateBack = () => {
    const baseUrl = window.location.origin;
    if (navigateTo === baseUrl) {
      window.location.href = navigateTo;
    } else {
      navigate(navigateTo);
    }
  };

  return (
    <Box
      sx={{
        '& .MuiDataGrid-row-bold': {
          '& .MuiCheckbox-root': {
            display: 'none !important',
          },
          ':hover': {
            cursor: 'default !important',
          },
        },
        '& .MuiDataGrid-columnHeaders': {
          backgroundColor: `${styles.lightColor100} !important`,
        },
        '& .MuiDataGrid-virtualScrollerContent': {
          minHeight: '160px !important',
        },
        '& .MuiDataGrid-columnHeaderTitleContainerContent': {
          '& .MuiCheckbox-root': {
            // display: 'none !important',
          },
        },
        '& .interactions__actions': {
          justifyContent: 'flex-start',
          '& .interactions__actions-selections': {
            display: 'none',
          },
        },
        '.MuiTablePagination-toolbar div:nth-of-type(3) span:nth-of-type(3)': {
          display: 'inline-flex',
          gap: '10px',
        },
      }}
    >
      <Box>
        <MSISDNContextProvider
          value={{
            getParamsFromUrl,
          }}
        >
          <MuiTableProvider
            defaultPagination={defaultPagination}
            onChange={onChange}
            onChangePagination={onChange}
            onChangeSearch={onChange}
            initialSearchValue={initialSearchValue}
            onChangeSort={onChange}
            defaultSort={defaultSort}
          >
            <MuiTable
              sx={{
                '& .MuiDataGrid-row:hover': {
                  cursor: 'pointer',
                  '& .MuiButtonBase-root': {
                    visibility: 'visible ',
                  },
                },
              }}
              Actions={() => (
                <Box
                  display="flex"
                  justifyContent="space-between"
                  sx={{ width: '100%' }}
                >
                  <Box display="flex" justifyContent="space-between" width="100%">
                    <Box
                      display="flex"
                      ml={2}
                      gap={2}
                      sx={{
                        '.imsi-range-total-sim-toggle-button_sim-icon': {
                          backgroundColor: '#FEEEEC',
                        },
                        '.imsi-range-total-sim-toggle-button_sim-icon svg path': {
                          stroke: '#F7735D',
                        },
                      }}
                    >

                      <CommonAuthwrapper
                        permission={[PERMISSION?.EXPORT_MSISDN_LIST, PERMISSION?.FREE_MSISDN_COUNT]}
                        repository={[REPOSITORY?.SIM_MANAGEMENT]}
                      >
                        <MSISDNExport
                          rowCount={totalRemainingCount?.totalCount}
                          // rowCount={rowCount}
                          onExportCSVFile={onExportCSVFile}
                          isLoading={isLoading}
                        />
                      </CommonAuthwrapper>

                      <CommonAuthwrapper
                        permission={[PERMISSION?.FREE_MSISDN_COUNT]}
                        repository={[REPOSITORY?.SIM_MANAGEMENT]}
                      >
                        <StyledTotalButton
                          type="button"
                          onClick={() => setTotalMsisdnModal(true)}
                          data-testid="imsi-range-total-sim-toggle-button"
                          sx={{
                            '.common-loader': {
                              margin: 'unset',
                            },
                          }}
                        >
                          <IMSIRangeSimModalIcon dataState={dataState} />
                          <StyledTotalSimData>
                            <Typography variant="body1">
                              Available MSISDNs
                            </Typography>
                            {totalRemainingCountState ? <Loader size={21} /> : (
                              <Typography variant="body2">
                                {getNumberWithCommas(totalRemainingCount?.totalCount)}
                              </Typography>
                            )}
                          </StyledTotalSimData>
                        </StyledTotalButton>
                      </CommonAuthwrapper>

                    </Box>

                    <MSISDNTotalModal
                      modalState={totalMsisdnModal}
                      onClose={() => setTotalMsisdnModal(false)}
                      totalRemainingCount={totalRemainingCount}
                    />

                    <CommonAuthwrapper
                      permission={[PERMISSION?.UPLOAD_MSISDN]}
                      repository={[REPOSITORY?.SIM_MANAGEMENT]}
                    >
                      <UploadMsisdn
                        parentLoading={loading}
                        fetchTotalRemainingCount={fetchTotalRemainingCount}
                        recallData={getData}
                      />
                    </CommonAuthwrapper>
                  </Box>
                </Box>
              )}
              rows={msisdnList}
              columns={columns}
              ActionsLeft={() => (
                <button
                  type="button"
                  onClick={navigateBack}
                  style={{
                    margin: '0px 10px 0px 0',
                  }}
                  className="sim-management-top-bar__back"
                >
                  <AiOutlineLeft fontSize={24} color={styles.darkColor300} />
                </button>
              )}
              loading={loading}
              rowCount={rowCount}
              primaryColor={primaryColor as string}
              getCurrentThemeColors={getBrandColors as TGetCurrentThemeColors}
              getRowClassName={getRowClassName}
              showFirstLastPageButtons
              isVisibleSearchInput
              getRowId={(row) => `${row.msisdn}`}
              onRowClick={(e) => {
                console.log('e: ', e);
                // if (e.id?.toString()?.toLocaleLowerCase() !== 'total') {
                //   return navigate(`${e.row.imsi}/sim-management`);
                // }
                // return null;
              }}
              hideFooter={!(msisdnList.length > 0)}
              noDataConfig={noData}
            />
          </MuiTableProvider>
        </MSISDNContextProvider>
      </Box>
    </Box>
  );
};

export default MSISDNMain;
