import { Box, Typography } from '@mui/material';
import ImagePlaceholder from 'assets/images/ImagePlaceholder';
import { formatDateWithHours } from 'core/utilities/formatDate';
import React, { useState } from 'react';
import ReactCountryFlag from 'react-country-flag';
import Image from 'shared/LazyLoad/Image';
import '../IMSIAllocations/IMSIAllocationsTableBody/Row/Row.scss';

const useMSISDNColummns = () => {
  const [flagError, setFlagError] = useState(false);

  const columns = [
    {
      headerName: 'MSISDN',
      field: 'msisdn',
      width: 220,
      sortable: true,
    },
    {
      headerName: 'MSISDN Type',
      field: 'msisdnFactor',
      width: 220,
      sortable: true,
    },
    {
      headerName: 'IMSI',
      field: 'imsi',
      width: 220,
      sortable: true,
    },
    {
      headerName: 'SIM Profile',
      field: 'simProfile',
      width: 180,
      sortable: true,
      valueGetter: ({ value }) => {
        if (value) {
          return value.replace(/_/g, ' ');
        }
        return '';
      },
    },
    {
      headerName: 'Account',
      field: 'accountName',
      width: 220,
      sortable: false,
      renderCell: ({ row }) => (
        <Box display="flex" alignItems="center" columnGap={2}>
          <Image
            src={row.logoUrl || ''}
            alt={row.name}
            style={{
              display: 'block',
              maxWidth: '30px',
              width: '57px',
              height: 'auto',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          />
          <Typography variant="body1" fontSize="13px">
            {`${row.accountName ? row?.accountName : ''}`}
          </Typography>
        </Box>
      ),
    },
    {
      headerName: 'Country',
      field: 'country',
      width: 180,
      sortable: false,
      renderCell: ({ row }) => (
        <div className="account-table-cell__country">
          {
            flagError && <ImagePlaceholder className="account-table-cell__country_flag" />
          }
          {
            !flagError && (
              <ReactCountryFlag
                className="account-table-cell__flag"
                style={{ width: '23px', marginRight: '10px' }}
                countryCode={row?.country || ''}
                svg
                onError={() => setFlagError(true)}
              />
            )
          }
          <Typography variant="body1" fontSize="13px">
            {row?.country}
          </Typography>
        </div>
      ),
    },
    {
      headerName: 'SIM Provider',
      field: 'simProvider',
      width: 180,
      sortable: false,
    },
    {
      headerName: 'Uploaded/Created',
      field: 'createdAt',
      width: 240,
      sortable: true,
      valueGetter: ({ value }) => {
        if (value) {
          return formatDateWithHours(value);
        }
        return '';
      },
    },
    {
      headerName: 'Uploaded By',
      field: 'uploadedBy',
      width: 220,
      sortable: false,
    },
  ];

  return { columns };
};

export default useMSISDNColummns;
