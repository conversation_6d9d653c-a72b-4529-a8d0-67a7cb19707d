import React from 'react';
import { IconButton, Tooltip } from '@mui/material';
import Loader from 'shared/Loader';
import { GridExport } from 'assets/images/ImagePlaceholder';

interface MSISDNActionsProps {
  rowCount: number,
  isLoading: boolean,
  onExportCSVFile: () => void,
}

const MSISDNExport = ({
  rowCount, isLoading, onExportCSVFile,

}: MSISDNActionsProps) => (
  <Tooltip title={rowCount !== 0 ? `Export ${rowCount} Record(s)` : ''} arrow placement="top">

    <IconButton
      onClick={onExportCSVFile}
      disabled={isLoading || !rowCount}
      data-testid="Export"
      sx={{
        '&:hover path': {
          stroke: 'unset',

        },
      }}
    >
      {
        isLoading
          ? <Loader size={21} />
          : <GridExport />
      }
    </IconButton>

  </Tooltip>
);

MSISDNExport.defaultProps = {

};

export default MSISDNExport;
