import {
  Modal, Table, TableBody, TableHead, TableRow, Typography,
} from '@mui/material';
import React from 'react';
import { GrClose } from 'react-icons/gr';
import { StyledCreateAllocationModal } from '../IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/componentUIparts';
import { StyledTableCell, StyledTableContainer, StyledTableRow } from '../componentUIparts';
import { StyledCloseButton } from '../IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/IMSIRangeSimModalTable/IMSIRangeSimModalTableUI';

const MSISDNTotalModal = ({ modalState, onClose, totalRemainingCount }:
  {
    modalState: boolean; onClose: () => void; totalRemainingCount: {
      totalCount: number;
      national: number;
      international: number;
    }
  }) => (
    <Modal open={modalState} onClose={onClose}>
      <StyledCreateAllocationModal
        data-testid="upload-msisdn-modal"
        className="upload-msisdn-modal"
      >
        <div className="upload-msisdn-modal__header">
          <Typography variant="h2" component="h2">
            Available MSISDNs
          </Typography>
          <StyledCloseButton
            data-testid="upload-msisdn-modal_close-button"
            type="button"
            onClick={onClose}
          >
            <GrClose size={21} />
          </StyledCloseButton>
        </div>
        <StyledTableContainer sx={{ maxHeight: '300px', marginTop: '10px', paddingBottom: '0px' }}>
          <Table sx={{ border: '1px solid #E7E7EE' }}>
            <TableHead>
              <TableRow>
                <StyledTableCell th bold align="left">SIM Provider</StyledTableCell>
                <StyledTableCell th bold>MSISDN Type</StyledTableCell>
                <StyledTableCell th bold align="right">Quantity</StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <StyledTableRow>
                <StyledTableCell>
                  NR
                </StyledTableCell>
                <StyledTableCell>
                  National
                </StyledTableCell>
                <StyledTableCell align="right">
                  {totalRemainingCount.national}
                </StyledTableCell>
              </StyledTableRow>
              <StyledTableRow>
                <StyledTableCell>
                  NR
                </StyledTableCell>
                <StyledTableCell>
                  International
                </StyledTableCell>
                <StyledTableCell align="right">
                  {totalRemainingCount.international}
                </StyledTableCell>
              </StyledTableRow>
              <StyledTableRow>
                <StyledTableCell sx={{
                  background: '#ebe3f6 !important',
                }}
                >
                  <b>
                    Total
                  </b>
                </StyledTableCell>
                <StyledTableCell sx={{
                  background: '#ebe3f6 !important',
                }}
                >
                  <b />
                </StyledTableCell>
                <StyledTableCell
                  sx={{
                    background: '#ebe3f6 !important',
                  }}
                  align="right"
                >
                  <b>
                    {totalRemainingCount.totalCount}
                  </b>
                </StyledTableCell>
              </StyledTableRow>
            </TableBody>
          </Table>
        </StyledTableContainer>
      </StyledCreateAllocationModal>
    </Modal>
);

export default MSISDNTotalModal;
