import {
  Box, Button, FormControl, IconButton, InputLabel, MenuItem, Modal, Select, Table,
  TableBody, TableHead, TableRow, Tooltip,
  TooltipProps, Typography, styled,
  tooltipClasses,
} from '@mui/material';
import ActionButtonWithTooltip from '@nv2/nv2-pkg-js-shared-components/lib/ActionButtonWithTooltip';
import { MB_CSV_FILE_SIZE } from 'core/utilities/constants';
import generateCSVFile from 'core/utilities/generateCSVFile';
import { toastError, toastSuccess } from 'core/utilities/toastHelper';
import { CSV_SUPPORTED_FORMATS, TOOL_TIP_UPLOAD_MSISDN } from 'features/constants';
import { useFormik } from 'formik';
import React, { useState } from 'react';
import { AiOutlineCloudUpload, AiOutlineMobile } from 'react-icons/ai';
import { GrCircleInformation, GrClose, GrFormDown } from 'react-icons/gr';
import LatestDropZone from 'shared/Dropzone/LatestDropZone';
import Loader from 'shared/Loader';
import * as Yup from 'yup';
import { uploadMsisdn } from '../api.service';
import { StyledTableCell, StyledTableContainer, StyledTableRow } from '../componentUIparts';
import { StyledCreateAllocationModal } from '../IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/componentUIparts';
import { MSISDN_PROVIDER } from '../IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants';
import { StyledCloseButton } from '../IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/IMSIRangeSimModalTable/IMSIRangeSimModalTableUI';
import { IMSISDNErrors } from '../SimManagement.models';
import './UploadMsisdn.scss';

interface msisdnListProps {
  errorMsisdn: number;
  totalMsisdn: number;
  errorResults: IMSISDNErrors[];
}

interface IUploadMsisdnProps {
  parentLoading: boolean;
  fetchTotalRemainingCount: () => void;
  recallData: any;
}

const UploadMsisdn: React.FC<IUploadMsisdnProps> = ({
  parentLoading,
  fetchTotalRemainingCount,
  recallData,
}) => {
  const [open, setOpen] = React.useState(false);
  const [errorModal, setErrorMOdal] = React.useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loader, setLoader] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [msisdnList, setMsisdnList] = useState<msisdnListProps>({
    errorMsisdn: 0,
    totalMsisdn: 0,
    errorResults: [],
  });
  const [image, setImage] = useState<File>();

  const initialValues = {
    file: null,
    // msisdnFactor: '',
  };

  const validationSchema = Yup.object().shape({
    // msisdnFactor: Yup.string().required('MSISDN Type is Required'),
    file: Yup.mixed()
      .required('A file is required of valid columns and formats.')
      .test(
        'fileSize',
        'File too large',
        (value) => value && value.size <= MB_CSV_FILE_SIZE,
      )
      .test(
        'fileFormat',
        'Unsupported Format',
        (value) => value && CSV_SUPPORTED_FORMATS.includes(value.type),
      ),
  });

  const formik = useFormik({
    validateOnBlur: false,
    validateOnChange: true,
    initialValues,
    validationSchema,
    onSubmit: async (values) => {
      const formData = new FormData();
      if (values.file && values.file !== null) {
        formData.append('file', values.file);
      }

      try {
        setLoader(true);
        setErrorMessage(null);
        const { data } = await uploadMsisdn(formData);
        setMsisdnList(data);
        setLoader(false);
        setOpen(false);
        toastSuccess(data?.message);
      } catch (err: any) {
        setErrorMessage(err?.response?.data?.detail);
        setMsisdnList({
          errorMsisdn: 0,
          totalMsisdn: 0,
          errorResults: [],
        });
        formik.setFieldValue('file', null);
        setImage(undefined);
        toastError(err?.response?.data?.detail || 'Something went wrong while uploading MSISDN');
        setLoader(false);
      }
    },
  });

  const uploadImage = (fileData) => {
    formik.setFieldValue('file', fileData);
    setErrorMessage(null);
  };

  const removeImage = () => {
    formik.setFieldValue('file', null);
    setImage(undefined);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleBackDrop = (event, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) { return; }
    handleClose();
  };

  const handleErrorModalClose = () => {
    setErrorMOdal(false);
    setImage(undefined);
    formik.resetForm();
    fetchTotalRemainingCount();
    recallData(1, 10);
  };

  const CustomWidthTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className ?? '' }} />
  ))({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: 500,
    },
  });

  const downLoad = () => {
    const sims = msisdnList?.errorResults?.map((t) => ({ MSISDN: t.msisdn, ErrorType: t.issue }));
    if (sims) {
      const dotIndex = image?.name?.lastIndexOf('.');
      const name = dotIndex !== -1
        ? `${image?.name?.substring(0, dotIndex)}_Invalid_IMSI`
        : `${image?.name}_Invalid_IMSI`;
      generateCSVFile(sims, name, setIsLoading, ',');
      handleErrorModalClose();
    }
  };

  return (
    <Box>
      <ActionButtonWithTooltip
        title="Upload MSISDN"
        data-testid="imsi-allocations__top-bar_create-button"
        className="imsi-allocations__top-bar_create-button"
        action={() => { setOpen(true); }}
        icon={<AiOutlineMobile size={21} />}
        disabled={parentLoading || false}
      />

      {/* ********************* Upload msisdn modal ****************** */}
      <Modal open={open} onClose={handleBackDrop}>
        <StyledCreateAllocationModal
          data-testid="upload-msisdn-modal"
          className="upload-msisdn-modal"
        >
          <div className="upload-msisdn-modal__header">
            <Typography variant="h2" component="h2" fontWeight={700}>
              Upload MSISDN(s)
            </Typography>
            <StyledCloseButton
              data-testid="upload-msisdn-modal_close-button"
              type="button"
              onClick={handleClose}
            >
              <GrClose size={21} />
            </StyledCloseButton>
          </div>
          <div className="upload-msisdn-modal__body">
            <form onSubmit={formik.handleSubmit}>
              {/* <FormControl className="create-allocation-modal__body_form-control">
                    <InputLabel>MSISDN Type</InputLabel>
                    <Select
                      IconComponent={() => (
                        <GrFormDown className="select-arrow-icon" size={21} />
                      )}
                      label="MSISDN Type"
                      name="msisdnFactor"
                      onChange={formik.handleChange}
                      value={formik.values.msisdnFactor}
                      error={!!formik.errors.msisdnFactor && !!formik.touched.msisdnFactor}
                    >
                      {MSISDN_TYPE_OPTIONS?.map((msisdn) => (
                        <MenuItem key={msisdn?.value} value={msisdn?.value}>
                          {msisdn?.title}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl> */}
              <FormControl className="create-allocation-modal__body_form-control">
                <InputLabel>MSISDN Provider</InputLabel>
                <Select
                  IconComponent={() => (
                    <GrFormDown className="select-arrow-icon" size={21} />
                  )}
                  label="MSISDN Provider"
                  defaultValue="NR"
                >
                  {MSISDN_PROVIDER?.map((msisdn) => (
                    <MenuItem key={msisdn?.value} value={msisdn?.value}>
                      {msisdn?.title}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <Box display="flex" alignItems="center" marginBottom="10px">
                <Typography
                  component="div"
                  variant="body2"
                  fontWeight={700}
                  color="#333333"
                >
                  Select File
                </Typography>
                <CustomWidthTooltip
                  arrow
                  placement="top"
                  title={(
                    <>
                      <Typography component="p" fontSize={14}>
                        Following criteria for successful processing:
                      </Typography>
                      <Typography>
                        <ul style={{ textIndent: '5px', fontSize: '13px' }}>
                          {TOOL_TIP_UPLOAD_MSISDN?.map((item) => (
                            <li key={item}>
                              •
                              &nbsp;
                              {item}
                            </li>
                          ))}
                        </ul>
                      </Typography>
                    </>
                  )}
                >
                  <IconButton
                    sx={{
                      height: '0px',
                      '&:hover': {
                        backgroundColor: 'none',
                      },
                    }}
                  >
                    <GrCircleInformation />
                  </IconButton>
                </CustomWidthTooltip>
              </Box>

              <LatestDropZone
                name="file"
                primaryColor="primary"
                formik={formik}
                uploadImage={uploadImage}
                setImage={setImage}
                image={image}
                dropzoneImg={<AiOutlineCloudUpload size={35} />}
                dropzoneText="Select CSV file with MSISDNs"
                removeAppImage={removeImage}
                description="File must be in .csv format"
                customErrorMessage={errorMessage}
              />
            </form>
            <div className="upload-msisdn-modal__body_buttons">
              <Button
                disabled={!(formik.isValid && formik.dirty)}
                onClick={(e) => {
                  e.preventDefault();
                  formik.handleSubmit();
                }}
                component="button"
                sx={{
                  p: '0px 25px', minWidth: '110px', textTransform: 'uppercase', fontSize: '12px !important',
                }}
                variant="contained"
                color="primary"
                onMouseDown={(e) => e.preventDefault()}
                data-testid="upload-msisdn-modal__body_buttons_confirm"
              >
                Confirm
              </Button>
              <Button
                className="upload-msisdn-modal__body_cancel-button"
                sx={{
                  p: '0px 25px', backgroundColor: '#ebe3f6', border: '0px', textTransform: 'uppercase', fontSize: '12px !important',
                }}
                variant="outlined"
                onClick={handleClose}
                onMouseDown={(e) => e.preventDefault()}
                data-testid="upload-msisdn-modal__body_buttons_cancel"
              >
                Cancel
              </Button>
            </div>
          </div>
          {loader && (
            <div
              className="upload-msisdn-modal__loader"
              data-testid="upload-msisdn-modal__loader"
            >
              <Loader staticColor="#f5f1fa" size={60} />
            </div>
          )}
        </StyledCreateAllocationModal>
      </Modal>
      {/* *********************end of Upload msisdn modal ****************** */}

      {/* *(************************Error MSISDN modal******************************** */}

      <Modal open={errorModal} onClose={() => handleErrorModalClose()}>
        <StyledCreateAllocationModal
          data-testid="upload-msisdn-modal"
          className="upload-msisdn-modal"
        >

          <div className="upload-msisdn-modal__header">
            <Typography variant="h2" component="h2">
              Upload MSISDN
            </Typography>
            <StyledCloseButton
              data-testid="upload-msisdn-modal_close-button"
              type="button"
              onClick={handleErrorModalClose}
            >
              <GrClose size={21} />
            </StyledCloseButton>
          </div>
          <div className="upload-msisdn-modal__body">
            <Box sx={{
              backgroundColor: '#f5f1fa',
              display: 'flex',
              padding: '10px',
              alignItems: 'start',
              flexDirection: 'column',
            }}
            >
              <Typography variant="body1" component="p">
                We have received your request.
              </Typography>
              {/* <Typography variant="body1" component="p">
                <b>
                  {Number(msisdnList?.totalMsisdn) - Number(msisdnList?.errorMsisdn)}
                  {' '}
                  MSISDN
                </b>
                {' '}
                out of
                {' '}
                <b>
                  {msisdnList?.totalMsisdn}
                </b>
                {' '}
                were successfully uploaded.
              </Typography> */}
              {msisdnList?.errorMsisdn > 0 ? (
                <Typography variant="body1" component="p">
                  <Typography variant="body1" component="span" color="error">
                    <b>
                      {msisdnList?.errorMsisdn}
                      {' '}
                      MSISDN
                    </b>
                  </Typography>
                  {' '}
                  in the file are invalid. You can fix them, replace or delete from the file.
                </Typography>
              ) : null}

            </Box>

            {msisdnList?.errorMsisdn > 0 ? (
              <StyledTableContainer sx={{ maxHeight: '300px', marginTop: '10px' }}>
                <Table sx={{ border: '1px solid #E7E7EE' }}>
                  <TableHead>
                    <TableRow>
                      <StyledTableCell th bold>MSISDN #</StyledTableCell>
                      <StyledTableCell th bold>Error Type</StyledTableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {msisdnList?.errorResults?.map((err) => (
                      <StyledTableRow>
                        <StyledTableCell>{err?.msisdn}</StyledTableCell>
                        <StyledTableCell>{err?.issue}</StyledTableCell>
                      </StyledTableRow>

                    ))}
                  </TableBody>
                </Table>
              </StyledTableContainer>
            ) : null}

            <div className="upload-msisdn-modal__body_buttons">
              {Number(msisdnList?.errorMsisdn) > 0 && (
                <Button
                  onClick={() => downLoad()}
                  onMouseDown={(e) => e.preventDefault()}
                  data-testid="upload-msisdn-modal__body_buttons_confirm"
                  component="button"
                  variant="contained"
                  color="primary"
                  disabled={isLoading}
                  sx={{
                    p: '0px 25px', minWidth: '110px', fontSize: '12px !important',
                  }}
                >
                  Download Invalid IMSIs
                </Button>
              )}
              <Button
                className="upload-msisdn-modal__body_cancel-button"
                onClick={handleErrorModalClose}
                variant="outlined"
                onMouseDown={(e) => e.preventDefault()}
                data-testid="upload-msisdn-modal__body_buttons_cancel"
                sx={{
                  p: '0px 25px', backgroundColor: '#ebe3f6', border: '0px', fontSize: '12px !important',
                }}
              >
                OK
              </Button>
            </div>
          </div>
        </StyledCreateAllocationModal>

      </Modal>

    </Box>
  );
};

export default UploadMsisdn;
