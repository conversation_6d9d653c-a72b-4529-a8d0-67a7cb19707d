import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import axios, { AxiosResponse } from 'axios';
import {
  Box,
  MenuItem,
  Pagination,
  PaginationItem,
  Select,
  Table,
  Typography,
} from '@mui/material';
import {
  RxDoubleArrowLeft, RxDoubleArrowRight,
} from 'react-icons/rx';
import { SlArrowLeft, SlArrowRight } from 'react-icons/sl';
import { StyledPaginationContainer, StyledTableContainer } from 'features/SimManagement/componentUIparts';
import { getSimRangesPaginated } from 'features/SimManagement/api.service';
import { IIMSIRange, IIMSIRangeResponse } from 'features/SimManagement/SimManagement.models';
import Loader from 'shared/Loader';
import { toastError } from 'core/utilities/toastHelper';
import NoData from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable/NoData';
import SimIconHeader from 'assets/images/SimIconHeader';
import getCurrentThemeColors from 'core/utilities/getCurrentThemeColors';
import useAbortController from 'core/hooks/useAbortController';
import useMuiTableSearchParams from 'hooks/useMuiTableSearchParams';
import IMSIRangesTopBar from './IMSIRangesTopBar';
import IMSIRangesTableHead from './IMSIRangesTableHead';
import IMSIRangesTableBody from './IMSIRangesTableBody/IMSIRangesTableBody';

import './IMSIRanges.scss';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import { REPOSITORY, ROUTE_PERMISSION } from 'core/utilities/constants';
import { sortFieldNames } from './IMSIRangesTableHead/IMSIRangesTableHead';
import { useDebounce } from 'hooks/useDebounce';

const IMSIRanges = () => {
  const [loading, setLoading] = useState(false);
  const [imsiRanges, setImsiRanges] = useState<IIMSIRange[]>([]);
  const [ranges, setRanges] = useState<IIMSIRangeResponse>();
  const { cancelPreviousRequest, setNewController } = useAbortController();
  const location = useLocation();
  const navigate = useNavigate();
  const [sortConfig, setSortConfig] = useState<any>({ key: null, direction: null });
  const [searchValue, setSearchValue] = useState<string>('');

  const { generateParamsForUrl, getParamsFromUrl } = useMuiTableSearchParams();
  const {
    page, pageSize, field, sort, search,
  } = getParamsFromUrl();

  const fetchRanges = async (
    paramPage,
    paramPageSize,
    paramField?,
    paramSort?,
    paramSearch?,
  ) => {
    const newSearchParams = generateParamsForUrl(paramPage, paramPageSize, paramField, paramSort, paramSearch);
    const newUrl = `${location?.pathname}?tab=imsi-ranges&${newSearchParams?.toString()}`;
    cancelPreviousRequest();
    const { signal } = setNewController();
    navigate(newUrl, { replace: true });

    try {
      setLoading(true);
      let ordering;
      if (paramField) { ordering = sortFieldNames[paramField]; }
      const rangesResponse: AxiosResponse = await getSimRangesPaginated(
        paramPage,
        paramPageSize,
        ordering,
        paramSort,
        paramSearch,
        signal,
      );
      const { data } = rangesResponse;
      setRanges(data);
      setImsiRanges(data?.results);
      setLoading(false);
    } catch (error) {
      if (axios.isCancel(error)) {
        toastError('Request was canceled');
      }
      setImsiRanges([]);
      setLoading(false);
    }
  };

  const handleChange = (event) => {
    const newPageSize = event?.target?.value;
    let newPage = parseInt(page.toString(), 10);
    const totalRecords = ranges?.totalCount || 1;
    if (newPage > Math.ceil(totalRecords / newPageSize)) {
      newPage = Math.ceil(totalRecords / newPageSize);
    }
    fetchRanges(newPage, newPageSize, field, sort, search);
  };

  const handleChangePage = (event, pageValue) => {
    fetchRanges(pageValue, pageSize, field, sort, search);
  };

  const getRanges = () => {
    fetchRanges(page, pageSize, field, sort, search);
  };

  const debouncedSearch = useDebounce((term: string) => {
    fetchRanges(1, pageSize, field, sort, term);
  }, 1000);

  const handleSearch = (event) => {
    const searchTerm = event?.target?.value
    setSearchValue(searchTerm);
    debouncedSearch(searchTerm)
  };

  const handleSortRequest = (key: string) => {
    if (sortConfig.key === key) {
      if (sortConfig.direction === 'asc') {
        fetchRanges(page, pageSize, key, 'desc', search);
        setSortConfig({ key, direction: 'desc' });
      } else if (sortConfig.direction === 'desc') {
        fetchRanges(page, pageSize, search);
        setSortConfig({ key: null, direction: null });
      }
    } else {
      setSortConfig({ key, direction: 'asc' });
      fetchRanges(page, pageSize, key, 'asc', search);
    }
  };

  useEffect(() => {
    fetchRanges(page, pageSize, field, sort, search);
    if (search) {
      setSearchValue(search);
    }
  }, []);


  const pageInt = parseInt(page.toString(), 10);
  const pageSizeInt = parseInt(pageSize.toString(), 10);

  return (
    <div className="imsi-ranges" data-testid="imsi-ranges">
      <IMSIRangesTopBar fetchData={getRanges} handleSearch={handleSearch} searchValue={searchValue} />
      <StyledTableContainer
        className="scrollBar">
        <CommonAuthwrapper
          permission={[ROUTE_PERMISSION.GET_RANGES]}
          repository={[REPOSITORY.SIM_ORDERS]}
          isComponent
        >
          <Table>
            <IMSIRangesTableHead
              order={sortConfig.direction}
              orderBy={sortConfig.key}
              onRequestSort={(eventData, key) => handleSortRequest(key)}
            />
            {
              !loading && imsiRanges && imsiRanges.length > 0
              && (
                <IMSIRangesTableBody imsiRanges={imsiRanges} />
              )
            }
          </Table>
        </CommonAuthwrapper>
        {
          !loading && imsiRanges && imsiRanges.length === 0
          && (
            <Box sx={{
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            >
              <NoData
                icon={<SimIconHeader />}
                title="No IMSI Ranges available."
                description="You can create new ranges by clicking on 'Create IMSI Range' button"
                getCurrentThemeColors={getCurrentThemeColors}
              />
            </Box>
          )
        }
        {
          loading && (
            <div className="imsi-ranges__loader">
              <Loader staticColor="#f5f1fa" size={60} />
            </div>
          )
        }
      </StyledTableContainer>
      <StyledPaginationContainer>
        <Box display="flex" alignItems="center" gap={3.5}>
          <Typography>Rows per page:</Typography>
          <Select
            labelId="demo-simple-select-label"
            id="demo-simple-select"
            value={pageSize}
            onChange={handleChange}
          >
            {[10, 20, 30, 40, 50].map((size) => (
              <MenuItem key={size} value={size}>
                {size}
              </MenuItem>
            ))}
          </Select>
        </Box>
        <Typography>
          {`${(pageInt - 1) * pageSizeInt + 1} - ${Math.min(
            pageInt * pageSizeInt,
            ranges?.totalCount || 0,
          )} of ${ranges?.totalCount || 0}`}
        </Typography>
        <Pagination
          showFirstButton
          showLastButton
          count={ranges?.lastPage || 0}
          defaultPage={
            ranges?.page ? parseInt(ranges?.page.toString(), 10) : 1
          }
          renderItem={(item: any) => (
            <PaginationItem
              {...item}
              components={{
                first: () => <RxDoubleArrowLeft size={20} />,
                previous: () => <SlArrowLeft size={12} />,
                next: () => <SlArrowRight size={12} />,
                last: () => <RxDoubleArrowRight size={20} />,
              }}
            />
          )}
          siblingCount={0}
          boundaryCount={2}
          onChange={handleChangePage}
        />
      </StyledPaginationContainer>
    </div>
  );
};

export default IMSIRanges;
