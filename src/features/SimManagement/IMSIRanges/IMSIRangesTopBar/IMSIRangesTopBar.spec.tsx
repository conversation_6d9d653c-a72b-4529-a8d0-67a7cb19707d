import React from 'react';
import { screen } from '@testing-library/react';
import testRender from 'core/utilities/testUtils';
import IMSIRangesTopBar from './IMSIRangersTopBar';

describe('IMSIRangesTopBar', () => {
  test('should render top bar', () => {
    testRender(<IMSIRangesTopBar fetchData={() => null} searchValue='' handleSearch={() => null} />);

    expect(screen.getByTestId('top-bar')).toBeInTheDocument();
  });
});
