import {
  Modal, Table, TableBody, TableHead, TableRow, Typography,
} from '@mui/material';
import { StyledCreateAllocationModal } from 'features/SimManagement/IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/componentUIparts';
import React from 'react';
import { GrClose } from 'react-icons/gr';
import { StyledTableCell, StyledTableContainer, StyledTableRow } from 'features/SimManagement/componentUIparts';
import { StyledCloseButton } from './IMSIRangeSimModalTable/IMSIRangeSimModalTableUI';

const IMSIRangeTotalModal = ({
  modalState, onClose, totalRemainingCount, totalCount,
}:
  {
    modalState: boolean; onClose: () => void; totalRemainingCount: {
      provider: string;
      standard: number;
      micro: number;
      nano: number;
      esim_mff2: number;
      esim_mff2_euicc: number;
    }
    totalCount: number
  }) => (
  <Modal open={modalState} onClose={onClose}>
    <StyledCreateAllocationModal
      data-testid="upload-msisdn-modal"
      className="upload-msisdn-modal"
    >
      <div className="upload-msisdn-modal__header">
        <Typography variant="h2" component="h2">
          Available SIMs
        </Typography>
        <StyledCloseButton
          data-testid="upload-msisdn-modal_close-button"
          type="button"
          onClick={onClose}
        >
          <GrClose size={21} />
        </StyledCloseButton>
      </div>
      <StyledTableContainer sx={{ maxHeight: '300px', marginTop: '10px', paddingBottom: '0px' }}>
        <Table sx={{ border: '1px solid #E7E7EE' }}>
          <TableHead>
            <TableRow>
              <StyledTableCell th bold align="left">SIM Provider</StyledTableCell>
              <StyledTableCell th bold>SIM Type</StyledTableCell>
              <StyledTableCell th bold align="right">Quantity</StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <StyledTableRow>
              <StyledTableCell>
                {totalRemainingCount?.provider}
              </StyledTableCell>
              <StyledTableCell>
                Standard (2FF)
              </StyledTableCell>
              <StyledTableCell align="right">
                {totalRemainingCount?.standard}
              </StyledTableCell>
            </StyledTableRow>
            <StyledTableRow>
              <StyledTableCell>
                {totalRemainingCount?.provider}
              </StyledTableCell>
              <StyledTableCell>
                Micro (3FF)
              </StyledTableCell>
              <StyledTableCell align="right">
                {totalRemainingCount?.micro}
              </StyledTableCell>
            </StyledTableRow>
            <StyledTableRow>
              <StyledTableCell>
                {totalRemainingCount?.provider}
              </StyledTableCell>
              <StyledTableCell>
                Nano (4FF)
              </StyledTableCell>
              <StyledTableCell align="right">
                {totalRemainingCount?.nano}
              </StyledTableCell>
            </StyledTableRow>
            <StyledTableRow>
              <StyledTableCell>
                {totalRemainingCount?.provider}
              </StyledTableCell>
              <StyledTableCell>
                eSIM (MFF2)
              </StyledTableCell>
              <StyledTableCell align="right">
                {totalRemainingCount?.esim_mff2}
              </StyledTableCell>
            </StyledTableRow>
            <StyledTableRow>
              <StyledTableCell>
                {totalRemainingCount?.provider}
              </StyledTableCell>
              <StyledTableCell>
                eSIM (MFF2 eUICC)
              </StyledTableCell>
              <StyledTableCell align="right">
                {totalRemainingCount?.esim_mff2_euicc}
              </StyledTableCell>
            </StyledTableRow>
            <StyledTableRow>
              <StyledTableCell sx={{
                background: '#ebe3f6 !important',
              }}
              >
                <b>
                  Total
                </b>
              </StyledTableCell>
              <StyledTableCell sx={{
                background: '#ebe3f6 !important',
              }}
              >
                <b />
              </StyledTableCell>
              <StyledTableCell
                sx={{
                  background: '#ebe3f6 !important',
                }}
                align="right"
              >
                <b>
                  {totalCount}
                </b>
              </StyledTableCell>
            </StyledTableRow>
          </TableBody>
        </Table>
      </StyledTableContainer>
    </StyledCreateAllocationModal>
  </Modal>
);

export default IMSIRangeTotalModal;
