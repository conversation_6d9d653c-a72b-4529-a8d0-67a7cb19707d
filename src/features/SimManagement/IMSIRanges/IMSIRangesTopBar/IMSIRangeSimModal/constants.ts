export const TotalProviderName = "Total";

export enum DataStates {
  Loading = "Loading",
  Loaded = "Loaded",
  Error = "Error",
}

export const SIMPROFILE_OPTIONS = [
  {
    value: "DATA_ONLY",
    title: "Data Only",
  },
  {
    value: "VOICE_SMS_DATA",
    title: "Voice/SMS/Data",
  },
];

export const formFactor = [
  {
    value: "STANDARD",
    title: "Standard (2FF)",
  },
  {
    value: "MICRO",
    title: "Micro (3FF)",
  },
  {
    value: "NANO",
    title: "Nano (4FF)",
  },
  {
    value: "eSIM_MFF2",
    title: "eSIM (MFF2)",
  },
  {
    value: "eSIM_MFF2_eUICC",
    title: "eSIM (MFF2 eUICC)",
  },
];

export const displayFormFactor = {
    STANDARD: "2FF",
    MICRO: "3FF",
    NANO: "4FF",
    eSIM_MFF2: "MFF2",
    eSIM_MFF2_eUICC: "MFF2 eUICC",
    SIM: "SIM",
}

export const displayFullFormFactor = {
    STANDARD: "Standard (2FF)",
    MICRO: "Micro (3FF)",
    NANO: "Nano (4FF)",
    eSIM_MFF2: "eSIM (MFF2)",
    eSIM_MFF2_eUICC: "eSIM (MFF2 eUICC)",
    SIM: "SIM",
}

export const MSISDN_TYPE_OPTIONS = [
  {
    value: "NATIONAL",
    title: "National",
  },
  {
    value: "INTERNATIONAL",
    title: "International",
  },
];

export const MSISDN_PROVIDER = [
  {
    value: "NR",
    title: "NR",
  },
];

export default {};
