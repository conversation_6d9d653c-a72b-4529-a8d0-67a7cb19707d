import React from 'react';
import axios from 'axios';
import testRender from 'core/utilities/testUtils';
import { fireEvent, waitFor } from '@testing-library/react';
import { coreAxios } from 'core/services/HTTPService';
import permissions from 'hooks/permissions';
import IMSIRangeSimModal from './IMSIRangeSimModal';
import imsiRangesMockData from './mockIMSIRangesSimData.json';

jest.mock('core/services/HTTPService');
const mockedAxios = coreAxios as jest.Mocked<typeof axios>;

const mockAxiosSuccess = () => {
  mockedAxios.get.mockImplementationOnce(() => Promise.resolve({ data: imsiRangesMockData }));
};
const mockAxiosError = () => {
  mockedAxios.get.mockImplementationOnce(() => Promise.reject());
};

jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    access: permissions.result,
  }),
}));

describe('IMSIRangeSimModal', () => {
  it('should render component without modal', async () => {
    mockAxiosSuccess();

    const { queryByTestId, getByTestId } = testRender(<IMSIRangeSimModal updateSIMS={0} />);

    await waitFor(() => {
      const button = getByTestId('imsi-range-total-sim-toggle-button_sim-icon');
      const modal = queryByTestId('imsi-range-total-sim-modal-body');

      expect(button).toBeInTheDocument();
      expect(modal).not.toBeInTheDocument();
    });
  });

  it('should render error icon if loading is failed', async () => {
    mockAxiosError();

    const { getByTestId } = testRender(<IMSIRangeSimModal updateSIMS={0} />);

    await waitFor(() => {
      const errorIcon = getByTestId('imsi-range-total-sim-toggle-button_error-icon');
      expect(errorIcon).toBeInTheDocument();
    });
  });

  it('should load data one more time on click on error icon', async () => {
    mockAxiosError();

    const { getByTestId } = testRender(<IMSIRangeSimModal updateSIMS={0} />);

    let errorIcon;

    await waitFor(() => {
      errorIcon = getByTestId('imsi-range-total-sim-toggle-button_error-icon');
      expect(errorIcon).toBeInTheDocument();
    });

    mockAxiosSuccess();
    fireEvent.click(errorIcon);

    await waitFor(() => {
      const simIcon = getByTestId('imsi-range-total-sim-toggle-button_sim-icon');
      expect(simIcon).toBeInTheDocument();
    });
  });
});
