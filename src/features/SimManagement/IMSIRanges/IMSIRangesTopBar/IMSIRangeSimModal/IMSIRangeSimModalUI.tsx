import { Box, styled } from '@mui/material';
import getCurrentThemeColors from 'core/utilities/getCurrentThemeColors';

export const StyledSimModal = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 1010,
  backgroundColor: theme.palette.common.white,
  borderTop: `6px solid ${theme.palette.primary.main}`,
  borderRadius: theme.shape.borderRadius,
  padding: 32,
  '& .MuiFormHelperText-root': {
    color: theme.palette.error.main,
  },
}));

export const StyledTotalButton = styled('button')(({ theme }) => ({
  display: 'flex',
  background: theme.palette.common.white,
  border: 'none',
  cursor: 'pointer',
}));

export const StyledIconWrapper = styled('div')(({ theme }) => ({
  width: '40px',
  height: '40px',
  backgroundColor: getCurrentThemeColors(theme.palette.primary.main)[50],
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-evenly',
  borderRadius: '4px',
  marginRight: '10px',
}));

export const StyledSimIconWrapper = styled(StyledIconWrapper)(({ theme }) => ({
  'svg path': {
    stroke: getCurrentThemeColors(theme.palette.primary.main)[500],
  },
}));

export const StyledTotalSimData = styled('div')(() => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start',
  justifyContent: 'center',
  height: '40px',

  '.MuiTypography-body1': {
    fontWeight: 400,
    color: '#333',
    fontSize: '14px',
    lineHeight: '20px',
  },

  '.MuiTypography-body2 ': {
    fontWeight: 600,
    color: '#333',
    fontSize: '16px',
    lineHeight: '20px',
  },
}));

export const StyledSimModalHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  marginBottom: '24px',
  alignItems: 'center',

  h2: {
    fontSize: '20px',
    fontWeight: 500,
  },

  button: {
    background: theme.palette.common.white,
    border: 'none',
    cursor: 'pointer',
  },
}));
