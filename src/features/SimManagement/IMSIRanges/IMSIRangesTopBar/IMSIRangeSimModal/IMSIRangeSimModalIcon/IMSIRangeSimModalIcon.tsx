import React from 'react';
import { StyledIconWrapper, StyledSimIconWrapper } from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/IMSIRangeSimModalUI';
import { DataStates } from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants';
import Loader from 'shared/Loader';
import { Tooltip } from '@mui/material';
import { BiErrorCircle } from 'react-icons/bi';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import SimIcon from 'assets/images/SimIcon';

interface IIMSIRangeSimModalIconProps {
  dataState: DataStates
}
const IMSIRangeSimModalIcon = ({ dataState }: IIMSIRangeSimModalIconProps) => {
  if (dataState === DataStates.Loading) {
    return (
      <StyledIconWrapper data-testid="imsi-range-total-sim-toggle-button_loader" className="imsi-range-total-sim-toggle-button_loader">
        <Loader size={21} />
      </StyledIconWrapper>
    );
  }

  if (dataState === DataStates.Error) {
    return (
      <Tooltip
        title="Failed to upload Total Remaining SIMs in the Stock. Click to Reload."
        placement="top"
        arrow
      >
        <StyledIconWrapper data-testid="imsi-range-total-sim-toggle-button_error-icon" className="imsi-range-total-sim-toggle-button_error-icon">
          <BiErrorCircle size={21} color={styles.redColor500} />
        </StyledIconWrapper>
      </Tooltip>
    );
  }

  if (dataState === DataStates.Loaded) {
    return (
      <StyledSimIconWrapper data-testid="imsi-range-total-sim-toggle-button_sim-icon" className="imsi-range-total-sim-toggle-button_sim-icon">
        <SimIcon />
      </StyledSimIconWrapper>
    );
  }

  return null;
};

export default IMSIRangeSimModalIcon;
