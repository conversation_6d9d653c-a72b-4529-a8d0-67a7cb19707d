import React from 'react';
import IMSIRangeSimModalIcon from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/IMSIRangeSimModalIcon';
import testRender from 'core/utilities/testUtils';
import { DataStates } from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants';

describe('IMSIRangeSimModalIcon', () => {
  test('should render loading icon', () => {
    const { getByTestId } = testRender(<IMSIRangeSimModalIcon dataState={DataStates.Loading} />);

    const loadingIcon = getByTestId('imsi-range-total-sim-toggle-button_loader');
    expect(loadingIcon).toBeInTheDocument();
  });

  test('should render error icon', () => {
    const { getByTestId } = testRender(<IMSIRangeSimModalIcon dataState={DataStates.Error} />);

    const errorIcon = getByTestId('imsi-range-total-sim-toggle-button_error-icon');
    expect(errorIcon).toBeInTheDocument();
  });

  test('should render sim icon', () => {
    const { getByTestId } = testRender(<IMSIRangeSimModalIcon dataState={DataStates.Loaded} />);

    const simIcon = getByTestId('imsi-range-total-sim-toggle-button_sim-icon');
    expect(simIcon).toBeInTheDocument();
  });
});
