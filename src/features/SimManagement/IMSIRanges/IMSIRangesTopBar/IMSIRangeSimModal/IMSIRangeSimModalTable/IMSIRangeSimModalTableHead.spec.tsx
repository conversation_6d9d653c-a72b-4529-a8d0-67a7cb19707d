import React from 'react';
import testRender from 'core/utilities/testUtils';
import IMSIRangeSimModalTableHead from './IMSIRangeSimModalTableHead';

describe('IMSIRangeSimModalTableHead', () => {
  it('should render header columns properly', () => {
    const { getByText } = testRender(<IMSIRangeSimModalTableHead />);
    expect(getByText('SIM Provider')).toBeInTheDocument();
    expect(getByText('SIM Type')).toBeInTheDocument();
    expect(getByText('Total')).toBeInTheDocument();
  });
});
