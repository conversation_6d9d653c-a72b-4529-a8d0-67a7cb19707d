import { styled, TableContainer, useMediaQuery } from '@mui/material';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import { StyledTableRow } from 'features/SimManagement/componentUIparts';
import getCurrentThemeColors from 'core/utilities/getCurrentThemeColors';

export const StyledTableRowTotal = styled(StyledTableRow)(({ theme }) => ({
  '&:nth-child(n) .MuiTableCell-root': {
    background: getCurrentThemeColors(theme.palette.primary.main)[50],
  },
  '&:hover .MuiTableCell-root': {
    background: getCurrentThemeColors(theme.palette.primary.main)[100],
  },
}));

export const StyledTableContainer = styled(TableContainer)({
  maxHeight: '70vh',
  overflow: 'hidden',
});

export const StyledTableHead = styled('span')(() => {
  const isSmallScreen = useMediaQuery('(max-width:1600px)');

  return {
    fontSize: isSmallScreen ? 13 : 14,
    fontWeight: 700,
    color: styles.darkColor500,
  };
});

export const StyledCloseButton = styled('button')(({ theme }) => ({
  width: 40,
  height: 40,
  borderRadius: theme.shape.borderRadius,

  '&:hover': {
    background: getCurrentThemeColors(theme.palette.primary.main)[50],
  },
}));
