import React from 'react';
import { Table, TableBody } from '@mui/material';
import { StyledTableCell, StyledTableRow } from 'features/SimManagement/componentUIparts';
import IMSIRangeSimModalTableHead from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/IMSIRangeSimModalTable/IMSIRangeSimModalTableHead';
import { StyledTableRowTotal, StyledTableContainer } from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/IMSIRangeSimModalTable//IMSIRangeSimModalTableUI';
import { ICardsRemains } from 'features/SimManagement/SimManagement.models';
import { TotalProviderName } from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants';
import { getNumberWithCommas } from 'core/utilities/toMoneyFormat';

interface IIMSIRangeSimModalTableProps {
  cardRemains: ICardsRemains[]
}

const IMSIRangeSimModalTable = ({ cardRemains }: IIMSIRangeSimModalTableProps) => (
  <StyledTableContainer
    className="imsi-ranges__sim-modal__table-container"
    data-testid="imsi-ranges__sim-modal__table-container"
  >
    <Table stickyHeader>
      <IMSIRangeSimModalTableHead />
      <TableBody data-testid="imsi-ranges__sim-modal__table-body">
        {
          cardRemains.map(({
            provider, standard, micro, nano,
          }) => {
            const isTotalRow = provider === TotalProviderName;
            const Row = isTotalRow ? StyledTableRowTotal : StyledTableRow;

            return (
              <Row key={provider}>
                <StyledTableCell bold={isTotalRow}>
                  {provider}
                </StyledTableCell>
                <StyledTableCell bold={isTotalRow} align="right">
                  {getNumberWithCommas(standard)}
                </StyledTableCell>
                <StyledTableCell bold={isTotalRow} align="right">
                  {getNumberWithCommas(micro)}
                </StyledTableCell>
                <StyledTableCell bold={isTotalRow} align="right">
                  {getNumberWithCommas(nano)}
                </StyledTableCell>
                <StyledTableCell bold align="right">
                  {getNumberWithCommas(standard + micro + nano)}
                </StyledTableCell>
              </Row>
            );
          })
        }
      </TableBody>
    </Table>
  </StyledTableContainer>
);

export default IMSIRangeSimModalTable;
