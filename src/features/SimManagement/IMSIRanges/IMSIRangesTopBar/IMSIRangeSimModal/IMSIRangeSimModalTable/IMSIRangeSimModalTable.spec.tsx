import React from 'react';
import testRender from 'core/utilities/testUtils';
import IMSIRangeSimModalTable from './IMSIRangeSimModalTable';
import mockIMSIRangesSimData from '../mockIMSIRangesSimData.json';

const localRender = () => (
  testRender(<IMSIRangeSimModalTable cardRemains={mockIMSIRangesSimData} />)
);

describe('IMSIRangeSimModalTable', () => {
  it('should render component properly', () => {
    const { getByTestId } = localRender();
    expect(getByTestId('imsi-ranges__sim-modal__table-container')).toBeInTheDocument();
  });

  it('should render header', () => {
    const { getByTestId } = localRender();
    expect(getByTestId('imsi-ranges__sim-modal__table-head')).toBeInTheDocument();
  });
});
