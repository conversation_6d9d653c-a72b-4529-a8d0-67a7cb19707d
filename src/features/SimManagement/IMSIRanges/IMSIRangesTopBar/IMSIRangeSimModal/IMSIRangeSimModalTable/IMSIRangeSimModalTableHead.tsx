import React from 'react';
import { TableHead, TableRow, TableCell } from '@mui/material';
import { StyledTableHead } from './IMSIRangeSimModalTableUI';

interface ITableCellHeader {
  value: string;
  width: number;
  align: 'left' | 'right' | 'center' | 'justify' | 'inherit';
}

const header: ITableCellHeader[] = [
  { value: 'SIM Provider', width: 170, align: 'left' },
  { value: 'SIM Type', width: 215, align: 'center' },
  { value: 'Total', width: 161, align: 'right' },
];

const IMSIRangeSimModalTableHead = () => (
  <TableHead data-testid="imsi-ranges__sim-modal__table-head">
    <TableRow>
      {header.map(({ value, width, align }) => (
        <TableCell align={align} key={value} width={width}>
          <StyledTableHead>
            {value}
          </StyledTableHead>
        </TableCell>
      ))}
    </TableRow>
  </TableHead>
);

export default IMSIRangeSimModalTableHead;
