
import { Typography } from '@mui/material';
import { toastError } from 'core/utilities/toastHelper';
import { getNumberWithCommas } from 'core/utilities/toMoneyFormat';
import { getCardsRemains } from 'features/SimManagement/api.service';
import {
  DataStates,
} from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants';
import IMSIRangeSimModalIcon from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/IMSIRangeSimModalIcon';
import {
  StyledTotalButton,
  StyledTotalSimData,
} from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/IMSIRangeSimModalUI';
import React, { useEffect, useState } from 'react';
// import CommonAuthwrapper from 'core/CommonAuthWrapper';
// import { PERMISSION, REPOSITORY } from 'core/utilities/constants';
import Loader from 'shared/Loader';
import IMSIRangeTotalModal from './IMSIRangeTotalModal';

const IMSIRangeSimModal = ({ updateSIMS }) => {
  const [dataState, setDataState] = useState<DataStates>(DataStates.Loading);
  const [totalRemainingCount, setTotalRemainingCount] = useState({
    provider: 'NR',
    standard: 0,
    micro: 0,
    nano: 0,
    esim_mff2: 0,
    esim_mff2_euicc: 0
  });
  const [totalMsisdnModal, setTotalMsisdnModal] = useState(false);

  const onLoadTotalRemaining = async () => {
    try {
      setDataState(DataStates.Loading);
      const cards = await getCardsRemains();

      setTotalRemainingCount(cards?.data[0]);

      setDataState(DataStates.Loaded);
    } catch (e) {
      toastError('Failed to upload Total Remaining SIMs in the Stock.');
      setDataState(DataStates.Error);
    }
  };

  useEffect(() => {
    if (updateSIMS > 0) {
      onLoadTotalRemaining();
    }
  }, [updateSIMS])

  useEffect(() => {
    onLoadTotalRemaining();
  }, []);

  const onClickTotal = () => {
    if (dataState === DataStates.Loading) return;

    if (dataState === DataStates.Loaded) {
      setTotalMsisdnModal(true);
      return;
    }

    if (dataState === DataStates.Error) {
      onLoadTotalRemaining();
    }
  };

  const totalAmount = ['nano', 'micro', 'standard', 'esim_mff2', 'esim_mff2_euicc']
    .reduce((sum, key) => sum + (totalRemainingCount?.[key] ?? 0), 0);

  return (
    <>
      {/* <CommonAuthwrapper
        permission={[PERMISSION.GET_SIM_REMAINS]}
        repository={[REPOSITORY.SIM_ORDERS]}
      > */}
      <StyledTotalButton
        type="button"
        onClick={onClickTotal}
        data-testid="imsi-range-total-sim-toggle-button"
      >
        <IMSIRangeSimModalIcon dataState={dataState} />
        <StyledTotalSimData>
          <Typography variant="body1">
            Available SIMs
          </Typography>
          {dataState === DataStates.Loading ? <Loader size={21} />
            : (
              <Typography variant="body2">
                {getNumberWithCommas(totalAmount)}
              </Typography>
            )}
        </StyledTotalSimData>
      </StyledTotalButton>
      {/* </CommonAuthwrapper> */}
      {/* <Modal open={isOpen} onClose={toggleIsOpen}>
        <StyledSimModal data-testid="imsi-range-total-sim-modal-body">
          <StyledSimModalHeader>
            <Typography variant="h2" component="h2" color="#1c1c28">
              Available SIMs
            </Typography>
            <StyledCloseButton
              type="button"
              onClick={toggleIsOpen}
              data-testid="imsi-range-total-sim-close-button"
            >
              <GrClose size={21} color="#696969" />
            </StyledCloseButton>
          </StyledSimModalHeader>
        </StyledSimModal>
      </Modal> */}
      <IMSIRangeTotalModal
        modalState={totalMsisdnModal}
        onClose={() => setTotalMsisdnModal(false)}
        totalCount={totalAmount}
        totalRemainingCount={totalRemainingCount}
      />
    </>
  );
};

export default IMSIRangeSimModal;
