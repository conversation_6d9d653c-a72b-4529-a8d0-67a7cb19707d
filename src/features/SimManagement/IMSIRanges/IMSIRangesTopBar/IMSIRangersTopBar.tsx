import React, { useEffect, useState } from 'react';
import TopBar from 'shared/TopBar';
// import SearchInput from 'shared/SearchInput'; // TODO: Return after search implementation
import { Box, Modal, Typography } from '@mui/material';
import ActionButtonWithTooltip from '@nv2/nv2-pkg-js-shared-components/lib/ActionButtonWithTooltip';
import { GetAuthorization } from 'PrivateRotes';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import { PERMISSION, REPOSITORY } from 'core/utilities/constants';
import { getNumberWithCommas } from 'core/utilities/toMoneyFormat';
import { IFreeMsisdn } from 'features/SimManagement/IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/models';
import MSISDNTotalModal from 'features/SimManagement/MSISDN/MSISDNTotalModal';
import { totalMsisdn } from 'features/SimManagement/api.service';
import { LuFileSpreadsheet } from 'react-icons/lu';
import Loader from 'shared/Loader';
import CreateIMSIRangesModal from './CreateIMSIRangesModal/CreateIMSIRangesModal';
import IMSIRangeSimModal from './IMSIRangeSimModal/IMSIRangeSimModal';
import IMSIRangeSimModalIcon from './IMSIRangeSimModal/IMSIRangeSimModalIcon';
import { StyledTotalButton, StyledTotalSimData } from './IMSIRangeSimModal/IMSIRangeSimModalUI';
import { DataStates } from './IMSIRangeSimModal/constants';
import './IMSIRangesTopBar.scss';
import SearchInput from 'shared/SearchInput';

interface IMSIRangesTopBarProps {
  fetchData: () => void
  handleSearch: (value: string) => void
  searchValue: string
}

const IMSIRangesTopBar = ({ fetchData, searchValue, handleSearch }: IMSIRangesTopBarProps) => {
  const baseUrl = window.location.origin;
  const [totalMsisdnModal, setTotalMsisdnModal] = useState(false);
  const [dataState, setDataState] = useState<DataStates>(DataStates.Loaded);
  const [totalRemainingCountState, setTotalRemainingCountState] = useState(false);
  const [totalRemainingCount, setTotalRemainingCount] = useState<IFreeMsisdn>({
    totalCount: 0,
    national: 0,
    international: 0,
  });
  const [updateSIMS, setUpdateSIMS] = useState(0)
  const [image, setImage] = useState<File>();
  const [open, setOpen] = React.useState(false);
  const checkMsisdnCount = GetAuthorization(
    [PERMISSION?.FREE_MSISDN_COUNT], [REPOSITORY?.SIM_MANAGEMENT]);

  const fetchTotalRemainingCount = async () => {
    if (!checkMsisdnCount) return;
    try {
      setDataState(DataStates.Loading);
      setTotalRemainingCountState(false);
      const { data } = await totalMsisdn();
      setTotalRemainingCount(data);
      setDataState(DataStates.Loaded);
      setTotalRemainingCountState(true);
    } catch (error: any) {
      setTotalRemainingCountState(false);
      setDataState(DataStates.Error);
      console.log('error: ', error);
    }
  };

  const handleOpen = () => {
    setImage(undefined);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    fetchTotalRemainingCount();
  }, []);

  return (
    <TopBar className="imsi-ranges__top-bar" navigateTo={baseUrl}>
      <Box
        display="flex"
        justifyContent="space-between"
        sx={{ width: '100% !important' }}
      >
        <Box display="flex" gap={8}>
          {/* <SearchInput placeholder="Search" /> TODO: Return after search implementation */}
          <div>
            <SearchInput
              placeholder="Search"
              value={searchValue}
              onChange={(e: any) => handleSearch(e)}
            />
          </div>
          <CommonAuthwrapper
            permission={[PERMISSION.GET_SIM_REMAINS]}
            repository={[REPOSITORY.SIM_ORDERS]}
          >
            <IMSIRangeSimModal updateSIMS={updateSIMS} />
          </CommonAuthwrapper>

          <CommonAuthwrapper
            permission={[PERMISSION.FREE_MSISDN_COUNT]}
            repository={[REPOSITORY.SIM_MANAGEMENT]}
          >
            <StyledTotalButton
              type="button"
              onClick={() => setTotalMsisdnModal(true)}
              disabled={dataState === DataStates.Error}
              data-testid="imsi-range-total-sim-toggle-button"
              sx={{
                // padding: '0px 40px',
                '.common-loader': {
                  margin: 'unset',
                },
                ':disabled': {
                  cursor: 'unset',
                },
                '.imsi-range-total-sim-toggle-button_sim-icon': {
                  backgroundColor: '#FEEEEC',
                },
                '.imsi-range-total-sim-toggle-button_sim-icon svg path': {
                  stroke: '#F7735D',
                },
              }}
            >
              <IMSIRangeSimModalIcon dataState={dataState} />
              <StyledTotalSimData>
                <Typography variant="body1">
                  Available MSISDNs
                </Typography>
                {!totalRemainingCountState ? <Loader size={21} /> : (
                  <Typography variant="body2">
                    {getNumberWithCommas(totalRemainingCount?.totalCount)}
                  </Typography>
                )}
              </StyledTotalSimData>
            </StyledTotalButton>
          </CommonAuthwrapper>

          <MSISDNTotalModal
            modalState={totalMsisdnModal}
            onClose={() => setTotalMsisdnModal(false)}
            totalRemainingCount={totalRemainingCount}
          />
        </Box>
        <Box>
          {/* <SearchInput placeholder="Search" /> TODO: Return after search implementation */}
          <CommonAuthwrapper
            permission={[PERMISSION.CREATE_ALLOCATION]}
            repository={[REPOSITORY.SIM_ORDERS]}
          >
            <ActionButtonWithTooltip
              title="Create IMSI Range"
              data-testid="imsi-allocations__top-bar_create-button"
              className="imsi-allocations__top-bar_create-button"
              action={handleOpen}
              icon={<LuFileSpreadsheet />}
            />
          </CommonAuthwrapper>
        </Box>
      </Box>
      <Modal open={open} onClose={handleClose}>
        <CreateIMSIRangesModal
          fetchData={fetchData}
          handleClose={handleClose}
          setImage={setImage}
          image={image}
          fetchTotalRemainingCount={fetchTotalRemainingCount}
          setUpdateSIMS={() => setUpdateSIMS((count) => count + 1)}
          totalRemainingCount={totalRemainingCount}
        />
      </Modal>
    </TopBar>
  );
};

export default IMSIRangesTopBar;
