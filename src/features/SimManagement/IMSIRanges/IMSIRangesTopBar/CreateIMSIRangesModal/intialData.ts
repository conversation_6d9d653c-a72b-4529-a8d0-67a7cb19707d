import { ICreateIMSIRanges } from "features/SimManagement/IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/models";

export const initialValues: ICreateIMSIRanges = {
  title: "",
  formFactor: "",
  file: null,
};

export const initialErrors = {
  title: "",
  formFactor: "",
  file: null,
};

export enum SimFactor {
  STANDARD = "STANDARD",
  MICRO = "MICRO",
  NANO = "NANO",
  MFF2_eUICC = "MFF2_eUICC",
  MFF2 = "MFF2",
}
