import React from 'react';
import {
  render, screen, fireEvent,
} from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import CreateIMSIRangesModal from './CreateIMSIRangesModal';

jest.mock('features/SimManagement/api.service', () => ({
  createImsiRanges: jest.fn(),
}));

jest.mock('core/utilities/toastHelper', () => ({
  toastError: jest.fn(),
  toastSuccess: jest.fn(),
}));

describe('CreateIMSIRangesModal', () => {
  const mockHandleClose = jest.fn();
  const mockFetchData = jest.fn();
  const mockSetImage = jest.fn();
  const mockSetcount = jest.fn();
  const mockSetupdate = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const renderComponent = () => render(
    <MemoryRouter>
      <CreateIMSIRangesModal
        handleClose={mockHandleClose}
        setImage={mockSetImage}
        image={undefined}
        fetchData={mockFetchData}
        totalRemainingCount={{
          totalCount: 0,
          national: 0,
          international: 0,
        }}
        fetchTotalRemainingCount={mockSetcount}
        setUpdateSIMS={mockSetupdate}
      />
    </MemoryRouter>,
  );

  test('should render modal correctly', () => {
    renderComponent();
    expect(screen.getByText('Create IMSI Range')).toBeInTheDocument();
  });

  test('handles close button click', () => {
    renderComponent();

    const closeButton = screen.getByTestId('create-imsi-range-modal_close-button');
    fireEvent.click(closeButton);

    expect(mockHandleClose).toHaveBeenCalled();
  });

  test('should close modal on cancel button click', () => {
    renderComponent();
    fireEvent.click(screen.getByTestId('create-imsi-range-modal__body_buttons_cancel'));
    expect(mockHandleClose).toHaveBeenCalled();
  });

  test('handles cancel button', () => {
    renderComponent();

    const cancelButton = screen.getByTestId('create-imsi-range-modal__body_buttons_cancel');
    fireEvent.click(cancelButton);

    expect(mockHandleClose).toHaveBeenCalled();
  });
});
