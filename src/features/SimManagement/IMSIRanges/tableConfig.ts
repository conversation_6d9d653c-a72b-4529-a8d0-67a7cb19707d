import { IHeader } from "../SimManagement.models";

export enum Alignment {
  Left = "left",
  Right = "right",
}

const headers: Array<IHeader> = [
  {
    id: "title",
    name: "Title",
    width: 140,
    sort: true,
  },
  {
    id: "simProvider",
    name: "SIM Provider",
    width: 165,
    sort: false,
  },
  {
    id: "simType",
    name: "SIM Type",
    width: 140,
    sort: true,
  },
  {
    id: "quantity",
    name: "Quantity",
    width: 140,
    align: Alignment.Left,
    sort: true,
  },
  {
    id: "imsiFirst",
    name: "IMSI First",
    width: 190,
    align: Alignment.Left,
    sort: true,
  },
  {
    id: "imsiLast",
    name: "IMSI Last",
    width: 190,
    align: Alignment.Left,
    sort: true,
  },
  {
    id: "remaining",
    name: "Remaining",
    width: 140,
    align: Alignment.Left,
    sort: true,
  },
  {
    id: "createdAt",
    name: "Uploaded/Created",
    width: 260,
    sort: true,
  },
  {
    id: "uploadedBy",
    name: "Uploaded by",
    width: 195,
    sort: true,
  },
  {
    id: "actions",
    name: "Actions",
    width: 140,
    sort: false,
  },
];

export default headers;
