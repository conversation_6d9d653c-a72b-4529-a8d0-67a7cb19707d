import React from 'react';
import { screen } from '@testing-library/react';
import testRender from 'core/utilities/testUtils';
import IMSIRangesTableHead from './IMSIRangesTableHead';

describe('IMSIRangesTableHead', () => {
  test('should render table head', () => {
    testRender(
      <table>
        <IMSIRangesTableHead order='' orderBy=''  onRequestSort={()=>null} />
      </table>,
    );

    expect(screen.getByTestId('imsi-ranges__table-head')).toBeInTheDocument();
  });
});
export {}