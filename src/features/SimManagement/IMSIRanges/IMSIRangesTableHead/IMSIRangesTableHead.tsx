import { Box, TableHead, TableRow, TableSortLabel } from '@mui/material';
import React, { FC } from 'react';
import { StyledTableCell } from 'features/SimManagement/componentUIparts';
import headers from 'features/SimManagement/IMSIRanges/tableConfig';
import { visuallyHidden } from '@mui/utils';


interface IMSIRangesTableHeadProps {
  order: string;
  orderBy: string;
  onRequestSort: (event: React.MouseEvent<unknown>, property: string) => void;
}

interface Data {
  title: string;
  simProvider: string;
  simType: string;
  quantity: string;
  imsiFirst: string;
  imsiLast: string;
  remaining: string;
  createdAt: string;
  uploadedBy: string;
  actions: string;
}
export const sortFieldNames: Data = {
  title: 'title',
  simProvider: 'sim_provider',
  simType: 'form_factor',
  quantity: 'quantity',
  imsiFirst: 'imsi_first',
  imsiLast: 'imsi_last',
  remaining: 'remaining',
  createdAt: 'created_at',
  uploadedBy: 'created_by',
  actions: 'action',
};

const IMSIRangesTableHead: FC<IMSIRangesTableHeadProps> = ({
  order,
  orderBy,
  onRequestSort,
}) => {
  const cssProperties: any = visuallyHidden;
  const createSortHandler = (property: string) => (event: React.MouseEvent<unknown>) => {
    onRequestSort(event, property);
  };
  const orderDirection = order as 'desc' | 'asc';


  return (
    <TableHead data-testid="imsi-ranges__table-head">
      <TableRow>
        {headers.map((header) => (
          <StyledTableCell
            key={header?.id}
            sx={{
              minWidth: header?.width,
              cursor: header?.id ? 'pointer' : 'default',
            }}
            align={header?.align ?? 'left'}
            sticky={!!header?.sticky}
            bold
            th
            onClick={() => header?.id && createSortHandler(header?.id)}
          >
            {header.sort ? (
              <TableSortLabel
                active={orderBy === header.id}
                direction={orderBy === header.id ? orderDirection : 'asc'}
                onClick={createSortHandler(header.id)}
              >
                {header.name}
                {orderBy === header.id && (
                  <Box component="span" sx={cssProperties}>
                    {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                  </Box>
                )}
              </TableSortLabel>
            ) : (
              header.name
            )}
          </StyledTableCell>
        ))}
      </TableRow>
    </TableHead>
  );
};

export default IMSIRangesTableHead;
