import { IIMSIRange } from '../SimManagement.models';

export const imsiRangesMockDataItem = {
  id: 1,
  title: 'Reference',
  provider: 'NR',
  formFactor: 'MICRO',
  quantity: 200,
  imsiFirst: '956473821000000',
  imsiLast: '956473821000200',
  remaining: 150,
  createdAt: '2023-01-10T05:18:01',
  createdBy: 'Brooklyn Simmons',
};

export const imsiRangesMockData: IIMSIRange[] = Array.from(new Array(10)).map((_, index) => ({
  id: index,
  title: `Reference ${index}`,
  provider: `NR ${index}`,
  formFactor: `MICRO ${index}`,
  quantity: 200,
  imsiFirst: `956473821000000 ${index}`,
  imsiLast: `956473821000200 ${index}`,
  remaining: 150,
  createdBy: `Brooklyn Simmons ${index}`,
  createdAt: `2023-01-${String(index + 1 < 12 ? index + 1 : 12).padStart(2, '0')}T05:18:01`,
}));

export const mockAllocation = [
  {
    provider: 'NR', standard: 9888, micro: 5, nano: 2635,
  },
];
export default imsiRangesMockData;
