import React, { <PERSON> } from 'react';
import { TableBody } from '@mui/material';

import { IIMSIRange } from 'features/SimManagement/SimManagement.models';
import Row from './Row';

interface IIMSIRangesTableBodyProps {
  imsiRanges: IIMSIRange[];
}

const IMSIRangesTableBody: FC<IIMSIRangesTableBodyProps> = ({ imsiRanges }) => (
  <TableBody data-testid="imsi-ranges__table-body">
    {
      imsiRanges.map((imsiRange) => <Row key={imsiRange.id} imsiRange={imsiRange} />)
    }
  </TableBody>
);

export default IMSIRangesTableBody;
