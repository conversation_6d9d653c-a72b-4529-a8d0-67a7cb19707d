import React from 'react';
import { screen } from '@testing-library/react';
import testRender from 'core/utilities/testUtils';
import { imsiRangesMockDataItem } from 'features/SimManagement/IMSIRanges/IMSIRangesMockData';
import Row from './Row';

const defaultMockProps = {
  imsiRange: imsiRangesMockDataItem,
};

describe('Row', () => {
  const mockProps = {
    ...defaultMockProps,
  };

  test('should render provider', () => {
    testRender(<Row {...mockProps} />);

    expect(screen.getByText('NR')).toBeInTheDocument();
  });

  test('should render type', () => {
    testRender(<Row {...mockProps} />);

    expect(screen.getByText('MICRO')).toBeInTheDocument();
  });

  test('should render quantity', () => {
    testRender(<Row {...mockProps} />);

    expect(screen.getByText(200)).toBeInTheDocument();
  });

  test('should render imsiFirst', () => {
    testRender(<Row {...mockProps} />);

    expect(screen.getByText('956473821000000')).toBeInTheDocument();
  });

  test('should render imsiLast', () => {
    testRender(<Row {...mockProps} />);

    expect(screen.getByText('956473821000200')).toBeInTheDocument();
  });

  test('should render remaining', () => {
    testRender(<Row {...mockProps} />);

    expect(screen.getByText(150)).toBeInTheDocument();
  });

  test('should render Uploaded/Created', () => {
    testRender(<Row {...mockProps} />);

    expect(screen.getByText('10-01-2023 05:18:01')).toBeInTheDocument();
  });

  test('should render Uploaded by', () => {
    testRender(<Row {...mockProps} />);

    expect(screen.getByText('Brooklyn Simmons')).toBeInTheDocument();
  });
});
