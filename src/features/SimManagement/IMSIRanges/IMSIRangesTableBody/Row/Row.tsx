import React, { FC } from 'react';
import { StyledTableCell, StyledTableRow } from 'features/SimManagement/componentUIparts';
import { IIMSIRange } from 'features/SimManagement/SimManagement.models';
import { formatDateWithHours } from 'core/utilities/formatDate';
import { getNumberWithCommas } from 'core/utilities/toMoneyFormat';
import { Tooltip } from '@mui/material';

interface IRowProps {
  imsiRange: IIMSIRange;
}

const Row: FC<IRowProps> = ({ imsiRange }) => {
  const {
    id,
    title,
    provider,
    formFactor,
    quantity,
    imsiFirst,
    imsiLast,
    remaining,
    createdAt,
    createdBy,
  } = imsiRange;

  return (
    <StyledTableRow key={id}>
      <StyledTableCell>
        <Tooltip title={title} arrow>
          <span>
            {title.length > 30 ? `${title.slice(0, 30)}...` : title}
          </span>
        </Tooltip>
      </StyledTableCell>
      <StyledTableCell>{provider}</StyledTableCell>
      <StyledTableCell>{formFactor.replaceAll('_', ' ')}</StyledTableCell>
      <StyledTableCell>{getNumberWithCommas(quantity)}</StyledTableCell>
      <StyledTableCell>{imsiFirst}</StyledTableCell>
      <StyledTableCell>{imsiLast}</StyledTableCell>
      <StyledTableCell>{getNumberWithCommas(remaining)}</StyledTableCell>
      <StyledTableCell>{formatDateWithHours(createdAt)}</StyledTableCell>
      <StyledTableCell>{createdBy}</StyledTableCell>
      <StyledTableCell />
    </StyledTableRow>
  );
};

export default Row;
