import React from 'react';
import { screen } from '@testing-library/react';
import testRender from 'core/utilities/testUtils';
import IMSIRangesTableBody from './IMSIRangesTableBody';
import imsiRangesMockData from '../IMSIRangesMockData';

const defaultMockProps = {
  imsiRanges: imsiRangesMockData,
};

describe('IMSIRangesTableBody', () => {
  const mockProps = {
    ...defaultMockProps,
  };

  test('should render table', () => {
    testRender(<IMSIRangesTableBody {...mockProps} />);

    expect(screen.getByTestId('imsi-ranges__table-body')).toBeInTheDocument();
  });
});
