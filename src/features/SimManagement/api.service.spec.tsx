import axios from 'axios';
import { coreAxios } from 'core/services/HTTPService';
import {
  createSimAllocations,
  getAccounts,
  getRatePlansByAccount,
  getRatePlansById,
  getSimAllocations,
  getSimRanges,
  getCards,
  getCardsExport,
} from './api.service';
import imsiRangesMockData from './IMSIRanges/IMSIRangesMockData';
import imsiAllocationsMockData, {
  accountsMockData,
  mockRatePlansListData, simCardsUserMockData,
} from './IMSIAllocations/IMSIAllocationsMockData';

jest.mock('core/services/HTTPService');
const mockedAxios = coreAxios as jest.Mocked<typeof axios>;

describe('getSimRanges', () => {
  test('should successfully get sim ranges', async () => {
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve(imsiRangesMockData));

    await expect(getSimRanges()).resolves.toEqual(imsiRangesMockData);
  });

  test('should get error from an API', async () => {
    const errorMessage = 'test error';

    mockedAxios.get.mockImplementationOnce(() => Promise.reject(new Error(errorMessage)));

    await expect(getSimRanges()).rejects.toThrow(errorMessage);
  });
});

describe('getSimAllocations', () => {
  test('should successfully get sim allocations', async () => {
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve(imsiAllocationsMockData));

    await expect(getSimAllocations()).resolves.toEqual(imsiAllocationsMockData);
  });

  test('should get error from an API', async () => {
    const errorMessage = 'test error';

    mockedAxios.get.mockImplementationOnce(() => Promise.reject(new Error(errorMessage)));

    await expect(getSimAllocations()).rejects.toThrow(errorMessage);
  });
});

describe('createSimAllocations', () => {
  const allocationsBody = {
    title: 'Reference',
    accountId: 1,
    rangeId: 1,
    ratePlanId: 1,
    quantity: 380,
  };

  test('should successfully create sim allocations', async () => {
    mockedAxios.post.mockImplementationOnce(() => Promise.resolve(imsiAllocationsMockData[0]));

    await expect(createSimAllocations(allocationsBody))
      .resolves.toEqual(imsiAllocationsMockData[0]);
  });

  test('should get error from an API', async () => {
    const errorMessage = 'test error';

    mockedAxios.post.mockImplementationOnce(() => Promise.reject(new Error(errorMessage)));

    await expect(createSimAllocations(allocationsBody)).rejects.toThrow(errorMessage);
  });
});

describe('getRatePlansByAccount', () => {
  test('should get successfully rate plans', async () => {
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve(mockRatePlansListData));

    await expect(getRatePlansByAccount()).resolves.toEqual(mockRatePlansListData);
  });

  test('should get error from an API', async () => {
    const errorMessage = 'test error';

    mockedAxios.get.mockImplementationOnce(() => Promise.reject(new Error(errorMessage)));

    await expect(getRatePlansByAccount()).rejects.toThrow(errorMessage);
  });
});

describe('getRatePlansById', () => {
  test('should get successfully rate plans by account id', async () => {
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve(mockRatePlansListData[0]));

    await expect(getRatePlansById('1')).resolves.toEqual(mockRatePlansListData[0]);
  });

  test('should get error from an API', async () => {
    const errorMessage = 'test error';

    mockedAxios.get.mockImplementationOnce(() => Promise.reject(new Error(errorMessage)));

    await expect(getRatePlansById('1')).rejects.toThrow(errorMessage);
  });
});

describe('getAccounts', () => {
  test('should get successfully accounts', async () => {
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve(accountsMockData));

    await expect(getAccounts()).resolves.toEqual(accountsMockData);
  });

  test('should get error from an API', async () => {
    const errorMessage = 'test error';

    mockedAxios.get.mockImplementationOnce(() => Promise.reject(new Error(errorMessage)));

    await expect(getAccounts()).rejects.toThrow(errorMessage);
  });
});
describe('getCards', () => {
  test('should get cards successfully', async () => {
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve({ data: simCardsUserMockData }));

    await expect(getCards(1,1, 10, '', '')).resolves.toEqual({ data: simCardsUserMockData });
  });

  test('should get cards for 1 page and 10 items', async () => {
    jest.clearAllMocks();
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve({ data: simCardsUserMockData }));

    getCards(1,1, 10, '', '');

    await expect(mockedAxios.get).toHaveBeenCalledWith('glass/sim/cards/usage?account_id=1&page=1&page_size=10');
  });

  test('should get cards for 1 page and 10 items with asc ordering', async () => {
    jest.clearAllMocks();
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve({ data: simCardsUserMockData }));

    getCards(1,1, 10, 'id', '');

    await expect(mockedAxios.get).toHaveBeenCalledWith('glass/sim/cards/usage?account_id=1&page=1&page_size=10&ordering=id');
  });

  test('should get cards for 1 page and 10 items with desc ordering', async () => {
    jest.clearAllMocks();
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve({ data: simCardsUserMockData }));

    getCards(1,1, 10, '-id', '');

    await expect(mockedAxios.get).toHaveBeenCalledWith('glass/sim/cards/usage?account_id=1&page=1&page_size=10&ordering=-id');
  });

  test('should get cards for 1 page and 10 items with search', async () => {
    jest.clearAllMocks();
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve({ data: simCardsUserMockData }));

    getCards(1,1, 10, '', 'value');

    await expect(mockedAxios.get).toHaveBeenCalledWith('glass/sim/cards/usage?account_id=1&page=1&page_size=10&search=value');
  });

  test('should get cards for 1 page and 10 items with search and ordering', async () => {
    jest.clearAllMocks();
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve({ data: simCardsUserMockData }));

    getCards(1,1, 10, '-id', 'value');

    await expect(mockedAxios.get).toHaveBeenCalledWith('glass/sim/cards/usage?account_id=1&page=1&page_size=10&ordering=-id&search=value');
  });

  test('should get error from an API', async () => {
    const errorMessage = 'test error';

    mockedAxios.get.mockImplementationOnce(() => Promise.reject(new Error(errorMessage)));

    await expect(getCards(1,1, 10, '', '')).rejects.toThrow(errorMessage);
  });
});

describe('getCardsExport', () => {
  test('should get cards successfully as csv', async () => {
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve({ data: 'csvFile.csv' }));

    await expect(getCardsExport('', '')).resolves.toEqual({ data: 'csvFile.csv' });
  });

  test('should get cards as csv file with asc ordering', async () => {
    jest.clearAllMocks();
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve({ data: 'csvFile.csv' }));

    getCardsExport(85, 'id', '');

    await expect(mockedAxios.get).toHaveBeenCalledWith('glass/sim/cards/usage/export?account_id=85&ordering=id');
  });

  test('should get cards as csv file with desc ordering', async () => {
    jest.clearAllMocks();
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve({ data: 'csvFile.csv' }));

    getCardsExport(85, '-id', '');

    await expect(mockedAxios.get).toHaveBeenCalledWith('glass/sim/cards/usage/export?account_id=85&ordering=-id');
  });

  test('should get cards as csv file with search and ordering', async () => {
    jest.clearAllMocks();
    mockedAxios.get.mockImplementationOnce(() => Promise.resolve({ data: 'csvFile.csv' }));

    getCardsExport(85, '-id', 'value');

    await expect(mockedAxios.get).toHaveBeenCalledWith('glass/sim/cards/usage/export?account_id=85&ordering=-id&search=value');
  });

  test('should get error from an API', async () => {
    const errorMessage = 'test error';

    mockedAxios.get.mockImplementationOnce(() => Promise.reject(new Error(errorMessage)));

    await expect(getCardsExport(85, '', '')).rejects.toThrow(errorMessage);
  });
});
