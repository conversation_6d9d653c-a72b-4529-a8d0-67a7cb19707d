export interface IPagination<Result> {
  results: Result
  lastPage: number
  page: number
  pageSize: number
  totalCount: number
}

export interface ITotalRow{
  totalUsage: number,
  totalActiveSims: number,
  totalReadyActivationSims: number,
  totalDeactivatedSims: number,
  totalPendingSims: number,
  totalSims: number,
  totalEEUsageData: number
}

export interface IPaginationTotal<Result, TotalRow> {
  results: Result
  summary: TotalRow
  lastPage: number
  page: number
  pageSize: number
  totalCount: number
}
export type TGetCurrentThemeColors = (color: string) => ({ [index: number]: string })
