import { useAppContext } from 'AppContextProvider';
import { parsePermissions } from 'core/utilities/parsing';
import IPermission from 'model/IPermission';
import React, { FC, ReactElement } from 'react';
import Forbidden from '@nv2/nv2-pkg-js-shared-components/lib/Forbidden';
import getCurrentThemeColors from 'core/utilities/getCurrentThemeColors';
// import Loader from 'shared/Loader';

export const GetAuthorization = (rights: string[], repoName: string[]) => {
  const { access } = useAppContext();
  let allow = false;
  const finalValue: Array<IPermission> = parsePermissions(access, repoName) || [];
  if (finalValue && finalValue?.length > 0) {
    const finalValueNames = finalValue.map((t) => t.name);

    // Check if 'rights' is in the 'permission' array
    allow = rights.every((name) => finalValueNames.includes(name));
  }
  // Only update 'isAuthorise' if 'allow' changes
  return allow;
};

interface IPrivateRotesProps {
  permission: string[],
  repoName: string[],
  children: ReactElement,
}
const PrivateRotes: FC<IPrivateRotesProps> = (
  { permission, repoName, children }): ReactElement => {
  const isAllow = GetAuthorization(permission, repoName);
  if ((permission && permission?.length !== 0) && (repoName && repoName?.length !== 0)) {
    const isAuthorise = isAllow || !permission;
    if (!isAuthorise) {
      return <Forbidden getCurrentThemeColors={getCurrentThemeColors} />;
    }
  }

  return children;
};

export default PrivateRotes;
