import React from 'react';
import App from 'App';
import testRender from 'core/utilities/testUtils';
import { screen } from '@testing-library/react';
import { IUser, organizationTypes } from 'user.model';

const mockUser: IUser = {
  id: 'dec3c',
  email: '<EMAIL>',
  firstName: 'name',
  lastName: 'last name',
  organization: {
    id: 12,
    name: 'oName',
    parent_id: '12dd3',
    type: organizationTypes.DISTRIBUTOR,
  },
};
describe('App', () => {
  test('should App render correct', () => {
    testRender(<App user={mockUser} isLoading={false} />);
    expect(screen.getByTestId('app')).toBeInTheDocument();
  });
});

export {};
