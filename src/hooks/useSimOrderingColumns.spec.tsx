import React from 'react';
import { renderHook } from '@testing-library/react';
import dayjs from 'dayjs';
import useBulkOperationsTrackerColumns from './useSimOrderingColumns';
import { IUser, organizationTypes } from 'user.model';

const mockDistributorUser: IUser = {
  id: 'dec3c',
  email: '<EMAIL>',
  firstName: 'name',
  lastName: 'last name',
  organization: {
    id: 12,
    name: 'oName',
    parent_id: '12dd3',
    type: organizationTypes.DISTRIBUTOR,
  },
};

// Mock external dependencies
jest.mock('shared/Status/StatusText', () => {
  return function MockStatusText({ status, message }) {
    return <div data-testid="status-text">{status} - {message}</div>;
  };
});

jest.mock('features/SimManagement/SimOrdering/SimOrderingAction', () => {
  return jest.fn((row, callback) => (
    <div data-testid="sim-ordering-action" onClick={() => callback(row)}>
      Action
    </div>
  ));
});

jest.mock('shared/LazyLoad/Image', () => {
  return function MockImage({ src, alt, style }) {
    return <img src={src} alt={alt} style={style} data-testid="lazy-image" />;
  };
});

jest.mock('core/utilities/constants', () => ({
  simOrderStatusColorMap: jest.fn()
}));

describe('useBulkOperationsTrackerColumns', () => {
  const mockOnSimOrderingActionClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return columns array with correct structure', () => {
    const { result } = renderHook(() => 
      useBulkOperationsTrackerColumns({ onSimOrderingActionClick: mockOnSimOrderingActionClick, user: mockDistributorUser })
    );

    expect(result.current.columns).toHaveLength(9);
    expect(result.current.columns).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          headerName: expect.any(String),
          field: expect.any(String),
          width: expect.any(Number)
        })
      ])
    );
  });

  it('should configure Account column correctly', () => {
    const { result } = renderHook(() => 
      useBulkOperationsTrackerColumns({ onSimOrderingActionClick: mockOnSimOrderingActionClick, user: mockDistributorUser })
    );

    const accountColumn = result.current.columns[0];
    expect(accountColumn).toEqual({
      headerName: "Account",
      field: "customerAccountName",
      width: 220,
      sortable: true,
      renderCell: expect.any(Function)
    });
  });

  it('should configure Order ID column correctly', () => {
    const { result } = renderHook(() => 
      useBulkOperationsTrackerColumns({ onSimOrderingActionClick: mockOnSimOrderingActionClick, user: mockDistributorUser })
    );

    const orderIdColumn = result.current.columns[1];
    expect(orderIdColumn).toEqual({
      headerName: "Order ID",
      field: "orderId",
      width: 180,
      sortable: true
    });
  });

  it('should configure Order Date & Time column with valueGetter', () => {
    const { result } = renderHook(() => 
      useBulkOperationsTrackerColumns({ onSimOrderingActionClick: mockOnSimOrderingActionClick, user: mockDistributorUser })
    );

    const orderDateColumn = result.current.columns[3];
    expect(orderDateColumn.headerName).toBe("Order Date & Time");
    expect(orderDateColumn.field).toBe("orderDate");
    expect(orderDateColumn.valueGetter).toBeInstanceOf(Function);
  });

  it('should format date correctly in Order Date & Time valueGetter', () => {
    const { result } = renderHook(() => 
      useBulkOperationsTrackerColumns({ onSimOrderingActionClick: mockOnSimOrderingActionClick, user: mockDistributorUser })
    );

    const orderDateColumn = result.current.columns[3];
    const testDate = '2023-12-25T10:30:45Z';
    expect(orderDateColumn.valueGetter).toBeDefined();
    const formattedDate = orderDateColumn.valueGetter?.({ value: testDate });
    
    expect(formattedDate).toBe(dayjs(testDate).format("DD-MM-YYYY hh:mm:ss"));
  });
  it('should handle null/undefined date in Order Date & Time valueGetter', () => {
  const { result } = renderHook(() => 
    useBulkOperationsTrackerColumns({ onSimOrderingActionClick: mockOnSimOrderingActionClick, user: mockDistributorUser })
  );

  const orderDateColumn = result.current.columns[3];
  
  expect(orderDateColumn.valueGetter!({ value: null })).toBe("");
  expect(orderDateColumn.valueGetter!({ value: undefined })).toBe("");
  expect(orderDateColumn.valueGetter!({ value: "" })).toBe("");
});

  it('should configure remaining columns correctly', () => {
    const { result } = renderHook(() => 
      useBulkOperationsTrackerColumns({ onSimOrderingActionClick: mockOnSimOrderingActionClick, user: mockDistributorUser })
    );

    const columns = result.current.columns;
    
    // Ordered By column
    expect(columns[4]).toEqual({
      headerName: "Ordered By",
      field: "personPlacingOrder",
      width: 150,
      sortable: true
    });

    // Email column
    expect(columns[5]).toEqual({
      headerName: "Email",
      field: "customerEmail",
      width: 180,
      sortable: true
    });

    // Phone column
    expect(columns[6]).toEqual({
      headerName: "Phone",
      field: "customerPhone",
      width: 140,
      sortable: true
    });
  });

  it('should configure Order Status column with renderCell', () => {
    const { result } = renderHook(() => 
      useBulkOperationsTrackerColumns({ onSimOrderingActionClick: mockOnSimOrderingActionClick, user: mockDistributorUser })
    );

    const orderStatusColumn = result.current.columns[7];
    expect(orderStatusColumn.headerName).toBe("Order Status");
    expect(orderStatusColumn.field).toBe("status");
    expect(orderStatusColumn.renderCell).toBeInstanceOf(Function);
  });

  it('should configure Action column correctly', () => {
    const { result } = renderHook(() => 
      useBulkOperationsTrackerColumns({ onSimOrderingActionClick: mockOnSimOrderingActionClick, user: mockDistributorUser })
    );

    const actionColumn = result.current.columns[8];
    expect(actionColumn).toEqual({
      headerName: "Action",
      field: "actions",
      width: 100,
      renderCell: expect.any(Function),
      sortable: false
    });
  });

it('should pass callback function to Action column renderCell', () => {
  const { result } = renderHook(() => 
    useBulkOperationsTrackerColumns({ onSimOrderingActionClick: mockOnSimOrderingActionClick, user: mockDistributorUser })
  );

  const actionColumn = result.current.columns[8];
  const mockRow = { id: 1, orderId: 'ORDER-123' };
  
  // Add type guard to ensure renderCell exists
  expect(actionColumn.renderCell).toBeDefined();
  
  if (actionColumn.renderCell) {
    actionColumn.renderCell({ row: mockRow, value: undefined });
    
    // Verify that SimOrderingAction was called with correct parameters
    const SimOrderingAction = require('features/SimManagement/SimOrdering/SimOrderingAction');
    expect(SimOrderingAction).toHaveBeenCalledWith(mockRow, mockOnSimOrderingActionClick);
  }
});

  it('should handle Account column renderCell with complete data', () => {
    const { result } = renderHook(() => 
      useBulkOperationsTrackerColumns({ onSimOrderingActionClick: mockOnSimOrderingActionClick, user: mockDistributorUser })
    );

    const accountColumn = result.current.columns[0];
    const mockRow = {
      customerAccountName: 'Test Account',
      customerAccountLogoUrl: 'https://example.com/logo.png'
    };

    expect(accountColumn.renderCell).toBeDefined();
    if (accountColumn.renderCell) {
      const renderResult = accountColumn.renderCell({ row: mockRow, value: undefined });
      expect(renderResult).toBeDefined();
    }
  });
  it('should handle Account column renderCell with missing data', () => {
    const { result } = renderHook(() => 
      useBulkOperationsTrackerColumns({ onSimOrderingActionClick: mockOnSimOrderingActionClick, user: mockDistributorUser })
    );

    const accountColumn = result.current.columns[0];
    const mockRow = {
      customerAccountName: null,
      customerAccountLogoUrl: null
    };

    expect(accountColumn.renderCell).toBeDefined();
    if (accountColumn.renderCell) {
      const renderResult = accountColumn.renderCell({ row: mockRow, value: undefined });
      expect(renderResult).toBeDefined();
    }
  });
});
