import { toastError } from 'core/utilities/toastHelper';
import { ICardsList, IRatePlanFullClient } from 'features/SimManagement/SimManagement.models';
import { getRatePlans } from 'features/SimManagement/api.service';
import { TOASTS } from 'features/constants';
import { useState, useEffect } from 'react';

const useSimManagementClient = () => {
  const [cards] = useState<ICardsList>();
  const [ratePlans, setRatePlans] = useState<IRatePlanFullClient[]>();
  const [loading, setLoading] = useState(false);

  const [search, setSearch] = useState('');

  const getFilteredRows = (data) => {
    if (search === '') {
      return data;
    }
    const filter = JSON.stringify(Object.values(data))?.toLocaleLowerCase().includes(
      search?.toString()?.toLocaleLowerCase());
    return filter;
  };
  const getData = async () => {
    try {
      setLoading(true);
      const [

        { data: ratePlansData },
      ] = await Promise.all([
        getRatePlans(),
      ]);

      setRatePlans(ratePlansData);
    } catch (e) {
      toastError(TOASTS.GET_IMSI_ALLOCATIONS_ERROR);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getData();
  }, []);
  const sortingOnColumn = (propertyParam) => {
    let property = propertyParam;
    let sortOrder = 1;
    if (property[0] === '-') {
      sortOrder = -1;
      property = property.substr(1);
    }
    return function sortColumn(a, b) {
      let result = 0;
      if (a[property] < b[property]) {
        result = -1;
      } else if ((a[property] > b[property])) {
        result = 1;
      }

      return result * sortOrder;
    };
  };
  const onSort = (column) => {
    const newData = cards?.results.length ? [...cards.results] : [];

    newData.sort(sortingOnColumn(column.toLowerCase()));

    // setCards(newData);
  };

  return {
    loading, cards, ratePlans, search, setSearch, onSort, getFilteredRows,
  };
};
export default useSimManagementClient;
