import React from 'react';
import dayjs from 'dayjs';
import StatusText from 'shared/Status/StatusText';
import BulkOperationAction from 'features/SimManagement/BulkOperationsTracker/BulkOperationAction';
import formatImportantWords from 'core/utilities/formatImportantWords';

const useBulkOperationsTrackerColumns = ({ onBulkOperationsTrackerActionClick }) => {
  const columns = [
    {
      headerName: 'Operation',
      field: 'action',
      width: 180,
      sortable: true,
      valueGetter: ({ value }) => {
        if (value) {
          return formatImportantWords(value);
        }
      }
    },
    {
      headerName: 'Field',
      field: 'field',
      width: 150,
      sortable: true,
    },
    {
      headerName: 'Created Date',
      field: 'createdDate',
      width: 180,
      sortable: true,
      valueGetter: ({ value }) => {
        if (value) {
          return dayjs(value).format('DD-MM-YYYY hh:mm:ss');
        }
        return '';
      },
    },
    {
      headerName: 'Initiated By',
      field: 'user',
      width: 180,
      sortable: true,
    },
    {
      headerName: 'IP Address',
      field: 'clientIp',
      width: 180,
      sortable: true,
    },
    {
      headerName: 'Status',
      field: 'status',
      width: 180,
      sortable: true,
      renderCell: ({ value }) => <StatusText status={value} message={value} />,
    },
    {
      headerName: 'Request ID',
      field: 'requestId',
      width: 300,
      sortable: true,
    },
    {
      headerName: 'Action',
      field: 'actions',
      width: 75,
      renderCell: (param) => BulkOperationAction(param.row, onBulkOperationsTrackerActionClick),
      sortable: false,
    },
  ];

  return { columns };
};
export default useBulkOperationsTrackerColumns;
