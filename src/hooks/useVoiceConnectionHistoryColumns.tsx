import { useMediaQuery } from '@mui/material';
import { formatDateWithHours, convertHourMinuteSecond } from 'core/utilities/formatDate';

const useVoiceConnectionHistoryColumns = () => {
  const isSmallScreen = useMediaQuery('(max-width:1600px)');

  return [
    {
      headerName: 'ICCID',
      field: 'iccid',
      width: isSmallScreen ? 200 : 300,
      // sortable: true,

    }, {
      headerName: 'IMSI',
      field: 'imsi',
      width: isSmallScreen ? 200 : 300,
      // sortable: false,

    }, {
      headerName: 'Country',
      field: 'countryName',
      width: isSmallScreen ? 250 : 350,
      // sortable: true,
    }, {
      headerName: 'Carrier',
      field: 'carrierName',
      width: isSmallScreen ? 150 : 250,
      // sortable: true,
    },
    {
      headerName: 'Call Date',
      field: 'callDate',
      width: isSmallScreen ? 180 : 250,
      renderCell: (param) => formatDateWithHours(param?.row?.callDate),
      // sortable: true,
    },
    {
      headerName: 'Call Number',
      field: 'callNumber',
      width: isSmallScreen ? 180 : 200,
      // sortable: true,
    },
    {
      headerName: 'Call Minutes',
      field: 'callMinutes',
      width: isSmallScreen ? 150 : 200,
      // sortable: true,
      renderCell: (param) => convertHourMinuteSecond(param?.row?.callMinutes),
    },

  ];
};

export default useVoiceConnectionHistoryColumns;
