import { useMediaQuery } from "@mui/material";
import { formatDateWithHours } from "core/utilities/formatDate";
import { calCulatePerCentEEUsage } from "core/utilities/toMoneyFormat";
import SimManagmentClientAction from "features/SimManagement/SimManagementClient/SimManagmentClientAction";
import renderStatus from "features/SimManagement/SimManagementClient/renderStatus";
import renderUsage from "features/SimManagement/SimManagementClient/renderUsage";

const useSimManagementClientColumns = ({ onSimManagmentClientActionClick,isEIDVisible }) => {
  const isSmallScreen = useMediaQuery("(max-width:1600px)");

  const columns = [
    {
      headerName: "IMSI",
      field: "imsi",
      width: 150,
      sortable: true,
    },
    {
      headerName: "ICCID",
      field: "iccid",
      width: isSmallScreen ? 175 : 175,
      sortable: true,
    },
    {
      headerName: "MSISDN",
      field: "msisdn",
      width: isSmallScreen ? 145 : 145,
      sortable: true,
    },
    {
      headerName: "EID",
      field: "eid",
      width: isEIDVisible ? 180 : 10,
      sortable: false,
      hide: !isEIDVisible
    },
    {
      headerName: "MSISDN Type",
      field: "msisdnFactor",
      width: 150,
      sortable: false,
      valueGetter: ({ value }) => {
        if (value) {
          return value?.name;
        }
        return "";
      },
    },
    {
      headerName: "SIM Profile",
      field: "simProfile",
      width: isSmallScreen ? 217 : 175,
      sortable: true,
      valueGetter: ({ value }) => {
        if (value) {
          return value?.name?.replace(/_/g, " ");
        }

        return "";
      },
    },
    {
      headerName: "SIM Type",
      field: "type",
      width: isSmallScreen ? 120 : 120,
      sortable: true,
       valueGetter: ({ value }) => {
        if (value) {
          return value.replace(/_/g, ' ');
        }
        return '';
      },
    },
    {
      headerName: "Allocation Reference",
      field: "allocationReference",
      width: isSmallScreen ? 179 : 215,
      sortable: true,
      valueGetter: ({ value }) => {
        if (value) {
          return value.replace(/_/g, ' ');
        }
        return '';
      },
    },
    {
      headerName: "Allocation Date",
      field: "allocationDate",
      width: 180,

      valueGetter: ({ value }) => {
        if (value) {
          return formatDateWithHours(value);
        }

        return "";
      },
      sortable: true,
    },
    {
      headerName: "Rate Plan",
      field: "ratePlan",
      width: isSmallScreen ? 140 : 160,
      sortable: true,
      styles: {
        paddingRight: "30px",
      },
    },
    {
      headerName: "EE %",
      field: "eeUsage",
      width: 120,
      type: "number",
      sortable: false,
      valueGetter: ({ id, value, row }) => {
        if (id === "TOTAL") return "";
        return calCulatePerCentEEUsage(value, row?.usage);
      },
    },
    {
      headerName: "Cycle to Date Traffic (MB)",
      field: "usage",
      width: isSmallScreen ? 140 : 200,
      renderCell: (param) => renderUsage(param.row),
      align: "right",
      sortable: false,
    },

    {
      headerName: "Status",
      field: "simStatus",
      width: isSmallScreen ? 214 : 251,
      sortable: true,
      renderCell: (param) => renderStatus(param.row),
    },
    {
      headerName: "Actions",
      field: "actions",
      width: isSmallScreen ? 153 : 165,
      renderCell: (param) =>
        SimManagmentClientAction(param.row, onSimManagmentClientActionClick),
    },
  ];

  return { columns };
};

export default useSimManagementClientColumns;
