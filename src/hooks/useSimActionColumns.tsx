import React from 'react';
import { useMediaQuery } from '@mui/material';
import { formatDateWithHours } from 'core/utilities/formatDate';
import StatusText from 'shared/Status/StatusText';

const useSimActionColumns = () => {
    const isSmallScreen = useMediaQuery('(max-width:1600px)');
    const isbigScreen = useMediaQuery('(min-width:1600px)');

    return [
        {
            headerName: 'Date',
            field: 'createdAt',
            flex: 0.8,
            minWidth: isSmallScreen ? 160 : 180,
            renderCell: (param) => formatDateWithHours(param?.row?.createdAt),
            sortable: true,
        },
        {
            headerName: 'Action',
            field: 'action',
            flex: 1,
            minWidth: isSmallScreen ? 180 : 200,
            sortable: true,
            renderCell: ({ value }) => {
                const maxLength = 20;
                const displayValue =
                    !isbigScreen && typeof value === 'string' && value.length > maxLength
                        ? `${value.slice(0, maxLength)}...`
                        : value;

                return <StatusText status={value} message={displayValue} />;
            },
        },
        {
            headerName: 'Response',
            field: 'response',
            flex: 1,
            minWidth: isSmallScreen ? 160 : 200,
            sortable: true,
        },
        {
            headerName: 'User Name',
            field: 'createdBy',
            flex: 1.2,
            minWidth: isSmallScreen ? 160 : 200,
            sortable: true,
        }
    ];
};

export default useSimActionColumns;
