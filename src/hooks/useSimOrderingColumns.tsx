import React from 'react';
import dayjs from 'dayjs';
import StatusText from 'shared/Status/StatusText';
import SimOrderingAction from 'features/SimManagement/SimOrdering/SimOrderingAction';
import { Box, Typography } from '@mui/material';
import Image from 'shared/LazyLoad/Image';
import { capitalizeFirstLetter, simOrderStatusColorMap } from 'core/utilities/constants';
import TruncatedList from 'shared/TruncatedList/TruncatedList';
import { organizationTypes } from 'user.model';
import { displayFormFactor } from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants';
import { orderStatusDisplay } from 'features/SimManagement/SimOrdering/constant';

const useBulkOperationsTrackerColumns = ({ onSimOrderingActionClick, user }) => {
  const columns = [
    ...(user?.organization?.type !== organizationTypes.CLIENT ? [
      {
        headerName: "Account",
        field: "customerAccountName",
        width: 220,
        sortable: true,
        renderCell: ({ row }) => (
          <Box
            display="flex"
            alignItems="center"
            columnGap={2}
            sx={{ overflow: "hidden",
                textOverflow: "ellipsis",
                maxWidth: "240px", 
              }}
          >
            <Image
              src={row.customerAccountLogoUrl || ""}
              alt={row.customerAccountName}
              style={{
                display: "block",
                maxWidth: "30px",
                width: "57px",
                height: "auto",
                backgroundSize: "cover",
                backgroundPosition: "center",
              }}
            />
            <Typography
              variant="body1"
              fontSize="13px"
              width="220px" 
              noWrap
              title={row?.customerAccountName || ""}
            >
              {`${row?.customerAccountName || ""}`}
            </Typography>
          </Box>
        ),
    }] : []),
    {
      headerName: "Order ID",
      field: "orderId",
      width: 180,
      sortable: true,
    },
    {
      headerName: "Qty & SIM Type",
      field: "orderItem",
      width: 220,
      sortable: false,
      renderCell: ({ value }) => {
        if (Array.isArray(value)) {
          return <TruncatedList list={value.map((x) => ` ${x.quantity} (${displayFormFactor[x.simType || 'SIM']})`)} maxVisible={1} />
        }
        return null;
      },
    },
    {
      headerName: "Order Date & Time",
      field: "orderDate",
      width: 160,
      sortable: true,
      valueGetter: ({ value }) => {
        if (value) {
          return dayjs(value).format("DD-MM-YYYY hh:mm:ss");
        }
        return "";
      },
    },
    {
      headerName: "Ordered By",
      field: "personPlacingOrder",
      width: 150,
      sortable: true,
    },
    {
      headerName: "Email",
      field: "customerEmail",
      width: 180,
      sortable: true,
    },
    {
      headerName: "Phone",
      field: "customerPhone",
      width: 140,
      sortable: true,
    },
    {
      headerName: "Order Status",
      field: "status",
      width: 180,
      sortable: true,
      renderCell: ({ value }) => (
        <StatusText
          colorFun={simOrderStatusColorMap}
          status={value === 'PENDING' ? orderStatusDisplay[value] : capitalizeFirstLetter(value)}
          message={value === 'PENDING' ? orderStatusDisplay[value] : capitalizeFirstLetter(value)}
        />
      ),
    },
    {
      headerName: "Action",
      field: "actions",
      width: 100,
      renderCell: (param) =>
        SimOrderingAction(param.row, onSimOrderingActionClick),
      sortable: false,
    },
  ];

  return { columns };
};

export default useBulkOperationsTrackerColumns;
