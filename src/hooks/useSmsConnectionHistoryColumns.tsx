import { useMediaQuery } from '@mui/material';
import { formatDateWithHours } from 'core/utilities/formatDate';

const useSmsConnectionHistoryColumns = () => {
  const isSmallScreen = useMediaQuery('(max-width:1600px)');

  return [
    {
      headerName: 'ICCID',
      field: 'iccid',
      width: isSmallScreen ? 200 : 300,
      // sortable: true,

    }, {
      headerName: 'IMSI',
      field: 'imsi',
      width: isSmallScreen ? 200 : 300,
      // sortable: false,

    }, {
      headerName: 'Country',
      field: 'countryName',
      width: isSmallScreen ? 250 : 350,
      // sortable: true,
    }, {
      headerName: 'Carrier',
      field: 'carrierName',
      width: isSmallScreen ? 150 : 250,
      // sortable: true,
    },

    {
      headerName: 'Date Sent',
      field: 'dateSent',
      width: isSmallScreen ? 180 : 250,
      renderCell: (param) => formatDateWithHours(param?.row?.dateSent),
      // sortable: true,
    },
    {
      headerName: 'Sent From',
      field: 'sentFrom',
      width: isSmallScreen ? 180 : 200,
      // sortable: true,
    },
    {
      headerName: 'Sent To',
      field: 'sentTo',
      width: isSmallScreen ? 150 : 200,
      // sortable: true,
    },

  ];
};

export default useSmsConnectionHistoryColumns;
