import { renderHook } from '@testing-library/react-hooks';
import { useMediaQuery } from '@mui/material';
import renderStatus from 'features/SimManagement/SimManagementClient/renderStatus';
import SimManagmentClientAction from 'features/SimManagement/SimManagementClient/SimManagmentClientAction';
import useSimManagementClientColumns from './useSimManagementClientColumns';

jest.mock('@mui/material', () => ({
  useMediaQuery: jest.fn(),
}));

const regularScreenColumns = [
  {
    field: 'iccid',
    headerName: 'ICCID',
    sortable: true,
    width: 175,
  },
  {
    field: 'msisdn',
    headerName: 'MSISDN',
    sortable: true,
    width: 175,
  },
  {
    field: 'imsi',
    headerName: 'IMSI Number',
    sortable: true,
    width: 175,
  },
  {
    field: 'type',
    headerName: 'Type',
    sortable: true,
    width: 175,
  },
  {
    field: 'allocationReference',
    headerName: 'Allocation Reference',
    sortable: true,
    width: 234,
  },
  {
    field: 'ratePlanName',
    headerName: 'Rate Plan',
    width: 175,
  },
  {
    field: 'usage',
    headerName: 'Cycle to Date Traffic (MB)',
    width: 200,
  },
  {
    field: 'simStatus',
    headerName: 'Status',
    renderCell: (param) => renderStatus(param.row),
    sortable: true,
    width: 251,
  },
  {
    field: 'actions',
    headerName: 'Actions',
    renderCell: (param) => SimManagmentClientAction(param?.row),
    width: 165,
  },
];

const smallScreenColumns = [
  {
    field: 'iccid',
    headerName: 'ICCID',
    sortable: true,
    width: 175,
  },
  {
    field: 'msisdn',
    headerName: 'MSISDN',
    sortable: true,
    width: 175,
  },
  {
    field: 'imsi',
    headerName: 'IMSI Number',
    sortable: true,
    width: 217,
  },
  {
    field: 'type',
    headerName: 'Type',
    sortable: true,
    width: 159,
  },
  {
    field: 'allocationReference',
    headerName: 'Allocation Reference',
    sortable: true,
    width: 159,
  },
  {
    field: 'ratePlanName',
    headerName: 'Rate Plan',
    width: 140,
  },
  {
    field: 'usage',
    headerName: 'Cycle to Date Traffic (MB)',
    width: 140,
  },
  {
    field: 'simStatus',
    headerName: 'Status',
    renderCell: (param) => renderStatus(param.row),
    sortable: true,
    width: 214,
  },
  {
    field: 'actions',
    headerName: 'Actions',
    renderCell: (param) => SimManagmentClientAction(param.row),
    width: 153,
  },
];

describe('useSimManagementClientColumns', () => {
  it('returns an array of columns', () => {
    useMediaQuery.mockReturnValue(false);

    renderHook(() => useSimManagementClientColumns()).waitFor((result) => {
      expect(result.current).toEqual(regularScreenColumns);
    });
  });

  it('returns an array of columns for small screens', () => {
    useMediaQuery.mockReturnValue(true);

    renderHook(() => useSimManagementClientColumns()).waitFor((result) => {
      expect(result.current).toEqual(smallScreenColumns);
    });
  });
});
