import getUser from 'api.service';
import { AxiosResponse } from 'axios';
import { RESPONSE } from 'core/utilities/constants';
import { toastInfo } from 'core/utilities/toastHelper';
import { useState, useEffect } from 'react';

const useProfile = () => {
  const [loading, setLoading] = useState(false);
  const [accounts, setAccounts] = useState([]);
  const [error, setAccountsError] = useState(false);
  const getAccountsList = async () => {
    try {
      setLoading(true);

      const accountsResponse: AxiosResponse = await getUser();
      setAccounts(accountsResponse.data);

      setLoading(false);
      setAccountsError(false);
    } catch (err) {
      setLoading(false);
      setAccountsError(true);
      toastInfo(RESPONSE.GET_ACCOUNTS_ERROR);
    }
  };
  useEffect(() => {
    getAccountsList();
  }, []);

  return {
    loading, accounts, error,
  };
};

export default useProfile;
