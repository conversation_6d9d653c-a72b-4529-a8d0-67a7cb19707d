import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { defaultPage, defaultPgeSize } from 'features/SimManagement/SimManagementClient/constants';

export const pageFieldName = 'page';
export const pageSizeFieldName = 'pageSize';
export const fieldFieldName = 'field';
export const sortFieldName = 'sort';
export const searchFieldName = 'search';

const useSimManagementClientSearchParams = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  const setParamsToUrl = (page, pageSize, field, sort, search) => {
    if (!page && !pageSize && !field && !sort && !search) {
      return;
    }

    const newSearchParams = new URLSearchParams();

    if (page) {
      newSearchParams.set(pageFieldName, page);
    }
    if (pageSize) {
      newSearchParams.set(pageSizeFieldName, pageSize);
    }
    if (field) {
      newSearchParams.set(fieldFieldName, field);
    }
    if (sort) {
      newSearchParams.set(sortFieldName, sort);
    }
    if (search) {
      newSearchParams.set(searchFieldName, search);
    }

    setSearchParams(newSearchParams);
  };

  const getParamsFromUrl = () => {
    const page = searchParams.get(pageFieldName) || defaultPage;
    const pageSize = searchParams.get(pageSizeFieldName) || defaultPgeSize;
    const field = searchParams.get(fieldFieldName);
    const sort = searchParams.get(sortFieldName);
    const search = searchParams.get(searchFieldName);

    return {
      page, pageSize, field, sort, search,
    };
  };

  const defaultPagination = useMemo(() => ({
    page: Number(searchParams.get(pageFieldName)) || defaultPage,
    pageSize: Number(searchParams.get(pageSizeFieldName)) || defaultPgeSize,
  }), []);

  const defaultSort = useMemo(() => ({
    field: searchParams.get(fieldFieldName),
    sort: searchParams.get(sortFieldName),
  }), []);

  const initialSearchValue = useMemo(() => searchParams.get(searchFieldName), []);

  return {
    setParamsToUrl,
    getParamsFromUrl,
    defaultPagination,
    defaultSort,
    initialSearchValue,
  };
};

export default useSimManagementClientSearchParams;
