import { useMediaQuery } from '@mui/material';
import { convertHourMinuteSecond, formateDateWithTimeZone } from 'core/utilities/formatDate';
import { getNumberWithCommas } from 'core/utilities/toMoneyFormat';

const useConnectionHistoryColumns = () => {
  const isSmallScreen = useMediaQuery('(max-width:1600px)');

  return [
    {
      headerName: 'ICCID',
      field: 'iccid',
      width: isSmallScreen ? 200 : 270,
      // sortable: true,

    }, {
      headerName: 'IMSI',
      field: 'imsi',
      width: isSmallScreen ? 200 : 270,
      // sortable: true,

    }, {
      headerName: 'Country',
      field: 'countryName',
      width: isSmallScreen ? 250 : 250,
      // sortable: true,
    }, {
      headerName: 'Carrier',
      field: 'carrierName',
      width: isSmallScreen ? 150 : 200,
      // sortable: true,
    }, {
      headerName: 'Session Start Time',
      field: 'sessionStarttime',
      width: isSmallScreen ? 200 : 220,
      renderCell: (param) => formateDateWithTimeZone(param?.row?.sessionStarttime),
      // sortable: true,
    },
    {
      headerName: 'Session End Time',
      field: 'sessionEndtime',
      width: isSmallScreen ? 200 : 200,
      renderCell: (param) => formateDateWithTimeZone(param?.row?.sessionEndtime),
      // sortable: true,
    },

    {
      headerName: 'Duration (HH:mm:ss)',
      field: 'duration',
      width: isSmallScreen ? 180 : 200,
      renderCell: (param) => convertHourMinuteSecond(param?.row?.duration),
      styles: {
        paddingRight: '30px',
      },
      // sortable: true,
    },
    {
      headerName: 'Data Volume (KB)',
      field: 'dataVolume',
      width: isSmallScreen ? 180 : 200,
       
      renderCell: (param) => getNumberWithCommas((Number(param?.row?.dataVolume) / 1024).toFixed(2)),
      type: 'number',
      // sortable: true,
      styles: {
        '& .MuiDataGrid-columnHeaderTitleContainer': {
          flexDirection: 'row-reverse',
        },
      },

    },
  ];
};

export default useConnectionHistoryColumns;
