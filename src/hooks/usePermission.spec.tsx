 
 
import { renderHook } from '@testing-library/react-hooks';
import { waitFor } from '@testing-library/react';
import usePermission from './usePermission'; // Update the import path accordingly
import permissions from './permissions';

// Mock the AppContextProvider module
jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    access: permissions.result,
  }),
}));

describe('usePermission', () => {
  it('should return an empty array when repoName is not found in access', () => {
    const { result } = renderHook(() => usePermission(['nonExistentRepo']));
    expect(result.current).toEqual([]);
  });

  it('should return the correct permissions for a single repo', () => {
    const { result } = renderHook(() => usePermission(['Accounts']));
    expect(result.current).toEqual([
      {
        name: 'Delete Account',
        title: 'Delete Account',
      },
      {
        name: 'View Accounts',
        title: 'View Accounts',
      },
      {
        name: 'View Account Details',
        title: 'View Account Details',
      },
      {
        name: 'Create Account',
        title: 'Create Account',
      },
      {
        name: 'Modify Account',
        title: 'Modify Account',
      },
    ]);
  });

  it('should return the correct permissions for multiple repos', () => {
    const { result } = renderHook(() => usePermission(['Audit', 'Accounts']));
    expect(result.current).toEqual([
      {
        name: 'SIM Audit Logs',
        title: 'SIM Audit Logs',
      },
      {
        name: 'Account Audit Logs',
        title: 'Account Audit Logs',
      },
      {
        name: 'Delete Account',
        title: 'Delete Account',
      },
      {
        name: 'View Accounts',
        title: 'View Accounts',
      },
      {
        name: 'View Account Details',
        title: 'View Account Details',
      },
      {
        name: 'Create Account',
        title: 'Create Account',
      },
      {
        name: 'Modify Account',
        title: 'Modify Account',
      },
    ]);
  });

  it('should update permissions when access prop changes', () => {
    const { result, rerender } = renderHook(({ repoName }) => usePermission(repoName), {
      initialProps: { repoName: ['AccountManagement'] },
    });
    waitFor(() => {
      expect(result.current).toEqual([
        {
          name: 'Create Account',
          title: 'Create Account',
        },
        {
          name: 'Modify Account',
          title: 'Modify Account',
        },
        {
          name: 'View Accounts',
          title: 'list_accounts',
        },
        {
          name: 'Delete Account',
          title: 'Delete Account',
        },
        {
          name: 'View Account Details',
          title: 'view_account',
        },
      ]);
    });
    rerender({ repoName: ['AuditLog'] });
    waitFor(() => {
      expect(result.current).toEqual([
        {
          name: 'Account Audit Logs',
          title: 'system_audit_logs',
        },
      ]);
    });
  });

  it('should return an empty array when access is empty', () => {
    // Mock the access to be an empty array
    jest.spyOn(require('AppContextProvider'), 'useAppContext').mockReturnValue({ access: [] });

    const { result } = renderHook(() => usePermission(['AccountManagement']));
    expect(result.current).toEqual([]);
  });
});
