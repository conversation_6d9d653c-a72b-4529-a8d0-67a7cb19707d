import { AxiosResponse } from 'axios';
import { getSummaryApi } from 'features/SimManagement/api.service';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

export default function useGetSummary() {
  const [loading, setLoading] = useState(false);
  const [summary, setSummary] = useState('');
  const { imsi } = useParams();

  const getSimSummary = async () => {
    try {
      setLoading(true);

      const response:AxiosResponse = await getSummaryApi(imsi);
      setSummary(response.data);

      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };
  useEffect(() => {
    getSimSummary();
  }, []);

  return {
    loading, summary,
  };
}
