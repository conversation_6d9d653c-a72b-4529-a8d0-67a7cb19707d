import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';

export const pageFieldName = 'page';
export const pageSizeFieldName = 'pageSize';
export const fieldFieldName = 'field';
export const sortFieldName = 'sort';
export const searchFieldName = 'search';

interface IUseMuiTableSearchParamsReturn {
  setParamsToUrl: (
    page: string | number, pageSize: string | number, field?: string, sort?: string,
    search?: string,
  ) => void
  getParamsFromUrl: () => ({
    page: string | number, pageSize: string | number, field: string | null, sort: string | null,
    search: string | null
  })
  generateParamsForUrl: (
    page: string | number, pageSize: string | number, field?: string, sort?: string,
    search?: string, prefix?: string
  ) => URLSearchParams
  defaultPagination: { page: number, pageSize: number }
  defaultSort: { field: string | null, sort: string | null }
  initialSearchValue: string
}

const useMuiTableSearchParams = (
  defaultPage = 1,
  defaultPageSize = 10,
) => {
  const [searchParams, setSearchParams] = useSearchParams();

  const generateParamsForUrl = (page, pageSize, field, sort, search, prefix = '') => {
    if (!page && !pageSize && !field && !sort && !search) {
      return new URLSearchParams();
    }

    const newSearchParams = new URLSearchParams();

    if (page) newSearchParams.set(`${prefix}${pageFieldName}`, String(page));
    if (pageSize) newSearchParams.set(`${prefix}${pageSizeFieldName}`, String(pageSize));
    if (field) newSearchParams.set(`${prefix}${fieldFieldName}`, field);
    if (sort) newSearchParams.set(`${prefix}${sortFieldName}`, sort);
    if (search) newSearchParams.set(`${prefix}${searchFieldName}`, search);

    return newSearchParams;
  };

  const setParamsToUrl = (page, pageSize, field, sort, search) => {
    if (!page && !pageSize && !field && !sort && !search) {
      return;
    }

    const newSearchParams = generateParamsForUrl(page, pageSize, field, sort, search);
    setSearchParams(newSearchParams);
  };

  const getParamsFromUrl = (prefix = '') => {
    const page = searchParams.get(prefix + pageFieldName) || defaultPage;
    const pageSize = searchParams.get(prefix + pageSizeFieldName) || defaultPageSize;
    const field = searchParams.get(prefix + fieldFieldName);
    const sort = searchParams.get(prefix + sortFieldName);
    const search = searchParams.get(prefix + searchFieldName);

    return {
      page, pageSize, field, sort, search,
    };
  };

  const defaultPagination = useMemo(() => {
    const page = Number(searchParams.get(pageFieldName)) || defaultPage;
    const pageSize = Number(searchParams.get(pageSizeFieldName)) || defaultPageSize;
    return { page, pageSize };
  }, [searchParams]);

  const defaultSort = useMemo(() => ({
    field: searchParams.get(fieldFieldName),
    sort: searchParams.get(sortFieldName),
  }), [searchParams]);

  const initialSearchValue = useMemo(() => searchParams.get(searchFieldName) ?? '', [searchParams]);


  return <IUseMuiTableSearchParamsReturn>{
    setParamsToUrl,
    getParamsFromUrl,
    generateParamsForUrl,
    defaultPagination,
    defaultSort,
    initialSearchValue,
  };
};

export default useMuiTableSearchParams;
