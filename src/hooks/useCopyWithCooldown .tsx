import { useRef } from 'react';

export const useCopyWithCooldown = (cooldownMs = 5000) => {
  const lastCopiedTime = useRef(0);

  const handleCopy = async (ref, onSuccess) => {
    const now = Date.now();
    if (now - lastCopiedTime.current < cooldownMs) return;

    if (ref?.current) {
      await navigator.clipboard.writeText(ref.current.value);
      lastCopiedTime.current = now;
      if (onSuccess) onSuccess();
    }
  };

  return { handleCopy };
};

