import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { useField, FieldInputProps, FieldMetaProps } from 'formik';
import TextFieldWrapper from 'features/SimManagement/SimOrdering/TextFieldWrapper';

// Mock Formik's useField hook
jest.mock('formik', () => ({
  ...jest.requireActual('formik'),
  useField: jest.fn(),
  getIn: jest.fn(),
}));

const mockUseField = useField as jest.MockedFunction<typeof useField>;
const mockGetIn = require('formik').getIn as jest.MockedFunction<any>;

describe('TextFieldWrapper', () => {
  const mockFormikValue = {
    values: { testField: 'test value' },
    setFieldValue: jest.fn(),
    setFieldTouched: jest.fn(),
  };

  const defaultProps = {
    name: 'testField',
    formikValue: mockFormikValue,
    label: 'Test Label',
  };

  const createMockField = (value: string = ''): FieldInputProps<string> => ({
    name: 'testField',
    value,
    onChange: jest.fn(),
    onBlur: jest.fn(),
  });

  const createMockMeta = (touched: boolean = false, error?: string): FieldMetaProps<string> => ({
    value: '',
    error: error || '',
    touched,
    initialError: '',
    initialTouched: false,
    initialValue: '',
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetIn.mockImplementation((obj: any, path: string) => obj[path] || '');
  });

  it('renders TextField with correct props when field has no errors', () => {
    mockUseField.mockReturnValue([
      createMockField('test value'),
      createMockMeta(false),
      { setValue: jest.fn(), setTouched: jest.fn(), setError: jest.fn() },
    ]);
    mockGetIn.mockReturnValue('test value');

    render(<TextFieldWrapper {...defaultProps} />);

    const textField = screen.getByRole('textbox');
    expect(textField).toBeInTheDocument();
    expect(textField).toHaveValue('test value');
    expect(textField).toHaveAttribute('aria-invalid', 'false');
  });

  it('does not display error when field has error but is not touched', () => {
    mockUseField.mockReturnValue([
      createMockField(''),
      createMockMeta(false, 'This field is required'),
      { setValue: jest.fn(), setTouched: jest.fn(), setError: jest.fn() },
    ]);
    mockGetIn.mockReturnValue('');

    render(<TextFieldWrapper {...defaultProps} />);

    const textField = screen.getByRole('textbox');
    expect(textField).toHaveAttribute('aria-invalid', 'false');
    expect(screen.queryByText('This field is required')).not.toBeInTheDocument();
  });

  it('handles onChange events correctly', () => {
    mockUseField.mockReturnValue([
      createMockField(''),
      createMockMeta(false),
      { setValue: jest.fn(), setTouched: jest.fn(), setError: jest.fn() },
    ]);
    mockGetIn.mockReturnValue('');

    render(<TextFieldWrapper {...defaultProps} />);

    const textField = screen.getByRole('textbox');
    fireEvent.change(textField, { target: { value: 'new value' } });

    expect(mockFormikValue.setFieldValue).toHaveBeenCalledWith('testField', 'new value');
  });

  it('handles onBlur events correctly', () => {
    mockUseField.mockReturnValue([
      createMockField('test'),
      createMockMeta(false),
      { setValue: jest.fn(), setTouched: jest.fn(), setError: jest.fn() },
    ]);
    mockGetIn.mockReturnValue('test');

    render(<TextFieldWrapper {...defaultProps} />);

    const textField = screen.getByRole('textbox');
    fireEvent.blur(textField);

    expect(mockFormikValue.setFieldTouched).toHaveBeenCalledWith('testField', true);
  });

  it('handles empty/undefined values correctly', () => {
    mockUseField.mockReturnValue([
      createMockField(''),
      createMockMeta(false),
      { setValue: jest.fn(), setTouched: jest.fn(), setError: jest.fn() },
    ]);
    mockGetIn.mockReturnValue(undefined);

    render(<TextFieldWrapper {...defaultProps} />);

    const textField = screen.getByRole('textbox');
    expect(textField).toHaveValue('');
  });

  it('forwards additional props to TextField', () => {
    mockUseField.mockReturnValue([
      createMockField('test'),
      createMockMeta(false),
      { setValue: jest.fn(), setTouched: jest.fn(), setError: jest.fn() },
    ]);
    mockGetIn.mockReturnValue('test');

    render(
      <TextFieldWrapper
        {...defaultProps}
        placeholder="Enter text here"
        disabled={true}
        className="custom-class"
      />
    );

    const textField = screen.getByRole('textbox');
    expect(textField).toHaveAttribute('placeholder', 'Enter text here');
    expect(textField).toBeDisabled();
  });

  it('works with nested field names', () => {
    const nestedProps = {
      ...defaultProps,
      name: 'user.email',
      formikValue: {
        ...mockFormikValue,
        values: { user: { email: '<EMAIL>' } },
      },
    };

    mockUseField.mockReturnValue([
      createMockField('<EMAIL>'),
      createMockMeta(false),
      { setValue: jest.fn(), setTouched: jest.fn(), setError: jest.fn() },
    ]);
    mockGetIn.mockReturnValue('<EMAIL>');

    render(<TextFieldWrapper {...nestedProps} />);

    const textField = screen.getByRole('textbox');
    expect(textField).toHaveValue('<EMAIL>');

    fireEvent.change(textField, { target: { value: '<EMAIL>' } });
    expect(mockFormikValue.setFieldValue).toHaveBeenCalledWith('user.email', '<EMAIL>');
  });

  it('applies fullWidth and outlined variant by default', () => {
    mockUseField.mockReturnValue([
      createMockField('test'),
      createMockMeta(false),
      { setValue: jest.fn(), setTouched: jest.fn(), setError: jest.fn() },
    ]);
    mockGetIn.mockReturnValue('test');

    const { container } = render(<TextFieldWrapper {...defaultProps} />);

    const textFieldRoot = container.querySelector('.MuiTextField-root');
    expect(textFieldRoot).toHaveClass('MuiFormControl-fullWidth');
  });
});
