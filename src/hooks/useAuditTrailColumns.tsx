import React from 'react';
import { useMediaQuery } from '@mui/material';
import { formatDateWithHours } from 'core/utilities/formatDate';
import StatusText from 'shared/Status/StatusText';

const useAuditTrailColumns = () => {
  const isSmallScreen = useMediaQuery('(max-width:1600px)');

  return [
    {
      headerName: 'Field',
      field: 'field',
      width: isSmallScreen ? 250 : 360,
      sortable: false,

    }, {
      headerName: 'Action',
      field: 'action',
      width: isSmallScreen ? 300 : 360,
      sortable: false,
      renderCell: ({ value }) => <StatusText status={value} message={value} />,

    }, {
      headerName: 'Previous Value',
      field: 'priorValue',
      width: isSmallScreen ? 300 : 360,
      sortable: true,
      valueGetter: ({ value }) => {
        if (value) {
          return value?.replace(/_/g, ' ');
        }
        return '';
      },
    }, {
      headerName: 'New Value',
      field: 'newValue',
      width: isSmallScreen ? 300 : 360,
      sortable: true,
      valueGetter: ({ value }) => {
        if (value) {
          return value?.replace(/_/g, ' ');
        }
        return '';
      },
    }, {
      headerName: 'Date',
      field: 'createdDate',
      width: isSmallScreen ? 300 : 360,
      renderCell: (param) => formatDateWithHours(param?.row?.createdDate),
      sortable: true,
    }, {
      headerName: 'User Name',
      field: 'createdBy',
      width: isSmallScreen ? 300 : 360,
      sortable: true,
    },

  ];
};

export default useAuditTrailColumns;
