import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';

export const pageFieldName = 'page';
export const pageSizeFieldName = 'pageSize';
export const fieldFieldName = 'field';
export const sortFieldName = 'sort';
export const searchFieldName = 'search';

interface IUseMuiTableSearchParamsReturn {
  setParamsToUrl: (
    page: string | number, pageSize: string | number, field?: string, sort?: string,
    search?: string,
  ) => void
  getParamsFromUrl: () => ({
    page: string | number, pageSize: string | number, field: string | null, sort: string | null,
    search: string | null
  })
  generateParamsForUrl: (
    page: string | number, pageSize: string | number, field?: string, sort?: string,
    search?: string,
  ) => URLSearchParams
  defaultPagination: { page: number, pageSize: number }
  defaultSort: { field: string | null, sort: string | null }
  initialSearchValue: string | null
}

const useMuiTableSearchParams = (
  defaultPage = 1,
  defaultPageSize = 10,
) => {
  const [searchParams, setSearchParams] = useSearchParams();

  const generateParamsForUrl = (page, pageSize, field, sort, search) => {
    if (!page && !pageSize && !field && !sort && !search) {
      return new URLSearchParams();
    }

    const newSearchParams = new URLSearchParams();

    if (page) {
      newSearchParams.set(pageFieldName, page);
    }
    if (pageSize) {
      newSearchParams.set(pageSizeFieldName, pageSize);
    }
    if (field) {
      newSearchParams.set(fieldFieldName, field);
    }
    if (sort) {
      newSearchParams.set(sortFieldName, sort);
    }
    if (search) {
      newSearchParams.set(searchFieldName, search);
    }

    return newSearchParams;
  };

  const setParamsToUrl = (page, pageSize, field, sort, search) => {
    if (!page && !pageSize && !field && !sort && !search) {
      return;
    }

    const newSearchParams = generateParamsForUrl(page, pageSize, field, sort, search);

    setSearchParams(newSearchParams);
  };

  const getParamsFromUrl = () => {
    const page = searchParams.get(pageFieldName) || defaultPage;
    const pageSize = searchParams.get(pageSizeFieldName) || defaultPageSize;
    const field = searchParams.get(fieldFieldName);
    const sort = searchParams.get(sortFieldName);
    const search = searchParams.get(searchFieldName);

    return {
      page, pageSize, field, sort, search,
    };
  };

  const defaultPagination = useMemo(() => ({
    page: Number(searchParams.get(pageFieldName)) || defaultPage,
    pageSize: Number(searchParams.get(pageSizeFieldName)) || defaultPageSize,
  }), []);

  const defaultSort = useMemo(() => ({
    field: searchParams.get(fieldFieldName),
    sort: searchParams.get(sortFieldName),
  }), []);

  const initialSearchValue = useMemo(() => searchParams.get(searchFieldName), []);

  return <IUseMuiTableSearchParamsReturn>{
    setParamsToUrl,
    getParamsFromUrl,
    generateParamsForUrl,
    defaultPagination,
    defaultSort,
    initialSearchValue,
  };
};

export default useMuiTableSearchParams;
