import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
} from 'axios';

import {
  setRequestHeadersInterceptor,
  setRequestErrorInterceptor,
} from 'core/interceptors';

const coreAxios: AxiosInstance = axios.create();

class HTTPService {
  static setDefaultGlobalConfig(axiosInstance: AxiosInstance, apiUrl: string) {
     
    axiosInstance.defaults.baseURL = apiUrl;
     
    axiosInstance.defaults.withCredentials = true;

    axiosInstance.interceptors.response.use(
      (res: AxiosResponse) => res,
      (error: AxiosError) => Promise.reject(error),
    );
  }

  static setAccessToken(axiosInstance: AxiosInstance, cookies: { [x: string]: unknown }) {
    axiosInstance.interceptors.request
      .use((prevConfig: AxiosRequestConfig) => setRequestHeadersInterceptor(prevConfig, cookies));
  }

  static setCorsError(
    axiosInstance: AxiosInstance,
    redirectToLogout: (url: string) => void,
    logoutUrl: string | undefined,
  ) {
    axiosInstance.interceptors.response.use((res: AxiosResponse) => res, (error: AxiosError) => {
      setRequestErrorInterceptor(error, redirectToLogout, logoutUrl);

      return Promise.reject(error);
    });
  }

  static getController() {
    return new AbortController();
  }

  static cancelRequest(controller) {
    controller.abort();
  }
}

export {
  HTTPService,
  coreAxios,
};
