import { renderHook } from '@testing-library/react';
import usePrevious from './usePrevious';

describe('usePrevious', () => {
  it('should return undefined on initial render', () => {
    const { result } = renderHook(() => usePrevious('initial'));

    expect(result.current).toBeUndefined();
  });

  it('should always return previous state after each update', () => {
    const { result, rerender } = renderHook(() => usePrevious('initial'));

    rerender('next');
    expect(result.current).toBe('initial');
  });
});
