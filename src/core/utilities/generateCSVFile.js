import { toast } from 'react-toastify';
import convertJsonToCsv from './convertJsonToCsv';
import { toastError, toastSuccess } from './toastHelper';

const generateCSVFile = (data, fileName, setIsLoading, delimiter = ',') => {
  try {
    const csvData = convertJsonToCsv(data, delimiter);
    const csvBlob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const csvUrl = URL.createObjectURL(csvBlob);
    const downloadLink = document.createElement('a');
    downloadLink.href = csvUrl;
    downloadLink.download = `${fileName}.csv`;
    downloadLink.style.display = 'none';
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
    URL.revokeObjectURL(csvUrl);
    toast.success('Generate Data');
    setIsLoading(false);
    toastSuccess('Data Downloaded Succesfully');
  } catch (err) {
    setIsLoading(false);
    toastError('Error while downloading CSV:', err);
  }
};

export default generateCSVFile;
