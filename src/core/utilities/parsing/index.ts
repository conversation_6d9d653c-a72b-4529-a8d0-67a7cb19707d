import dayjs from 'dayjs';
import IPermission from 'model/IPermission';

const SummaryParse = (data) => {
  try {
    if (!data) { return []; }

    const modifiedSummary = [
      {
        name: 'Month',
        value: '',
      }, {
        name: 'ICCID',
        value: data?.iccid || '-',
      }, {
        name: 'MSISDN',
        value: data?.msisdn || '-',
      }, {
        name: 'Primary IMSI',
        value: data?.imsi || '-',
      }, {
        name: 'SIM Status',
        value: data?.simStatus || '-',
      },
      {
        name: 'First Activated',
        value: data?.firstActivated ? dayjs(data?.firstActivated).format('DD-MM-YYYY') : 'N/A',
      },
      {
        name: 'Last Session',
        value: data?.lastSession ? dayjs(data?.lastSession).format('DD-MM-YYYY') : 'N/A',
      },
      {
        name: 'Rate Plan',
        value: data?.ratePlan ? data?.ratePlan : 'N/A',
      },
    ];
    return modifiedSummary;
  } catch (e) {
    return [];
  }
};
export const accountNameSlice = (str, size = 15) => {
  let accountName = '';
  if (str && str.length > size) {
    accountName = str.substring(0, size).concat('...');
  } else {
    accountName = str;
  }
  return accountName;
};
export const parsePermissions = (access, repoName) => {
  const finalValue: Array<IPermission> = [];
  if (access && access.length > 0) {
    const accessValue = access.filter((x) => repoName.includes(x.name));

    if (accessValue) {
      accessValue?.map((x) => x.permission).reduce((result, arr) => {
        arr?.forEach((obj) => {
          finalValue.push(obj);
        });
        return result;
      }, {});
    }
  }
  return finalValue;
};
export default SummaryParse;
