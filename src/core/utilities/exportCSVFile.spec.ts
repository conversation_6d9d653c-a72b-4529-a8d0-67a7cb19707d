 
import exportCSVFile from './exportCSVFile';

interface MockElement {
  url?: string;
  name?: string;
  click: () => void;

  [key: string]: unknown;
}

const mockClick = jest.fn();

const creatElementMock = jest.fn<MockElement, []>(() => ({
  set href(url: string) {
    this.url = url;
  },

  set download(name: string) {
    this.name = name;
  },

  click: mockClick,
}));

describe('exportCSVFile', () => {
  beforeEach(() => {
    // Mock Blob
    // @ts-ignore
    global.Blob = jest.fn();

    // Mock window.URL.createObjectURL
    global.URL.createObjectURL = jest.fn();

    // Mock window.URL.revokeObjectURL
    global.URL.revokeObjectURL = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should export a CSV file with given data and file name', () => {
    const data = 'test-data';
    const fileName = 'test.csv';

    // @ts-ignore
    document.createElement = creatElementMock;

    exportCSVFile(data, fileName);

    expect(global.URL.createObjectURL).toHaveBeenCalledTimes(1);
    expect(mockClick).toHaveBeenCalledTimes(1);
  });
});
