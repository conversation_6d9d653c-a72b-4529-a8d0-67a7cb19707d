import { ICardsRemains } from 'features/SimManagement/SimManagement.models';
import { TotalProviderName } from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants';

export const getTotalRow = (data: ICardsRemains[]): ICardsRemains => {
  const { standard: standardTotal, micro: microTotal, nano: nanoTotal, mff2: mffTotal, mff2_euicc: mff2EuiccTotal  } = data.reduce(
    (item, accumulator) => ({
      standard: accumulator.standard + item.standard,
      micro: accumulator.micro + item.micro,
      nano: accumulator.nano + item.nano,
      mff2: accumulator.esim_mff2 + item.mff2,
      mff2_euicc: accumulator.esim_mff2_euicc + item.mff2_euicc
    }), { standard: 0, micro: 0, nano: 0, mff2: 0, mff2_euicc: 0 });

  return {
    provider: TotalProviderName,
    standard: standardTotal,
    micro: microTotal,
    nano: nanoTotal,
    esim_mff2: mffTotal,
    esim_mff2_euicc: mff2EuiccTotal,
  };
};

export default getTotalRow;
