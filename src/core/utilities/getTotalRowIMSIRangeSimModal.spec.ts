import getTotalRow from 'core/utilities/getTotalRowIMSIRangeSimModal';
import mockIMSIRangesSimData from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/mockIMSIRangesSimData.json';

describe('IMSIRangeSimModal utils', () => {
  describe('getTotalRow', () => {
    test('should calculate standard', () => {
      const { standard } = getTotalRow(mockIMSIRangesSimData);

      expect(standard).toBe(
        mockIMSIRangesSimData[0].standard
        + mockIMSIRangesSimData[1].standard
        + mockIMSIRangesSimData[2].standard,
      );
    });
    test('should calculate micro', () => {
      const { micro } = getTotalRow(mockIMSIRangesSimData);

      expect(micro).toBe(
        mockIMSIRangesSimData[0].micro
        + mockIMSIRangesSimData[1].micro
        + mockIMSIRangesSimData[2].micro,
      );
    });
    test('should calculate nano', () => {
      const { nano } = getTotalRow(mockIMSIRangesSimData);

      expect(nano).toBe(
        mockIMSIRangesSimData[0].nano
        + mockIMSIRangesSimData[1].nano
        + mockIMSIRangesSimData[2].nano,
      );
    });
  });
});
