import React from 'react';
import { toast, ToastOptions } from 'react-toastify';
import { AiOutlineExclamationCircle, AiOutlineInfoCircle } from 'react-icons/ai';

export const TOAST_CONTAINER_ID = 'SPOG_SIM_MANAGEMENT';

export const toastError = (content: any, options?: ToastOptions) => {
  toast.error(content, {
    icon: <AiOutlineExclamationCircle />,
    containerId: TOAST_CONTAINER_ID,
    ...options,
  });
};

export const toastInfo = (content: any, options?: ToastOptions) => {
  toast.info(content, {
    icon: <AiOutlineInfoCircle />,
    containerId: TOAST_CONTAINER_ID,
    ...options,
  });
};

export const toastSuccess = (content: any, options?: ToastOptions) => {
  toast.success(content, { containerId: TOAST_CONTAINER_ID, ...options });
};
