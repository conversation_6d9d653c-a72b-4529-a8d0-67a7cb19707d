import React, { FC, ReactElement } from 'react';
import { render } from '@testing-library/react';
import { MemoryRouter, Route, Routes } from 'react-router-dom';

type Children = ReactElement | ReactElement[];

interface ITestProviderProps {
  children: Children;
  route: string | undefined;
  query: string | undefined;
}

const TestProvider: FC<ITestProviderProps> = ({ children, route, query }) => {
  const routerProps = {
    ...(route && { initialEntries: [route + query] }),
  };

  return (
    <MemoryRouter {...routerProps}>
      <Routes>
        <Route path="*" element={children} />
      </Routes>
    </MemoryRouter>
  );
};

export default function testRender(ui: Children, route?: string, query?: string) {
  return render(<TestProvider route={route} query={query}>{ui}</TestProvider>);
}
