import { getNumberWithCommas } from './toMoneyFormat';

describe('utils', () => {
  it('should not add empty spaces', () => {
    const formatted = getNumberWithCommas(100);
    expect(formatted).toBe('100');
  });

  it('should add 1 empty space', () => {
    const formatted = getNumberWithCommas(1000);
    expect(formatted).toBe('1,000');
  });

  it('should add 2 empty space', () => {
    const formatted = getNumberWithCommas(1000000);
    expect(formatted).toBe('1,000,000');
  });

  it('should format string and number the same way', () => {
    const formattedNumber = getNumberWithCommas(1000000);
    const formattedString = getNumberWithCommas('1000000');
    expect(formattedNumber).toBe(formattedString);
  });
});
