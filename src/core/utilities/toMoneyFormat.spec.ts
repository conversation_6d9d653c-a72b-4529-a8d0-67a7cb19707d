import toMoneyFormat, {
  getNumberWithComasEsacpe,
  bytesToMb,
  getNumberWithCommas,
  makeNumberFormat,
  calCulatePerCentEEUsage,
} from './toMoneyFormat';

import { BYTES_TO_MB_DIVIDER } from './constants';

describe('toMoneyFormat', () => {
  test('should output withoout currrency', () => {
    expect(toMoneyFormat(2500)).toBe('2,500.00');
  });

  test('should output with currency (GBP)', () => {
    expect(toMoneyFormat(2500, 'GBP')).toBe('2,500.00, GBP');
  });

  test('should output with no number amount return NaN', () => {
    expect(toMoneyFormat(Number('not a number'))).toBe('0.00');
  });

  it('should return "0.00" when input is NaN', () => {
    expect(toMoneyFormat(NaN)).toBe('0.00');
  });

  it('should format numbers without currency correctly', () => {
    expect(toMoneyFormat(123456789)).toBe('123,456,789.00');
    expect(toMoneyFormat('987654321')).toBe('987,654,321.00');
    expect(toMoneyFormat(123)).toBe('123.00');
  });

  it('should format numbers with currency correctly', () => {
    expect(toMoneyFormat(123456789, 'USD')).toBe('123,456,789.00, USD');
    expect(toMoneyFormat('987654321', 'EUR')).toBe('987,654,321.00, EUR');
    expect(toMoneyFormat(123, 'GBP')).toBe('123.00, GBP');
  });

  it('should handle specific scenarios', () => {
    expect(toMoneyFormat(1234.56, 'CAD')).toBe('1,234.56, CAD');
  });
});
describe('getNumberWithComasEsacpe', () => {
  it('should handle undefined or null input', () => {
    expect(getNumberWithComasEsacpe(undefined)).toBe('0');
    expect(getNumberWithComasEsacpe(null)).toBe('0');
  });

  it('should handle valid numbers without decimal places', () => {
    expect(getNumberWithComasEsacpe(1000)).toBe('1,000.00');
    expect(getNumberWithComasEsacpe('2000', 4)).toBe('2,000.0000');
  });

  it('should handle valid numbers with specified decimal places', () => {
    expect(getNumberWithComasEsacpe(1234.5678, 2)).toBe('1,234.57');
    expect(getNumberWithComasEsacpe('9876.54321', 3)).toBe('9,876.543');
  });

  it('should handle non-numeric input', () => {
    expect(getNumberWithComasEsacpe('abc')).toBe('0.00');
    expect(getNumberWithComasEsacpe('')).toBe('0.00');
  });

  it('should handle negative numbers', () => {
    expect(getNumberWithComasEsacpe(-123456.789)).toBe('-123,456.79');
    expect(getNumberWithComasEsacpe('-987654.321', 3)).toBe('-987,654.321');
  });
});

describe('makeNumberFormat', () => {
  it('should return "0" when input is NaN', () => {
    expect(makeNumberFormat(NaN)).toBe('0');
  });

  it('should format numbers with commas correctly', () => {
    expect(makeNumberFormat(123456789)).toBe('123,456,789');
    expect(makeNumberFormat(987654321)).toBe('987,654,321');
    expect(makeNumberFormat(123)).toBe('123');
  });

  it('should handle specific scenarios', () => {
    expect(makeNumberFormat(1234.56)).toBe('1,234.56');
  });
});

describe('bytesToMb', () => {
  it('should return "0.00" when input is 0', () => {
    expect(bytesToMb(0)).toBe('0.00');
  });

  it('should correctly convert bytes to megabytes with two decimal places', () => {
    expect(bytesToMb(BYTES_TO_MB_DIVIDER)).toBe('1.00');
    expect(bytesToMb(BYTES_TO_MB_DIVIDER * 2)).toBe('2.00');
    expect(bytesToMb(BYTES_TO_MB_DIVIDER * 1.5)).toBe('1.50');
  });

  it('should handle specific scenarios', () => {
    expect(bytesToMb(BYTES_TO_MB_DIVIDER / 2)).toBe('0.50');
  });
});

describe('getNumberWithCommas', () => {
  it('should return "0" when input is undefined, null, or empty string', () => {
    expect(getNumberWithCommas(undefined)).toBe('0');
    expect(getNumberWithCommas(null)).toBe('0');
    expect(getNumberWithCommas('')).toBe('0');
  });

  it('should format numbers with commas correctly', () => {
    expect(getNumberWithCommas(123456789)).toBe('123,456,789');
    expect(getNumberWithCommas('987654321')).toBe('987,654,321');
    expect(getNumberWithCommas('123')).toBe('123');
  });

  it('should handle specific scenarios', () => {
    expect(getNumberWithCommas(1234.56)).toBe('1,234.56');
  });
});

describe('calCulatePerCentEEUsage', () => {
  it('should return "0.00" when either valueParam or usage is 0', () => {
    expect(calCulatePerCentEEUsage(0, 10)).toBe('0.00');
    expect(calCulatePerCentEEUsage(5, 0)).toBe('0.00');
  });

  it('should calculate percentage correctly when both valueParam and usage are non-zero', () => {
    expect(calCulatePerCentEEUsage(50, 100)).toBe('50.00');
    expect(calCulatePerCentEEUsage(75, 150)).toBe('50.00');
  });

  it('should handle specific scenarios', () => {
    expect(calCulatePerCentEEUsage(0.5, 1)).toBe('50.00');
  });
});
