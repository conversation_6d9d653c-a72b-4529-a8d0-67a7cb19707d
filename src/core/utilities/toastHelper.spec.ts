import { toast } from 'react-toastify';
import { toastError, toastSuccess, toastInfo } from './toastHelper';

jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

describe('toastHelper', () => {
  test('toastSuccess', () => {
    toastSuccess('success');
    expect(toast.success).toHaveBeenCalled();
  });

  test('toastError', () => {
    toastError('error');
    expect(toast.error).toHaveBeenCalled();
  });

  test('toastInfo', () => {
    toastInfo('info');
    expect(toast.info).toHaveBeenCalled();
  });
});
