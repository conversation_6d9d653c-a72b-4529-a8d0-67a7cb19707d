import zerosToLetterFormatter from './zerosToLetterFormatter';

describe('zerosToLetterFormatter', () => {
  test('should output 100k', () => {
    expect(zerosToLetterFormatter(100000)).toBe('100k');
  });

  test('should output 1m', () => {
    expect(zerosToLetterFormatter(1000000)).toBe('1m');
  });

  test('should output plain number', () => {
    expect(zerosToLetterFormatter(10)).toBe(10);
  });
});
