import { descending, sortFieldNames } from 'features/SimManagement/SimManagementClient/constants';
import { TOASTS } from 'features/constants';
import { toastError, toastSuccess } from './toastHelper';

export const getSearchSortModel = (getParamsFromUrl, setIsLoading) => {
  const formattedDate = new Date()
    .toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  const {
    field, sort, search,
  } = getParamsFromUrl();
  setIsLoading(true);

  let ordering = field ? sortFieldNames[field] : null;
  if (ordering && sort === descending) {
    ordering = `-${ordering}`;
  }
  return { ordering, search, formattedDate };
};

export const onExportCSVFileHandle = (csvData, fileName, exportCSVFile, setIsLoading) => {
  try {
    exportCSVFile(csvData, fileName);
    toastSuccess(TOASTS.EXPORT_SIM_MANAGEMENT_CLIENT_SUCCESS);
  } catch (e) {
    toastError(TOASTS.EXPORT_SIM_MANAGEMENT_CLIENT_ERROR);
  } finally {
    setIsLoading(false);
  }
};
