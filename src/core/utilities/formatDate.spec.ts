import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

import {
  formatDate, formatDateWithHours, convertHourMinuteSecond,
  lastDayOfSelectedMonth, currentYearMonthDay,
} from './formatDate';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault('Europe/Berlin');

describe('date formatting', () => {
  describe('formatDateWithHours', () => {
    test('should return empty string', () => {
      expect(formatDateWithHours('')).toBe('');
      expect(formatDateWithHours(undefined)).toBe('');
    });

    test('should output correct date format', () => {
      expect(formatDateWithHours('2023-01-10T05:18:01')).toBe('10-01-2023 05:18:01');
    });
  });

  describe('formatDate', () => {
    test('should return empty string', () => {
      expect(formatDate('')).toBe('');
      expect(formatDate(undefined)).toBe('');
    });

    test('should output correct date format', () => {
      expect(formatDate('2023-01-10T05:18:01')).toBe('10-01-2023');
    });
  });
  describe('currentYearMonthDay', () => {
    it('should return the first day of the current month in YYYY-MM-DD format', () => {
      const result = currentYearMonthDay();
      const expected = dayjs(new Date()).startOf('month').format('YYYY-MM-DD');
      expect(result).toBe(expected);
    });
  });

  describe('lastDayOfSelectedMonth', () => {
    it('should return the last day of the selected month in YYYY-MM-DD format', () => {
      const result = lastDayOfSelectedMonth('2023-04-15');
      expect(result).toBe('2023-04-30');
    });
  });

  describe('convertHourMinuteSecond', () => {
    it('should convert seconds to formatted time', () => {
      expect(convertHourMinuteSecond(3665)).toBe('01:01:05');
      expect(convertHourMinuteSecond(7200)).toBe('02:00:00');
      expect(convertHourMinuteSecond(250)).toBe('00:04:10');
    });
  });
});
