import getCurrentThemeColors from 'core/utilities/getCurrentThemeColors';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

 
const getButtonColors = (theme: any, btnType: 'primary' | 'secondary' | 'light' | 'superLight') => {
 
  const colors: any = {
    primary: {
      bg: {
        default: theme.palette.primary.main,
        hover: getCurrentThemeColors(theme.palette.primary.main)[400],
        disabled: styles.lightColor300,
      },
      color: {
        default: theme.palette.common.white,
        hover: theme.palette.common.white,
        disabled: theme.palette.common.white,
      },
    },
    secondary: {
      bg: {
        default: getCurrentThemeColors(theme.palette.secondary.main)[300],
        hover: getCurrentThemeColors(theme.palette.secondary.main)[400],
        disabled: styles.lightColor300,
      },
      color: {
        default: theme.palette.common.white,
        hover: theme.palette.common.white,
        disabled: theme.palette.common.white,
      },
    },
    light: {
      bg: {
        default: getCurrentThemeColors(theme.palette.primary.main)[50],
        hover: getCurrentThemeColors(theme.palette.primary.main)[100],
        disabled: styles.lightColor300,
      },
      color: {
        default: getCurrentThemeColors(theme.palette.primary.main)[500],
        hover: getCurrentThemeColors(theme.palette.primary.main)[400],
        disabled: theme.palette.common.white,
      },
    },
    superLight: {
      bg: {
        default: theme.palette.common.white,
        hover: getCurrentThemeColors(theme.palette.primary.main)[50],
        disabled: theme.palette.common.white,
      },
      color: {
        default: getCurrentThemeColors(theme.palette.primary.dark)[500],
        hover: getCurrentThemeColors(theme.palette.primary.main)[500],
        disabled: styles.lightColor300,
      },
    },
  };

  return colors[btnType];
};

export default getButtonColors;
