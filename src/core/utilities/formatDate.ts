import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault('Europe/Berlin');

function padTo2Digits(num) {
  return num.toString().padStart(2, '0');
}

export const formatDate = (initialDate?: string | Date): string => {
  if (!initialDate) return '';

  const date = new Date(initialDate);

  const day = padTo2Digits(date.getDate());
  const month = padTo2Digits(date.getMonth() + 1);
  const year = date.getFullYear();

  return `${day}-${month}-${year}`;
};

export const formateDateWithTimeZone = (serverTime) => {
  const utcDateTime = dayjs.utc(serverTime);

  // Convert the UTC datetime to the Asia/Kolkata time zone
  const localDateTime = utcDateTime.tz(Intl.DateTimeFormat().resolvedOptions().timeZone);

  // Format the datetime as 'DD-MM-YYYY HH:mm:ss'
  const formattedDateTime = localDateTime.format('DD-MM-YYYY HH:mm:ss');
  return formattedDateTime;
};

export const formatDateWithHours = (initialDate?: string | Date) => {
  if (!initialDate) return '';

  const formattedDate = formatDate(initialDate);

  const date = new Date(initialDate);

  const hours = padTo2Digits(date.getHours());
  const minutes = padTo2Digits(date.getMinutes());
  const seconds = padTo2Digits(date.getSeconds());

  return (
    `${formattedDate} ${hours}:${minutes}:${seconds}`
  );
};

export const currentYearMonth = dayjs(new Date()).format('YYYY-MM');

export const startMonthAugest = () => {
  const currentDate = new Date();
  if (currentDate < new Date('08-01-2023')) {
    return dayjs(new Date('08-01-2023')).format('YYYY-MM');
  }
  return currentYearMonth;
};
export const currentYearMonthDay = () => {
  const date = dayjs(new Date());
  const firstDayOfMonth = date.startOf('month');
  const formattedDate = firstDayOfMonth.format('YYYY-MM-DD');
  return formattedDate;
};
export const lastDayOfSelectedMonth = (param) => {
  const date = dayjs(param);
  const lastDayOfMonth = date.endOf('month');
  const formattedDate = lastDayOfMonth.format('YYYY-MM-DD');
  return formattedDate;
};
export const lastDayOfMonth = dayjs().endOf('month').format('MM-DD-YYYY');
export const startWithAugust = dayjs('08-01-2023').startOf('month').format('MM-DD-YYYY');

export const formatDateHoursWithMinute = (dateParam?: Date | string) => dayjs(dateParam).format('YYYY-MM-DD hh:mm a');

export const convertHourMinuteSecond = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  const formattedTime = `${(`0${hours}`).slice(-2)}:${(`0${minutes}`).slice(-2)}:${(`0${remainingSeconds}`).slice(-2)}`;
  return formattedTime;
};


export const getRelativeTime = (pastTime: string | Date, message: string = '') => {
  const now = dayjs.utc();
  const past = typeof pastTime === 'string'
    ? dayjs.utc(pastTime.replace(' ', 'T').substring(0, 23)) // fix format
    : dayjs.utc(pastTime);

  const diffMinutes = now.diff(past, 'minute');
  const diffHours = now.diff(past, 'hour');
  const diffDays = now.diff(past, 'day');
  const diffMonths = now.diff(past, 'month');

  if (diffMinutes < 1) return 'Just now';
  if (message) {
    if (diffMinutes < 60) return `${message} ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${message} ${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffDays < 30) return `${message} ${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    return `${message} ${diffMonths} month${diffMonths > 1 ? 's' : ''} ago`;
  } else {
    if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffDays < 30) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    return `${diffMonths} month${diffMonths > 1 ? 's' : ''} ago`;
  }
};

export default formatDate;
