import React from 'react';
import { waitFor } from '@testing-library/react';
import PrivateRotes from 'PrivateRotes';
import permissions from 'hooks/permissions';
import testRender from 'core/utilities/testUtils';

jest.mock('AppContextProvider', () => ({
  useAppContext: () => ({
    access: permissions.result,
  }),
}));
const Test = () => <span>hellow</span>;
describe('PrivateRotes', () => {
  it('renders the component when authorization is granted', () => {
    // Mock the necessary values for authorization
    const mockItem = 'someValidItem';
    const repository = ['SIMManagement'];
    const { container } = testRender(
      <PrivateRotes
        permission={[mockItem]}
        repoName={repository}
      >
        <Test />

      </PrivateRotes>,
    );

    expect(container).toBeInTheDocument();
    // You can add more assertions based on your component's behavior.
  });

  it('redirects to LogoutComponent when authorization is not granted', () => {
    // Mock the necessary values for failed authorization
    const mockItem = 'someInvalidItem';
    const repository = ['SIMManagement'];
    const { container } = testRender(
      <PrivateRotes
        permission={[mockItem]}
        repoName={repository}
      >
        <Test />

      </PrivateRotes>,
    );

    // Verify that the LogoutComponent is rendered when authorization is not granted.
    waitFor(() => {
      expect(container).toHaveTextContent('LogoutComponent');
    });
  });

  it('renders the component with default props when props are not provided', () => {
    const repository = ['SIMManagement'];
    const { container } = testRender(
      <PrivateRotes
        permission={['/']}
        repoName={repository}
      >
        <Test />

      </PrivateRotes>,
    );

    // Verify that the component is rendered with default props.
    expect(container).toBeInTheDocument();
    // You can add more assertions based on your component's default behavior.
  });

  // Add more test cases as needed to cover additional scenarios and edge cases.
});
