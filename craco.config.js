const sassResourcesLoader = require('craco-sass-resources-loader');
const ModuleFederationPlugin = require('webpack/lib/container/ModuleFederationPlugin');
const { whenDev, whenProd } = require('@craco/craco');
const { dependencies } = require('./package.json');

module.exports = {
  typescript: {
    enableTypeChecking: true,
  },
  plugins: [
    {
      plugin: sassResourcesLoader,
      options: {
        resources: [
          'node_modules/@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss',
          'src/assets/styles/variables.scss',
        ],
      },
    },
  ],
  webpack: {
    configure: (webpackConfig) => {

      webpackConfig.plugins = [
        ...webpackConfig.plugins,
        new ModuleFederationPlugin({
          name: 'spogSimManagement',
          filename: 'remoteEntry.js?v=1.0.24',
          exposes: {
            './spogSimManagement': './src/App',
          },
          shared: {
            ...dependencies,
            react: {
              eager: true,
              singleton: true,
              requiredVersion: dependencies.react,
            },
            'react-router-dom': {
              eager: true,
              requiredVersion: dependencies['react-router-dom'],
            },
            'react-dom': {
              singleton: true,
              eager: true,
              requiredVersion: dependencies['react-dom'],
            },
            'react-cookie': {
              singleton: true,
              eager: true,
              requiredVersion: dependencies['react-cookie'],
            },
            axios: {
              singleton: true,
              eager: true,
              requiredVersion: dependencies.axios,
            },
            'react-toastify': {
              singleton: true,
              eager: true,
              requiredVersion: dependencies['react-toastify'],
            },
            'react-country-flag': {
              singleton: true,
              eager: true,
              requiredVersion: dependencies['react-country-flag'],
            },
            formik: {
              singleton: true,
              eager: true,
              requiredVersion: dependencies.formik,
            },
            yup: {
              singleton: true,
              eager: true,
              requiredVersion: dependencies.yup,
            },
            '@mui/icons-material': {
              eager: true,
              singleton: true,
              requiredVersion: dependencies['@mui/icons-material'],
            },
            '@mui/lab': {
              eager: true,
              singleton: true,
              requiredVersion: dependencies['@mui/lab'],
            },
            '@mui/material': {
              eager: true,
              singleton: true,
              requiredVersion: dependencies['@mui/material'],
            },
            '@mui/styles': {
              eager: true,
              singleton: true,
              requiredVersion: dependencies['@mui/styles'],
            },
            'socket.io-client': {
              eager: true,
              singleton: true,
              requiredVersion: dependencies['socket.io-client'],
            },
            recharts: {
              eager: true,
              singleton: true,
              requiredVersion: dependencies.recharts,
            },
            'react-dropzone': { eager: true },
            dayjs: { eager: true },
            lodash: { eager: true },
          },
        }),
      ];


      // webpackConfig.output.publicPath = 'auto';

      webpackConfig.output = {
        ...webpackConfig.output,
        // for start as container part
        // ...whenDev(() => ({ publicPath: 'auto', clean: true })),
        // for start as independent application
        ...whenDev(() => ({ publicPath: '/', clean: true })),
        ...whenProd(() => ({ publicPath: 'auto', clean: true })),
      };

      return webpackConfig;
    },
  },
};
